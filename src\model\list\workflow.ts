import type * as dto from "../../def/index"

import { WorkflowApi } from "./workflow-api"
import type { AllFilters } from "./workflow-api"
export class Workflow {
    private api: WorkflowApi
    constructor(
        private config: {
            model_name: string
            ids: number[]
            tabName: string
            allFilters: AllFilters
        }
    ) {
        this.api = new WorkflowApi(config.model_name)
    }
    /**
     * 查询工作流元数据
     * @param params
     */
    public query(params: dto.WorkflowTypes.getProcessInfoParams) {
        return this.api.getProcessInfo(params)
    }
    /**
     * 创建工作流
     * @param params
     */
    public createWorkflow(params: dto.WorkflowTypes.createWorkflow) {
        return this.api.createWorkflow({
            tabName: this.config.tabName,
            ids: this.config.ids,
            allFilters: this.config.allFilters,
            ...params,
        })
    }
    /**
     * 更新工作流
     * @param params
     */
    public updateWorkflow(params: dto.WorkflowTypes.updateWorkflow) {
        return this.api.updateWorkflow({
            tabName: this.config.tabName,
            ids: this.config.ids,
            ...params,
        })
    }
}
