export function copyTextToClipboard(text: string) {
    const m = text.includes("\n")
    const input = m
        ? document.createElement("textarea")
        : document.createElement("input")
    input.setAttribute("readonly", "readonly")
    m ? (input.value = text) : input.setAttribute("value", text)
    document.body.appendChild(input)
    input.select()
    const ret = document.execCommand("copy")
    document.body.removeChild(input)
    return ret
}
