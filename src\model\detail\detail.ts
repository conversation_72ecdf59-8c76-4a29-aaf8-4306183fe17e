import { AnonymousModel } from "../../core/anonymous-api"
import { events } from "../../core/events"
import { exportToExcelForDetail } from "../../core/tools/sse"
import type * as dto from "../../def/index"

import { DetailApi, DetailApi2 } from "./api"

class BaseDetailModel extends AnonymousModel {
    protected params: dto.DetailTypes.constructorType
    protected callbackOnChange?: dto.SSE.callBackOfModelUpdated
    protected logQueryParams = {
        showLog: false,
        pageIndex: 0,
    }

    protected metaModelName = ""

    protected detailsParams: Array<dto.DetailTypes.detailsQueryParam> = []

    /**
     * 在query之前调用
     * 使query时返回日志信息
     * @param pageIndex  返回log的第几页
     */
    public addLog(pageIndex: number) {
        this.logQueryParams.showLog = true
        this.logQueryParams.pageIndex = pageIndex
        return this
    }

    /**
     * 在query之前调用
     * 取消在query中返回日志信息
     */
    public removeLog() {
        this.logQueryParams.showLog = false
        return this
    }

    /**
     * 在query之前调用
     * 使query返回指定的子列表
     * @param name
     * @param pageIndex
     * @param pageSize
     */
    public addDetail(name: string, pageIndex: number, pageSize: number) {
        this.detailsParams.push({
            name,
            itemIndex: (pageIndex - 1) * pageSize,
            itemSize: pageSize,
        })
        return this
    }
    /**
     * 在query之前调用
     * 取消query返回任何子列表
     */
    public clearAddDetail() {
        this.detailsParams = []
        return this
    }

    private onTransportMessage = (msg: dto.SSE.msg) => {
        const models = msg.dataUpdates.filter(
            (x) => x.model === this.metaModelName || this.params.model_name
        )
        if (models.length === 0) return
        const ifNeedRefresh = models.every((model) => {
            return model.selectedList.every(
                (id) => String(this.params.keyValue) === id
            )
        })
        if (ifNeedRefresh) {
            this.callbackOnChange && this.callbackOnChange(msg.createByMyself)
        }
    }

    /**
     * 注册当前模型的数据变化时的回掉
     * @param CallBackOfModelUpdated
     */
    public registerOnChange(cb: dto.SSE.callBackOfModelUpdated) {
        this.callbackOnChange = cb
        return events.addTransportMessageListener(this.onTransportMessage)
    }
}

export class Detail<
    T extends dto.ListRow = dto.ListRow,
    LogRow extends dto.DetailTypes.GetLogsRequestRow = dto.DetailTypes.GetLogsRequestRow
> extends BaseDetailModel {
    protected api: DetailApi<T, LogRow>

    constructor(params: dto.DetailTypes.constructorType) {
        super()
        this.params = params
        this.api = new DetailApi<T, LogRow>(
            this.params.model_name,
            this.params.keyValue,
            this.params.detailName
        )
    }

    public async smartQuery() {
        const queryParams: dto.DetailTypes.getDetailAPIParams = {}
        if (this.logQueryParams.showLog) {
            queryParams.log = this.logQueryParams
        }
        if (this.detailsParams.length > 0) {
            queryParams.details = this.detailsParams
        }
        const data = await this.api.smartQuery(queryParams)
        const detailName = data.meta.name
        this.api = new DetailApi<T, LogRow>(
            this.params.model_name,
            this.params.keyValue,
            detailName
        )
        return Object.freeze(data)
    }
    /**
     *
     * 查询详情页面的数据
     * @returns  Promise<getDetailRequestResult>;
     */
    public async query() {
        const queryParams: dto.DetailTypes.getDetailAPIParams = {}
        if (this.logQueryParams.showLog) {
            queryParams.log = this.logQueryParams
        }
        if (this.detailsParams.length > 0) {
            queryParams.details = this.detailsParams
        }
        const data = await this.api.getDetail(queryParams)
        this.metaModelName = data.meta.modelName
        return Object.freeze(data)
    }

    /**
     *
     * 查询详情页面的数据 自定key
     * @returns  Promise<getDetailRequestResult>;
     */
    public async queryKeyCustom() {
        const queryParams: dto.DetailTypes.getDetailAPIParams = {}
        if (this.logQueryParams.showLog) {
            queryParams.log = this.logQueryParams
        }
        if (this.detailsParams.length > 0) {
            queryParams.details = this.detailsParams
        }
        const data = await this.api.getDetailKeyCustom(queryParams)
        this.metaModelName = data.meta.modelName
        return Object.freeze(data)
    }
    /**
     * 得到详情页面的logs
     * @param pageIndex
     * @returns Promise<DetailPageGetLogsRequest>;
     */
    public async getLogs(pageIndex: number) {
        const data = await this.api.getLogs(pageIndex)
        return Object.freeze(data)
    }

    /**
     * 获取默认导出url
     */
    public getDefaultTemplateUrl() {
        return this.api.getDefaultTemplateUrl()
    }
    /**
     * 导出列表为excel
     * @param ListExportExcelParams
     * @returns 返回一个开始导出的方法startDownload, 新方法有一个参数用来监听导出进度
     * 返回的方法是一个Promise，resolve时得到exlce下载链接
     */
    public exportToExcel(params: dto.DetailTypes.createExportUrl = {}) {
        return exportToExcelForDetail(
            this.api.createExportUrl({
                ...params,
                name: this.params.detailName,
            })
        )
    }
}

export class Detail2<
    LogRow extends dto.DetailTypes.GetLogsRequestRow = dto.DetailTypes.GetLogsRequestRow
> extends BaseDetailModel {
    protected api: DetailApi2<LogRow>

    constructor(params: dto.DetailTypes.constructorType) {
        super()
        this.params = params
        this.api = new DetailApi2<LogRow>(
            this.params.model_name,
            this.params.keyValue,
            this.params.detailName
        )
    }

    public async smartQuery() {
        const queryParams: dto.DetailTypes.getDetailAPIParams = {}
        if (this.logQueryParams.showLog) {
            queryParams.log = this.logQueryParams
        }
        if (this.detailsParams.length > 0) {
            queryParams.details = this.detailsParams
        }
        const data = await this.api.smartQuery(queryParams)
        const detailName = data.meta.name
        this.api = new DetailApi2<LogRow>(
            this.params.model_name,
            this.params.keyValue,
            detailName
        )
        return Object.freeze(data)
    }
    /**
     *
     * 查询详情页面的数据
     * @returns  Promise<getDetailRequestResult>;
     */
    public async query() {
        const queryParams: dto.DetailTypes.getDetailAPIParams = {}
        if (this.logQueryParams.showLog) {
            queryParams.log = this.logQueryParams
        }
        if (this.detailsParams.length > 0) {
            queryParams.details = this.detailsParams
        }
        const data = await this.api.getDetail(queryParams)
        this.metaModelName = data.meta.modelName
        return Object.freeze(data)
    }

    /**
     *
     * 查询详情页面的数据 自定key
     * @returns  Promise<getDetailRequestResult>;
     */
    public async queryKeyCustom() {
        const queryParams: dto.DetailTypes.getDetailAPIParams = {}
        if (this.logQueryParams.showLog) {
            queryParams.log = this.logQueryParams
        }
        if (this.detailsParams.length > 0) {
            queryParams.details = this.detailsParams
        }
        const data = await this.api.getDetailKeyCustom(queryParams)
        this.metaModelName = data.meta.modelName
        return Object.freeze(data)
    }
    /**
     * 得到详情页面的logs
     * @param pageIndex
     * @returns Promise<DetailPageGetLogsRequest>;
     */
    public async getLogs(pageIndex: number) {
        const data = await this.api.getLogs(pageIndex)
        return Object.freeze(data)
    }

    /**
     * 获取默认导出url
     */
    public getDefaultTemplateUrl() {
        return this.api.getDefaultTemplateUrl()
    }
    /**
     * 导出列表为excel
     * @param ListExportExcelParams
     * @returns 返回一个开始导出的方法startDownload, 新方法有一个参数用来监听导出进度
     * 返回的方法是一个Promise，resolve时得到exlce下载链接
     */
    public exportToExcel(params: dto.DetailTypes.createExportUrl = {}) {
        return exportToExcelForDetail(
            this.api.createExportUrl({
                ...params,
                name: this.params.detailName,
            })
        )
    }
}
