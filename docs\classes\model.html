<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Model | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="model.html">Model</a>
				</li>
			</ul>
			<h1>Class Model</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">Model</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="model.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section tsd-is-private tsd-is-private-protected">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="model.html#api" class="tsd-kind-icon">api</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="model.html#name" class="tsd-kind-icon">name</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="model.html#action" class="tsd-kind-icon">action</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="model.html#callbackonchange" class="tsd-kind-icon">callback<wbr>OnChange</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="model.html#chat" class="tsd-kind-icon">chat</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="model.html#detail" class="tsd-kind-icon">detail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="model.html#getforwardurl" class="tsd-kind-icon">get<wbr>ForwardURL</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="model.html#gethistoryversion" class="tsd-kind-icon">get<wbr>History<wbr>Version</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="model.html#getremarklist" class="tsd-kind-icon">get<wbr>Remark<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="model.html#getrequest" class="tsd-kind-icon">get<wbr>Request</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="model.html#getsection" class="tsd-kind-icon">get<wbr>Section</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="model.html#gettimebyidentifier" class="tsd-kind-icon">get<wbr>Time<wbr>ByIdentifier</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="model.html#groupsearch" class="tsd-kind-icon">group<wbr>Search</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="model.html#intentsearch" class="tsd-kind-icon">intent<wbr>Search</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="model.html#jointsearch" class="tsd-kind-icon">joint<wbr>Search</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="model.html#list" class="tsd-kind-icon">list</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="model.html#mappingfetch" class="tsd-kind-icon">mapping<wbr>Fetch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="model.html#ontransportmessage" class="tsd-kind-icon">on<wbr>Transport<wbr>Message</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="model.html#postmodelrequest" class="tsd-kind-icon">post<wbr>Model<wbr>Request</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="model.html#rawlist" class="tsd-kind-icon">raw<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="model.html#registeronchange" class="tsd-kind-icon">register<wbr>OnChange</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="model.html#supercascaderfetch" class="tsd-kind-icon">super<wbr>Cascader<wbr>Fetch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="model.html#supercascadersearch" class="tsd-kind-icon">super<wbr>Cascader<wbr>Search</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="model.html#toolbalenhancervalid" class="tsd-kind-icon">toolbal<wbr>Enhancer<wbr>Valid</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new <wbr>Model<span class="tsd-signature-symbol">(</span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="model.html" class="tsd-signature-type">Model</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:14</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>name: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="model.html" class="tsd-signature-type">Model</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="api" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> api</h3>
					<div class="tsd-signature tsd-kind-icon">api<span class="tsd-signature-symbol">:</span> <a href="modelapi.html" class="tsd-signature-type">ModelApi</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/model/model.ts:14</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="name" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> name</h3>
					<div class="tsd-signature tsd-kind-icon">name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/model/model.ts:15</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="action" class="tsd-anchor"></a>
					<h3>action</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">action<span class="tsd-signature-symbol">(</span>actionName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="action.html" class="tsd-signature-type">Action</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:82</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>得到模型下的action</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>actionName: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="action.html" class="tsd-signature-type">Action</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="callbackonchange" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> callback<wbr>OnChange</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">callback<wbr>OnChange<span class="tsd-signature-symbol">(</span>msg<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.msg</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:19</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>msg: <span class="tsd-signature-type">dto.SSE.msg</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="chat" class="tsd-anchor"></a>
					<h3>chat</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">chat<span class="tsd-signature-symbol">(</span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span>, orgID<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="chat.html" class="tsd-signature-type">Chat</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:75</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>id: 详情的keyvalue
									获得一个会话类</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>id: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>orgID: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="chat.html" class="tsd-signature-type">Chat</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="detail" class="tsd-anchor"></a>
					<h3>detail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">detail&lt;RowType, LogRow&gt;<span class="tsd-signature-symbol">(</span>keyValue<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span>, detailName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="detail.html" class="tsd-signature-type">Detail</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">LogRow</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:61</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>得到模型对应的detail类型页面</p>
								</div>
							</div>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>RowType<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListRow</span> = <span class="tsd-signature-type">dto.ListRow</span></h4>
								</li>
								<li>
									<h4>LogRow<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.DetailTypes.GetLogsRequestRow</span> = <span class="tsd-signature-type">dto.DetailTypes.GetLogsRequestRow</span></h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>keyValue: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> detailName: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="detail.html" class="tsd-signature-type">Detail</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">LogRow</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getforwardurl" class="tsd-anchor"></a>
					<h3>get<wbr>ForwardURL</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>ForwardURL<span class="tsd-signature-symbol">(</span>forward<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#getforwardurlapiresult" class="tsd-signature-type">getForwardURLApiResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:114</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>forward: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> params: <span class="tsd-signature-type">unknown</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#getforwardurlapiresult" class="tsd-signature-type">getForwardURLApiResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="gethistoryversion" class="tsd-anchor"></a>
					<h3>get<wbr>History<wbr>Version</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>History<wbr>Version<span class="tsd-signature-symbol">(</span>objectId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span>, field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#historyversion" class="tsd-signature-type">HistoryVersion</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:156</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>objectId: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>field: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#historyversion" class="tsd-signature-type">HistoryVersion</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getremarklist" class="tsd-anchor"></a>
					<h3>get<wbr>Remark<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Remark<wbr>List<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Parameters</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ModelApiType</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">"getRemarkList"</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">0</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>pageIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>pageSize<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>rows<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:137</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">Parameters</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ModelApiType</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">"getRemarkList"</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">0</span><span class="tsd-signature-symbol">]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>pageIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>pageSize<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>rows<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="getrequest" class="tsd-anchor"></a>
					<h3>get<wbr>Request</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Request&lt;T&gt;<span class="tsd-signature-symbol">(</span>apiName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, query<span class="tsd-signature-symbol">: </span><a href="../globals.html#queryobject" class="tsd-signature-type">QueryObject</a><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:118</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>apiName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>query: <a href="../globals.html#queryobject" class="tsd-signature-type">QueryObject</a><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getsection" class="tsd-anchor"></a>
					<h3>get<wbr>Section</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Section<span class="tsd-signature-symbol">(</span>sectionName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/detailtypes.html#section" class="tsd-signature-type">section</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:172</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>sectionName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/detailtypes.html#section" class="tsd-signature-type">section</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="gettimebyidentifier" class="tsd-anchor"></a>
					<h3>get<wbr>Time<wbr>ByIdentifier</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Time<wbr>ByIdentifier<span class="tsd-signature-symbol">(</span>startIdentifier<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, endIdentifier<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:130</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>用户时间filter的自定义按钮。
									根据时间的标识符返回符合当下的具体时间</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>startIdentifier: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>endIdentifier: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="groupsearch" class="tsd-anchor"></a>
					<h3>group<wbr>Search</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">group<wbr>Search<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ModelTypes.groupSearchApiParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#groupsearchapiresult" class="tsd-signature-type">groupSearchApiResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:110</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ModelTypes.groupSearchApiParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#groupsearchapiresult" class="tsd-signature-type">groupSearchApiResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="intentsearch" class="tsd-anchor"></a>
					<h3>intent<wbr>Search</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">intent<wbr>Search<span class="tsd-signature-symbol">(</span>intentName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>form_params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#rowintent" class="tsd-signature-type">RowIntent</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:103</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>intentName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>params: <span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>form_params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5>action<wbr>Id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5>form_<wbr>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#rowintent" class="tsd-signature-type">RowIntent</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="jointsearch" class="tsd-anchor"></a>
					<h3>joint<wbr>Search</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">joint<wbr>Search<span class="tsd-signature-symbol">(</span>joint_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="jointsearchmanager.html" class="tsd-signature-type">JointSearchManager</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:99</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>joint_name: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="jointsearchmanager.html" class="tsd-signature-type">JointSearchManager</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="list" class="tsd-anchor"></a>
					<h3>list</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">list&lt;RowType&gt;<span class="tsd-signature-symbol">(</span>listName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="listeasy.html" class="tsd-signature-type">ListEasy</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:47</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>得到模型对应的list类型页面</p>
								</div>
							</div>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>RowType<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListRow</span> = <span class="tsd-signature-type">dto.ListRow</span></h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> listName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="listeasy.html" class="tsd-signature-type">ListEasy</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>list</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="mappingfetch" class="tsd-anchor"></a>
					<h3>mapping<wbr>Fetch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">mapping<wbr>Fetch<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ModelTypes.mappingFetchAPIParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:95</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>用于级联懒加载</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ModelTypes.mappingFetchAPIParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="ontransportmessage" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> on<wbr>Transport<wbr>Message</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">on<wbr>Transport<wbr>Message<span class="tsd-signature-symbol">(</span>msg<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.msg</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:150</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>msg: <span class="tsd-signature-type">dto.SSE.msg</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="postmodelrequest" class="tsd-anchor"></a>
					<h3>post<wbr>Model<wbr>Request</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">post<wbr>Model<wbr>Request&lt;T&gt;<span class="tsd-signature-symbol">(</span>apiName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">FormData</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:122</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>apiName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>params: <span class="tsd-signature-type">FormData</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="rawlist" class="tsd-anchor"></a>
					<h3>raw<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">raw<wbr>List<span class="tsd-signature-symbol">(</span>listName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="listhard.html" class="tsd-signature-type">ListHard</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:33</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>给report项目使用的List类。</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> listName: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="listhard.html" class="tsd-signature-type">ListHard</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="registeronchange" class="tsd-anchor"></a>
					<h3>register<wbr>OnChange</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">register<wbr>OnChange<span class="tsd-signature-symbol">(</span>cb<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.callBackOfModelUpdatedFullMsg</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:145</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>注册当前模型的数据变化时的回掉</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>cb: <span class="tsd-signature-type">dto.SSE.callBackOfModelUpdatedFullMsg</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="supercascaderfetch" class="tsd-anchor"></a>
					<h3>super<wbr>Cascader<wbr>Fetch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">super<wbr>Cascader<wbr>Fetch<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ModelTypes.SuperCascaderFetchApiParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#supercascaderfetchapiresult" class="tsd-signature-type">SuperCascaderFetchApiResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:160</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ModelTypes.SuperCascaderFetchApiParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#supercascaderfetchapiresult" class="tsd-signature-type">SuperCascaderFetchApiResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="supercascadersearch" class="tsd-anchor"></a>
					<h3>super<wbr>Cascader<wbr>Search</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">super<wbr>Cascader<wbr>Search<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ModelTypes.SuperCascaderSearchApiParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#supercascadersearchapiresult" class="tsd-signature-type">SuperCascaderSearchApiResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:166</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ModelTypes.SuperCascaderSearchApiParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#supercascadersearchapiresult" class="tsd-signature-type">SuperCascaderSearchApiResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="toolbalenhancervalid" class="tsd-anchor"></a>
					<h3>toolbal<wbr>Enhancer<wbr>Valid</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">toolbal<wbr>Enhancer<wbr>Valid&lt;T&gt;<span class="tsd-signature-symbol">(</span>behavior<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, jsonStr<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/model.ts:25</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>behavior: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>jsonStr: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="model.html" class="tsd-kind-icon">Model</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class">
								<a href="model.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="model.html#api" class="tsd-kind-icon">api</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="model.html#name" class="tsd-kind-icon">name</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="model.html#action" class="tsd-kind-icon">action</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="model.html#callbackonchange" class="tsd-kind-icon">callback<wbr>OnChange</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="model.html#chat" class="tsd-kind-icon">chat</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="model.html#detail" class="tsd-kind-icon">detail</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="model.html#getforwardurl" class="tsd-kind-icon">get<wbr>ForwardURL</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="model.html#gethistoryversion" class="tsd-kind-icon">get<wbr>History<wbr>Version</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="model.html#getremarklist" class="tsd-kind-icon">get<wbr>Remark<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="model.html#getrequest" class="tsd-kind-icon">get<wbr>Request</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="model.html#getsection" class="tsd-kind-icon">get<wbr>Section</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="model.html#gettimebyidentifier" class="tsd-kind-icon">get<wbr>Time<wbr>ByIdentifier</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="model.html#groupsearch" class="tsd-kind-icon">group<wbr>Search</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="model.html#intentsearch" class="tsd-kind-icon">intent<wbr>Search</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="model.html#jointsearch" class="tsd-kind-icon">joint<wbr>Search</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="model.html#list" class="tsd-kind-icon">list</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="model.html#mappingfetch" class="tsd-kind-icon">mapping<wbr>Fetch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="model.html#ontransportmessage" class="tsd-kind-icon">on<wbr>Transport<wbr>Message</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="model.html#postmodelrequest" class="tsd-kind-icon">post<wbr>Model<wbr>Request</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="model.html#rawlist" class="tsd-kind-icon">raw<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="model.html#registeronchange" class="tsd-kind-icon">register<wbr>OnChange</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="model.html#supercascaderfetch" class="tsd-kind-icon">super<wbr>Cascader<wbr>Fetch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="model.html#supercascadersearch" class="tsd-kind-icon">super<wbr>Cascader<wbr>Search</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="model.html#toolbalenhancervalid" class="tsd-kind-icon">toolbal<wbr>Enhancer<wbr>Valid</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>