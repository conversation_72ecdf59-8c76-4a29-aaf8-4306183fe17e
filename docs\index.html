<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="assets/js/search.json" data-base=".">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="globals.html">Globals</a>
				</li>
			</ul>
			<h1>uniplat-sdk</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<div class="tsd-panel tsd-typography">
				<a href="#文档" id="文档" style="color: inherit; text-decoration: none;">
					<h1><a href="./docs/index.html">文档</a></h1>
				</a>
				<a href="#使用方法详见羽雀" id="使用方法详见羽雀" style="color: inherit; text-decoration: none;">
					<h1><a href="https://teammix.yuque.com/hqdev/nnpn4g/pa12sa">使用方法详见羽雀</a></h1>
				</a>
				<p>[发布]</p>
				<p>1 修改代码后，执行 npm run build 确认没有编译错误
					2 修改 package.json version 属性，手动加一个版本号，如 0.1.134-private 修改为 0.1.135-private
					3 提交代码
				4 在 <a href="https://pre-hrmanager.hrs100.com/ssh">https://pre-hrmanager.hrs100.com/ssh</a> 找到【Uniplat Sdk Publisher】菜单，执行启用，进行远程打包，没有打包权限的联系管理员</p>
			</div>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="globals.html"><em>Globals</em></a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/actiontypes.html">Action<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/configcentertypes.html">Config<wbr>Center<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/dashboardtypes.html">Dashboard<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/detailtypes.html">Detail<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/etltypes.html">ETLTypes</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/filemodel.html">File<wbr>Model</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/globalsearchtypes.html">Global<wbr>Search<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/groovyeditortypes.html">Groovy<wbr>Editor<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/index.html">Index</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/indexsearchtypes.html">Index<wbr>Search<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/listsection.html">List<wbr>Section</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/listtypes.html">List<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/modelschemamanagertypes.html">Model<wbr>Schema<wbr>Manager<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/modeltypes.html">Model<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/modelvalidatortypes.html">Model<wbr>Validator<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/newswaggertypes.html">New<wbr>Swagger<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/oauthlogintypes.html">OAuth<wbr>Login<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/passwordboxtypes.html">Password<wbr>Box<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/pivottabletypes.html">Pivot<wbr>Table<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/roletypes.html">Role<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/sse.html">SSE</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/scenetypes.html">Scene<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/supercascader.html">Super<wbr>Cascader</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/swaggertypes.html">Swagger<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/tagmanagertypes.html">Tag<wbr>Manager<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/templatemanagertypes.html">Template<wbr>Manager<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/tools.html">Tools</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/treetypes.html">Tree<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/uiconfigtypes.html">UIConfig<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/workflowtypes.html">Workflow<wbr>Types</a>
					</li>
					<li class=" tsd-kind-namespace">
						<a href="modules/workflow2.html">workflow2</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
					<li class=" tsd-kind-enum">
						<a href="enums/dategrouptypeenum.html" class="tsd-kind-icon">Date<wbr>Group<wbr>Type<wbr>Enum</a>
					</li>
					<li class=" tsd-kind-enum">
						<a href="enums/inputtypes.html" class="tsd-kind-icon">Input<wbr>Types</a>
					</li>
					<li class=" tsd-kind-enum">
						<a href="enums/intentaction.html" class="tsd-kind-icon">Intent<wbr>Action</a>
					</li>
					<li class=" tsd-kind-enum">
						<a href="enums/intentcontainer.html" class="tsd-kind-icon">Intent<wbr>Container</a>
					</li>
					<li class=" tsd-kind-enum">
						<a href="enums/messagetype.html" class="tsd-kind-icon">Message<wbr>Type</a>
					</li>
					<li class=" tsd-kind-enum">
						<a href="enums/operationtype.html" class="tsd-kind-icon">Operation<wbr>Type</a>
					</li>
					<li class=" tsd-kind-enum">
						<a href="enums/sdklistrowtype.html" class="tsd-kind-icon">Sdk<wbr>List<wbr>Row<wbr>Type</a>
					</li>
					<li class=" tsd-kind-enum">
						<a href="enums/summarytypeenum.html" class="tsd-kind-icon">Summary<wbr>Type<wbr>Enum</a>
					</li>
					<li class=" tsd-kind-enum">
						<a href="enums/uploadtype.html" class="tsd-kind-icon">Upload<wbr>Type</a>
					</li>
					<li class=" tsd-kind-enum">
						<a href="enums/useragenttype.html" class="tsd-kind-icon">User<wbr>Agent<wbr>Type</a>
					</li>
					<li class=" tsd-kind-enum">
						<a href="enums/workflowparamstype.html" class="tsd-kind-icon">Workflow<wbr>Params<wbr>Type</a>
					</li>
					<li class=" tsd-kind-enum">
						<a href="enums/order.html" class="tsd-kind-icon">order</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/action.html" class="tsd-kind-icon">Action</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/actionapi.html" class="tsd-kind-icon">Action<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/actionform.html" class="tsd-kind-icon">Action<wbr>Form</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/anonymousapi.html" class="tsd-kind-icon">Anonymous<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/anonymousmodel.html" class="tsd-kind-icon">Anonymous<wbr>Model</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/authlogin.html" class="tsd-kind-icon">Auth<wbr>Login</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/bindaccount.html" class="tsd-kind-icon">Bind<wbr>Account</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/booleanitem.html" class="tsd-kind-icon">Boolean<wbr>Item</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/broadcast.html" class="tsd-kind-icon">Broadcast</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/chat.html" class="tsd-kind-icon">Chat</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/chatapi.html" class="tsd-kind-icon">Chat<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/configcenter.html" class="tsd-kind-icon">Config<wbr>Center</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/configcenterapi.html" class="tsd-kind-icon">Config<wbr>CenterAPI</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/cubebuilder.html" class="tsd-kind-icon">Cube<wbr>Builder</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/dashboard.html" class="tsd-kind-icon">Dashboard</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/dashboardapi.html" class="tsd-kind-icon">Dashboard<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/dateitem.html" class="tsd-kind-icon">Date<wbr>Item</a>
					</li>
					<li class=" tsd-kind-class tsd-has-type-parameter">
						<a href="classes/detail.html" class="tsd-kind-icon">Detail</a>
					</li>
					<li class=" tsd-kind-class tsd-has-type-parameter">
						<a href="classes/detailapi.html" class="tsd-kind-icon">Detail<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/detailparametermanager.html" class="tsd-kind-icon">Detail<wbr>Parameter<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/domainservice.html" class="tsd-kind-icon">Domain<wbr>Service</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/domainserviceapi.html" class="tsd-kind-icon">Domain<wbr>Service<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/etl.html" class="tsd-kind-icon">ETL</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/etlapi.html" class="tsd-kind-icon">ETLAPI</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/enumitem.html" class="tsd-kind-icon">Enum<wbr>Item</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/events.html" class="tsd-kind-icon">Events</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/fakestorage.html" class="tsd-kind-icon">Fake<wbr>Storage</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/fileapi.html" class="tsd-kind-icon">File<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/filemanager.html" class="tsd-kind-icon">File<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/formdataforuni.html" class="tsd-kind-icon">Form<wbr>Data<wbr>For<wbr>Uni</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/formitem.html" class="tsd-kind-icon">Form<wbr>Item</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/gateway.html" class="tsd-kind-icon">Gateway</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/global.html" class="tsd-kind-icon">Global</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/globalsearch.html" class="tsd-kind-icon">Global<wbr>Search</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/globalsearchapi.html" class="tsd-kind-icon">Global<wbr>Search<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/groovyeditor.html" class="tsd-kind-icon">Groovy<wbr>Editor</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/groovyeditorapi.html" class="tsd-kind-icon">Groovy<wbr>Editor<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/imagecompresser.html" class="tsd-kind-icon">Image<wbr>Compresser</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/indexsearch.html" class="tsd-kind-icon">Index<wbr>Search</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/indexsearchapi.html" class="tsd-kind-icon">Index<wbr>Search<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/jointsearchmanager.html" class="tsd-kind-icon">Joint<wbr>Search<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/jwttokenchecker.html" class="tsd-kind-icon">Jwt<wbr>Token<wbr>Checker</a>
					</li>
					<li class=" tsd-kind-class tsd-has-type-parameter">
						<a href="classes/list.html" class="tsd-kind-icon">List</a>
					</li>
					<li class=" tsd-kind-class tsd-has-type-parameter">
						<a href="classes/listeasy.html" class="tsd-kind-icon">List<wbr>Easy</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/listform.html" class="tsd-kind-icon">List<wbr>Form</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/listhard.html" class="tsd-kind-icon">List<wbr>Hard</a>
					</li>
					<li class=" tsd-kind-class tsd-has-type-parameter">
						<a href="classes/listquery.html" class="tsd-kind-icon">List<wbr>Query</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/listsectionbuilder.html" class="tsd-kind-icon">List<wbr>Section<wbr>Builder</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/mappingitem.html" class="tsd-kind-icon">Mapping<wbr>Item</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/matchitem.html" class="tsd-kind-icon">Match<wbr>Item</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/mediacontroller.html" class="tsd-kind-icon">Media<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/model.html" class="tsd-kind-icon">Model</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/modelapi.html" class="tsd-kind-icon">Model<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/modelschemamanager.html" class="tsd-kind-icon">Model<wbr>Schema<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/modelschemamanagerapi.html" class="tsd-kind-icon">Model<wbr>Schema<wbr>Manager<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/modelvalidator.html" class="tsd-kind-icon">Model<wbr>Validator</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/modelvalidatorapi.html" class="tsd-kind-icon">Model<wbr>Validator<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/newswagger.html" class="tsd-kind-icon">New<wbr>Swagger</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/newswaggerapi.html" class="tsd-kind-icon">New<wbr>Swagger<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/numberitem.html" class="tsd-kind-icon">Number<wbr>Item</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/oauthlogin.html" class="tsd-kind-icon">OAuth<wbr>Login</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/org.html" class="tsd-kind-icon">Org</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/orgapi.html" class="tsd-kind-icon">Org<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/passportlogin.html" class="tsd-kind-icon">Passport<wbr>Login</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/passwordbox.html" class="tsd-kind-icon">Password<wbr>Box</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/passwordboxapi.html" class="tsd-kind-icon">Password<wbr>Box<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/pivottable.html" class="tsd-kind-icon">Pivot<wbr>Table</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/pivottableapi.html" class="tsd-kind-icon">Pivot<wbr>Table<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/pluginprovider.html" class="tsd-kind-icon">Plugin<wbr>Provider</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/report.html" class="tsd-kind-icon">Report</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/reportapi.html" class="tsd-kind-icon">Report<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/role.html" class="tsd-kind-icon">Role</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/roleapi.html" class="tsd-kind-icon">Role<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/sseinvoker.html" class="tsd-kind-icon">SSEInvoker</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/scene.html" class="tsd-kind-icon">Scene</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/sceneapi.html" class="tsd-kind-icon">Scene<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/searchitem.html" class="tsd-kind-icon">Search<wbr>Item</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/sse.html" class="tsd-kind-icon">Sse</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/swagger.html" class="tsd-kind-icon">Swagger</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/swaggerapi.html" class="tsd-kind-icon">SwaggerAPi</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/tagmanager.html" class="tsd-kind-icon">Tag<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/tagmanagerapi.html" class="tsd-kind-icon">Tag<wbr>Manager<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/templatemanager.html" class="tsd-kind-icon">Template<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/templatemanagerapi.html" class="tsd-kind-icon">Template<wbr>Manager<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/tree.html" class="tsd-kind-icon">Tree</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/treeapi.html" class="tsd-kind-icon">Tree<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/treeitem.html" class="tsd-kind-icon">Tree<wbr>Item</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/uiconfig.html" class="tsd-kind-icon">UIConfig</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/uiconfigapi.html" class="tsd-kind-icon">UIConfig<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/uniappstorage.html" class="tsd-kind-icon">Uniapp<wbr>Storage</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/uniplatsdk.html" class="tsd-kind-icon">Uniplat<wbr>Sdk</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/uniplatsdkextender.html" class="tsd-kind-icon">Uniplat<wbr>Sdk<wbr>Extender</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/useragenthelper.html" class="tsd-kind-icon">User<wbr>Agent<wbr>Helper</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/workflow.html" class="tsd-kind-icon">Workflow</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/workflow2.html" class="tsd-kind-icon">Workflow2</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/workflow2api.html" class="tsd-kind-icon">Workflow2<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/workflowapi.html" class="tsd-kind-icon">Workflow<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/gatewayapi.html" class="tsd-kind-icon">gateway<wbr>Api</a>
					</li>
					<li class=" tsd-kind-class">
						<a href="classes/listapi.html" class="tsd-kind-icon">list<wbr>Api</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#actionintent" class="tsd-kind-icon">Action<wbr>Intent</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#allfilters" class="tsd-kind-icon">All<wbr>Filters</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#associateinfo" class="tsd-kind-icon">Associate<wbr>Info</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#basicinfo" class="tsd-kind-icon">Basic<wbr>Info</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#broadcastlister" class="tsd-kind-icon">Broadcast<wbr>Lister</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#changemasterrequest" class="tsd-kind-icon">Change<wbr>Master<wbr>Request</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#chartschemaitem" class="tsd-kind-icon">Chart<wbr>Schema<wbr>Item</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#chatapitype" class="tsd-kind-icon">Chat<wbr>Api<wbr>Type</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#checkeditparam" class="tsd-kind-icon">Check<wbr>Edit<wbr>Param</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#controlinfo" class="tsd-kind-icon">Control<wbr>Info</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#createchatresult" class="tsd-kind-icon">Create<wbr>Chat<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#createprocessrequest" class="tsd-kind-icon">Create<wbr>Process<wbr>Request</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-has-type-parameter">
						<a href="globals.html#createdallpresent" class="tsd-kind-icon">Created<wbr>All<wbr>Present</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-has-type-parameter">
						<a href="globals.html#createdspecifiedpresent" class="tsd-kind-icon">Created<wbr>Specified<wbr>Present</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#data" class="tsd-kind-icon">Data</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#definfo" class="tsd-kind-icon">Def<wbr>Info</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#detailitem" class="tsd-kind-icon">Detail<wbr>Item</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#editprocessrequest" class="tsd-kind-icon">Edit<wbr>Process<wbr>Request</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#fielddef" class="tsd-kind-icon">Field<wbr>Def</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#fielditem" class="tsd-kind-icon">Field<wbr>Item</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#filter" class="tsd-kind-icon">Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-has-type-parameter">
						<a href="globals.html#getlisttype" class="tsd-kind-icon">Get<wbr>List<wbr>Type</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#groupfieldfilter" class="tsd-kind-icon">Group<wbr>Field<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#groupfieldsitem" class="tsd-kind-icon">Group<wbr>Fields<wbr>Item</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#headeritem" class="tsd-kind-icon">Header<wbr>Item</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#historyversion" class="tsd-kind-icon">History<wbr>Version</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#historyversionidentity" class="tsd-kind-icon">History<wbr>Version<wbr>Identity</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#humblefilter" class="tsd-kind-icon">Humble<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#listrow" class="tsd-kind-icon">List<wbr>Row</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#meta" class="tsd-kind-icon">Meta</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#modelapitype" class="tsd-kind-icon">Model<wbr>Api<wbr>Type</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#morepreciseinput" class="tsd-kind-icon">More<wbr>Precise<wbr>Input</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#notifymeta" class="tsd-kind-icon">Notify<wbr>Meta</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#pagescolumns" class="tsd-kind-icon">Pages<wbr>Columns</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#processconfiginfo" class="tsd-kind-icon">Process<wbr>Config<wbr>Info</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#processdetail2" class="tsd-kind-icon">Process<wbr>Detail2</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#processgroup" class="tsd-kind-icon">Process<wbr>Group</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-has-type-parameter">
						<a href="globals.html#promisefunc" class="tsd-kind-icon">Promise<wbr>Func</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#queryobject" class="tsd-kind-icon">Query<wbr>Object</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-has-type-parameter">
						<a href="globals.html#returnofgetlist" class="tsd-kind-icon">Return<wbr>OfGet<wbr>List</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#rowintent" class="tsd-kind-icon">Row<wbr>Intent</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-has-type-parameter">
						<a href="globals.html#rowtypemapper" class="tsd-kind-icon">Row<wbr>Type<wbr>Mapper</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#safequeryvalue" class="tsd-kind-icon">Safe<wbr>Query<wbr>Value</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#sdkconstructorparams" class="tsd-kind-icon">Sdk<wbr>Constructor<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#sdklistrowpredict" class="tsd-kind-icon">Sdk<wbr>List<wbr>Row<wbr>Predict</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#sdklistrowpredictobject" class="tsd-kind-icon">Sdk<wbr>List<wbr>Row<wbr>Predict<wbr>Object</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#searchextproperties" class="tsd-kind-icon">Search<wbr>Ext<wbr>Properties</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#ssemessageresp" class="tsd-kind-icon">Sse<wbr>Message<wbr>Resp</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#summaryfieldsitem" class="tsd-kind-icon">Summary<wbr>Fields<wbr>Item</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#taskchangebanner" class="tsd-kind-icon">Task<wbr>Change<wbr>Banner</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#taskchangeinfo" class="tsd-kind-icon">Task<wbr>Change<wbr>Info</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#taskdefaultdealerrequest" class="tsd-kind-icon">Task<wbr>Default<wbr>Dealer<wbr>Request</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#timer" class="tsd-kind-icon">Timer</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#universalerrorcallback" class="tsd-kind-icon">Universal<wbr>Error<wbr>Callback</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#universalerrorresponsecallback" class="tsd-kind-icon">Universal<wbr>Error<wbr>Response<wbr>Callback</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#userinfochangedeventlistener" class="tsd-kind-icon">User<wbr>Info<wbr>Changed<wbr>Event<wbr>Listener</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#wantedclass" class="tsd-kind-icon">Wanted<wbr>Class</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#workobject" class="tsd-kind-icon">Work<wbr>Object</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#workflowbatchparam" class="tsd-kind-icon">Workflow<wbr>Batch<wbr>Param</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#workflowbatchrequest" class="tsd-kind-icon">Workflow<wbr>Batch<wbr>Request</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#workflowhandle" class="tsd-kind-icon">Workflow<wbr>Handle</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#workflowuser" class="tsd-kind-icon">Workflow<wbr>User</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#xidtokencache" class="tsd-kind-icon">Xid<wbr>Token<wbr>Cache</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#action" class="tsd-kind-icon">action</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#addconnectivityobserver" class="tsd-kind-icon">add<wbr>Connectivity<wbr>Observer</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#addssenotifymessagelistener" class="tsd-kind-icon">add<wbr>Sse<wbr>Notify<wbr>Message<wbr>Listener</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#addtransportmessagelistener" class="tsd-kind-icon">add<wbr>Transport<wbr>Message<wbr>Listener</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#buildfilters" class="tsd-kind-icon">build<wbr>Filters</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#column" class="tsd-kind-icon">column</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#defaultscheme" class="tsd-kind-icon">default<wbr>Scheme</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#field" class="tsd-kind-icon">field</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#field_group" class="tsd-kind-icon">field_<wbr>group</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#filter" class="tsd-kind-icon">filter</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#getallfilters" class="tsd-kind-icon">get<wbr>All<wbr>Filters</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#jwtjson" class="tsd-kind-icon">jwt<wbr>Json</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#metafilter" class="tsd-kind-icon">meta<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#metafilterfunc" class="tsd-kind-icon">meta<wbr>Filter<wbr>Func</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-has-type-parameter">
						<a href="globals.html#metarow" class="tsd-kind-icon">meta<wbr>Row</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#omittedprops" class="tsd-kind-icon">omitted<wbr>Props</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#orderobj" class="tsd-kind-icon">order<wbr>Obj</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#passportunbind" class="tsd-kind-icon">passport<wbr>Unbind</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#prefilter" class="tsd-kind-icon">prefilter</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#prefilters" class="tsd-kind-icon">prefilters</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#simplerelationshipfilter" class="tsd-kind-icon">simple<wbr>Relationship<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#uuid" class="tsd-kind-icon">uuid</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#voidfunc" class="tsd-kind-icon">void<wbr>Func</a>
					</li>
					<li class=" tsd-kind-type-alias">
						<a href="globals.html#wantedclassinstance" class="tsd-kind-icon">wanted<wbr>Class<wbr>Instance</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#init_data_storage" class="tsd-kind-icon">INIT_<wbr>DATA_<wbr>STORAGE</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#is_super_admin" class="tsd-kind-icon">IS_<wbr>SUPER_<wbr>ADMIN</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#jwt_token_storage" class="tsd-kind-icon">JWT_<wbr>TOKEN_<wbr>STORAGE</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#line_url_regex" class="tsd-kind-icon">LINE_<wbr>URL_<wbr>REGEX</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#offline" class="tsd-kind-icon">OFFLINE</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#online" class="tsd-kind-icon">ONLINE</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#project_name" class="tsd-kind-icon">PROJECT_<wbr>NAME</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#root_entrance" class="tsd-kind-icon">ROOT_<wbr>ENTRANCE</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#url_regex" class="tsd-kind-icon">URL_<wbr>REGEX</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#user_id" class="tsd-kind-icon">USER_<wbr>ID</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#user_name" class="tsd-kind-icon">USER_<wbr>NAME</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#xid_jwt_token_storage" class="tsd-kind-icon">XID_<wbr>JWT_<wbr>TOKEN_<wbr>STORAGE</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#_fakelocalstorage" class="tsd-kind-icon">_fake<wbr>Local<wbr>Storage</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#_fakesessionstorage" class="tsd-kind-icon">_fake<wbr>Session<wbr>Storage</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#alias" class="tsd-kind-icon">alias</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#axiosfactory" class="tsd-kind-icon">axios<wbr>Factory</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#channel" class="tsd-kind-icon">channel</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#charttypelist" class="tsd-kind-icon">chart<wbr>Type<wbr>List</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#chatsse" class="tsd-kind-icon">chat<wbr>Sse</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#dategrouptypemap" class="tsd-kind-icon">date<wbr>Group<wbr>Type<wbr>Map</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#defaultdatetimevalue1" class="tsd-kind-icon">default<wbr>Date<wbr>Time<wbr>Value1</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#defaultdatetimevalue2" class="tsd-kind-icon">default<wbr>Date<wbr>Time<wbr>Value2</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#defaultdatevalue1" class="tsd-kind-icon">default<wbr>Date<wbr>Value1</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#defaultdatevalue2" class="tsd-kind-icon">default<wbr>Date<wbr>Value2</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#defaultvalue" class="tsd-kind-icon">default<wbr>Value</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#events" class="tsd-kind-icon">events</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#exporttoexcelfordetail" class="tsd-kind-icon">export<wbr>ToExcel<wbr>For<wbr>Detail</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#exporttoexcelforlist" class="tsd-kind-icon">export<wbr>ToExcel<wbr>For<wbr>List</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#exporttoexcelforreport" class="tsd-kind-icon">export<wbr>ToExcel<wbr>For<wbr>Report</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#fakelocalstorage" class="tsd-kind-icon">fake<wbr>Local<wbr>Storage</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#fakesessionstorage" class="tsd-kind-icon">fake<wbr>Session<wbr>Storage</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#g" class="tsd-kind-icon">g</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#global" class="tsd-kind-icon">global</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#innode" class="tsd-kind-icon">in<wbr>Node</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#inuniapp" class="tsd-kind-icon">in<wbr>Uni<wbr>App</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#inner" class="tsd-kind-icon">inner</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#k" class="tsd-kind-icon">k</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#loadedpaths" class="tsd-kind-icon">loaded<wbr>Paths</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#m" class="tsd-kind-icon">m</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#mediacontroller" class="tsd-kind-icon">media<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#mobile_reg" class="tsd-kind-icon">mobile_<wbr>reg</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#noauthheaderurls" class="tsd-kind-icon">no<wbr>Auth<wbr>Header<wbr>Urls</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#offline" class="tsd-kind-icon">offline</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#oneday" class="tsd-kind-icon">one<wbr>Day</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#online" class="tsd-kind-icon">online</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#outlawurls" class="tsd-kind-icon">outlawURLs</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#recheckertimers" class="tsd-kind-icon">re<wbr>Checker<wbr>Timers</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#reconnecttimers" class="tsd-kind-icon">reconnect<wbr>Timers</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#sseinvoker" class="tsd-kind-icon">sse<wbr>Invoker</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#summarymap" class="tsd-kind-icon">summary<wbr>Map</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#timeoutkey" class="tsd-kind-icon">timeout<wbr>Key</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#tokenchecker" class="tsd-kind-icon">token<wbr>Checker</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#unactiveaction" class="tsd-kind-icon">unactive<wbr>Action</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#unactivechecker" class="tsd-kind-icon">unactive<wbr>Checker</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#unactivetimer" class="tsd-kind-icon">unactive<wbr>Timer</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#validtoken" class="tsd-kind-icon">valid<wbr>Token</a>
					</li>
					<li class=" tsd-kind-variable">
						<a href="globals.html#version_control" class="tsd-kind-icon">version_<wbr>control</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#channel" class="tsd-kind-icon">Channel</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#timersmanager" class="tsd-kind-icon">Timers<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#addslash" class="tsd-kind-icon">add<wbr>Slash</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#authlogin" class="tsd-kind-icon">auth<wbr>Login</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#authloginbind" class="tsd-kind-icon">auth<wbr>Login<wbr>Bind</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#bindpassporttokentojwt" class="tsd-kind-icon">bind<wbr>Passport<wbr>Token<wbr>ToJwt</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#bindteammixaccount" class="tsd-kind-icon">bind<wbr>Teammix<wbr>Account</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#bindxiaobaoaccount" class="tsd-kind-icon">bind<wbr>Xiao<wbr>Bao<wbr>Account</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#builddatestring" class="tsd-kind-icon">build<wbr>Date<wbr>String</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#buildfilters" class="tsd-kind-icon">build<wbr>Filters</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#changetokenwithxid" class="tsd-kind-icon">change<wbr>Token<wbr>With<wbr>Xid</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#color2rgb" class="tsd-kind-icon">color2<wbr>Rgb</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#colortohex" class="tsd-kind-icon">color<wbr>ToHex</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#colortorgb" class="tsd-kind-icon">color<wbr>ToRgb</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#convert" class="tsd-kind-icon">convert</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#convert2range" class="tsd-kind-icon">convert2<wbr>Range</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#convertdatetimedefaultvalue" class="tsd-kind-icon">convert<wbr>Date<wbr>Time<wbr>Default<wbr>Value</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#copytexttoclipboard" class="tsd-kind-icon">copy<wbr>Text<wbr>ToClipboard</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#createaxios" class="tsd-kind-icon">create<wbr>Axios</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#createbroadcastchannel" class="tsd-kind-icon">create<wbr>Broad<wbr>Cast<wbr>Channel</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#createconnection" class="tsd-kind-icon">create<wbr>Connection</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#createerror" class="tsd-kind-icon">create<wbr>Error</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#createfiltershandler" class="tsd-kind-icon">create<wbr>Filters<wbr>Handler</a>
					</li>
					<li class=" tsd-kind-function tsd-has-type-parameter">
						<a href="globals.html#createformdata" class="tsd-kind-icon">create<wbr>Form<wbr>Data</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#createiniterrormsg" class="tsd-kind-icon">create<wbr>Init<wbr>Error<wbr>Msg</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#createverifycodeimageurl" class="tsd-kind-icon">create<wbr>Verify<wbr>Code<wbr>Image<wbr>Url</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#debounce" class="tsd-kind-icon">debounce</a>
					</li>
					<li class=" tsd-kind-function tsd-has-type-parameter">
						<a href="globals.html#encodeparams" class="tsd-kind-icon">encode<wbr>Params</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#exporttoexcel" class="tsd-kind-icon">export<wbr>ToExcel</a>
					</li>
					<li class=" tsd-kind-function tsd-has-type-parameter">
						<a href="globals.html#exporttoreportexcel" class="tsd-kind-icon">export<wbr>ToReport<wbr>Excel</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#filterhumblefilter" class="tsd-kind-icon">filter<wbr>Humble<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#filtersimplerelationshipfilter" class="tsd-kind-icon">filter<wbr>Simple<wbr>Relationship<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#formdataarray" class="tsd-kind-icon">form<wbr>Data<wbr>Array</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#formatdate" class="tsd-kind-icon">format<wbr>Date</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#formatfilesize" class="tsd-kind-icon">format<wbr>File<wbr>Size</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#getaccountbindstatus" class="tsd-kind-icon">get<wbr>Account<wbr>Bind<wbr>Status</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#getallfilters" class="tsd-kind-icon">get<wbr>All<wbr>Filters</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#getanonymousurl" class="tsd-kind-icon">get<wbr>Anonymous<wbr>Url</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#getformitem" class="tsd-kind-icon">get<wbr>Form<wbr>Item</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#getjwttokenbypassporttoken" class="tsd-kind-icon">getJWTToken<wbr>ByPassport<wbr>Token</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#getloglist" class="tsd-kind-icon">get<wbr>Log<wbr>List</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#gettokenexp" class="tsd-kind-icon">get<wbr>Token<wbr>Exp</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#gettreefilterlist" class="tsd-kind-icon">get<wbr>Tree<wbr>Filter<wbr>List</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#getuserinfo" class="tsd-kind-icon">get<wbr>User<wbr>Info</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#getuserinfobyjwt" class="tsd-kind-icon">get<wbr>User<wbr>Info<wbr>ByJwt</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#hrsoauthlogin" class="tsd-kind-icon">hrs<wbr>Oauth<wbr>Login</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#initcompanyauthlogindata" class="tsd-kind-icon">init<wbr>Company<wbr>Auth<wbr>Login<wbr>Data</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#isaccessibleurl" class="tsd-kind-icon">is<wbr>Accessible<wbr>Url</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#isinnode" class="tsd-kind-icon">is<wbr>InNode</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#isinuniapp" class="tsd-kind-icon">is<wbr>InUni<wbr>App</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#issimplerelationshipfilter" class="tsd-kind-icon">is<wbr>Simple<wbr>Relationship<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#istokenvalid" class="tsd-kind-icon">is<wbr>Token<wbr>Valid</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#isurl" class="tsd-kind-icon">is<wbr>Url</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#loadscript" class="tsd-kind-icon">load<wbr>Script</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#login" class="tsd-kind-icon">login</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#logout" class="tsd-kind-icon">logout</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#metafilter" class="tsd-kind-icon">meta<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#noop" class="tsd-kind-icon">noop</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#paramhandler" class="tsd-kind-icon">param<wbr>Handler</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#parsetoken" class="tsd-kind-icon">parse<wbr>Token</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#prevertxss" class="tsd-kind-icon">prevert<wbr>Xss</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#randstring" class="tsd-kind-icon">rand<wbr>String</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#rebuildtoken" class="tsd-kind-icon">rebuild<wbr>Token</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#refreshtoken" class="tsd-kind-icon">refresh<wbr>Token</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#replacephone2dom" class="tsd-kind-icon">replace<wbr>Phone2<wbr>Dom</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#replacetext2dom" class="tsd-kind-icon">replace<wbr>Text2<wbr>Dom</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#replacetext2link" class="tsd-kind-icon">replace<wbr>Text2<wbr>Link</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#serialize" class="tsd-kind-icon">serialize</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#setunactivecallback" class="tsd-kind-icon">set<wbr>Unactive<wbr>Callback</a>
					</li>
					<li class=" tsd-kind-function tsd-has-type-parameter">
						<a href="globals.html#sseworker" class="tsd-kind-icon">sse<wbr>Worker</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#teammixoauthlogin" class="tsd-kind-icon">teammix<wbr>Oauth<wbr>Login</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#text2md5" class="tsd-kind-icon">text2md5</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#throttle" class="tsd-kind-icon">throttle</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#trim" class="tsd-kind-icon">trim</a>
					</li>
					<li class=" tsd-kind-function tsd-has-type-parameter">
						<a href="globals.html#trycatchwrapper" class="tsd-kind-icon">try<wbr>Catch<wbr>Wrapper</a>
					</li>
					<li class=" tsd-kind-function tsd-has-type-parameter">
						<a href="globals.html#unique" class="tsd-kind-icon">unique</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#uppperfirstchar" class="tsd-kind-icon">uppper<wbr>First<wbr>Char</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#utf8codeat" class="tsd-kind-icon">utf8<wbr>Code<wbr>At</a>
					</li>
					<li class=" tsd-kind-function">
						<a href="globals.html#uuid" class="tsd-kind-icon">uuid</a>
					</li>
					<li class=" tsd-kind-object-literal">
						<a href="globals.html#axios" class="tsd-kind-icon">axios</a>
					</li>
					<li class=" tsd-kind-object-literal">
						<a href="globals.html#builder" class="tsd-kind-icon">builder</a>
					</li>
					<li class=" tsd-kind-object-literal">
						<a href="globals.html#configurationapi" class="tsd-kind-icon">configuration<wbr>Api</a>
					</li>
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="assets/js/main.js"></script>
</body>
</html>