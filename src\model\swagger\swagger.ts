import { global } from "../../core/global"
import type * as dto from "../../def/index"

import SwaggerAPi from "./api"
export class Swagger {
    private interfaceModels: unknown = {}

    private api: SwaggerAPi
    constructor() {
        this.api = new SwaggerAPi()
    }

    private forgeInterfaceModels(data: dto.SwaggerTypes.queryResult) {
        data.forEach((subproject) => {
            this.interfaceModels[subproject.label] = {}
            subproject.datamodels.forEach((model) => {
                this.interfaceModels[subproject.label][model.name] = {
                    queries: {},
                    detail: {
                        keyvalue: "",
                    },
                    actions: {},
                    customs: {},
                }
                model.actions.forEach((action) => {
                    this.interfaceModels[subproject.label][model.name].actions[
                        action.name
                    ] = {
                        inputs: {},
                    }
                    action.inputs.map((parameter) => {
                        this.interfaceModels[subproject.label][
                            model.name
                        ].actions[action.name].inputs[parameter.property] = ""
                    })
                })
                model.customs.forEach((custom) => {
                    this.interfaceModels[subproject.label][model.name].customs[
                        custom.interfaceName
                    ] = {
                        inputs: {},
                        header: "UniplatJWT",
                    }
                    Object.keys(custom.description.parameters).map(
                        (parameter) => {
                            this.interfaceModels[subproject.label][
                                model.name
                            ].customs[custom.interfaceName].inputs[parameter] =
                                ""
                        }
                    )
                })
            })
        })
    }

    public getInterfaceModels() {
        return this.interfaceModels
    }

    public getCodeSnipperThenRequest(params: dto.SwaggerTypes.requestParams) {
        const queryObj =
            this.interfaceModels[params.label][params.model_name].customs[
                params.interfaceName
            ].inputs
        const queryString = Object.keys(queryObj)
            .map((k) => k + "=" + encodeURI(queryObj[k]))
            .join("&")
        const url = `general/model/${params.projectname}.${params.model_name}/request/${params.interfaceName}/?${queryString}`

        const snipperParams = `{
            ${Object.keys(
                this.interfaceModels[params.label][params.model_name].customs[
                    params.interfaceName
                ].inputs
            )
                .map(
                    (key) =>
                        `   ${key}:'${
                            this.interfaceModels[params.label][
                                params.model_name
                            ].customs[params.interfaceName].inputs[key]
                        }'`
                )
                .join(",\n")}
        }
        `
        const snippet = `
        let params =${snipperParams}
        this.axios({
                method:"${params.methodName}",
                url:"${url}"+"?"+Object.keys(x).map(xx=>xx+"="+encodeURI(x[xx])).join("&"),
                headers: {
                    'Authorization': "${global.jwtToken}"
                }
            }).then(res => {
                if (res.data.rescode === 0) {
                    console.log('request succeed and response succeed:', res);
                } else {
                    console.log('request succeed and response failed:', res.data.msg);
                }
            })
            .catch(err => {
                console.log('result failed with error:', err);
            });
        });
        `
        return {
            snippet,
            request: this.request.bind(this, params),
        }
    }

    public async query() {
        const data = await this.api.query()
        this.forgeInterfaceModels(data)
        return data
    }

    private request(params: dto.SwaggerTypes.requestParams) {
        const queryObj =
            this.interfaceModels[params.label][params.model_name].customs[
                params.interfaceName
            ].inputs
        const queryString = Object.keys(queryObj)
            .map((k) => k + "=" + encodeURI(queryObj[k]))
            .join("&")
        const url = `general/model/${params.projectname}.${params.model_name}/request/${params.interfaceName}/?${queryString}`

        return this.api.request({
            methodName: params.methodName,
            projectname: params.projectname,
            model_name: params.model_name,
            interfaceName: params.interfaceName,
            url,
        })
    }
}
