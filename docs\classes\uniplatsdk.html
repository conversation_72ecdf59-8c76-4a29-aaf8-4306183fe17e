<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>UniplatSdk | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="uniplatsdk.html">UniplatSdk</a>
				</li>
			</ul>
			<h1>Class UniplatSdk</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">UniplatSdk</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="uniplatsdk.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="uniplatsdk.html#_isloggedin" class="tsd-kind-icon">_is<wbr>Loggedin</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="uniplatsdk.html#config" class="tsd-kind-icon">config</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class"><a href="uniplatsdk.html#configurationapi" class="tsd-kind-icon">configuration<wbr>Api</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class"><a href="uniplatsdk.html#custompluginprovider" class="tsd-kind-icon">custom<wbr>Plugin<wbr>Provider</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class"><a href="uniplatsdk.html#events" class="tsd-kind-icon">events</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class"><a href="uniplatsdk.html#global" class="tsd-kind-icon">global</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class"><a href="uniplatsdk.html#mediacontroller" class="tsd-kind-icon">media<wbr>Controller</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="uniplatsdk.html#seed" class="tsd-kind-icon">seed</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Accessors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="uniplatsdk.html#isloggedin" class="tsd-kind-icon">is<wbr>Logged<wbr>In</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="uniplatsdk.html#afterlogin" class="tsd-kind-icon">after<wbr>Login</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#connect" class="tsd-kind-icon">connect</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#disconnect" class="tsd-kind-icon">disconnect</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#domainservice" class="tsd-kind-icon">domain<wbr>Service</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#downloadfile" class="tsd-kind-icon">download<wbr>File</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="uniplatsdk.html#get" class="tsd-kind-icon">get</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#getauthlogin" class="tsd-kind-icon">get<wbr>Auth<wbr>Login</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#getaxios" class="tsd-kind-icon">get<wbr>Axios</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#getbindaccountmethods" class="tsd-kind-icon">get<wbr>Bind<wbr>Account<wbr>Methods</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#getloglist" class="tsd-kind-icon">get<wbr>Log<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#getoauthmethods" class="tsd-kind-icon">getOAuth<wbr>Methods</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#getpassportlogin" class="tsd-kind-icon">get<wbr>Passport<wbr>Login</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#getsseconnectivity" class="tsd-kind-icon">getSSEConnectivity</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#getuserinfo" class="tsd-kind-icon">get<wbr>User<wbr>Info</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#getuserinfobyjwt" class="tsd-kind-icon">get<wbr>User<wbr>Info<wbr>ByJwt</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#getverifyimage" class="tsd-kind-icon">get<wbr>Verify<wbr>Image</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#getverifyimageandseed" class="tsd-kind-icon">get<wbr>Verify<wbr>Image<wbr>And<wbr>Seed</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#injectdependency" class="tsd-kind-icon">inject<wbr>Dependency</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="uniplatsdk.html#loggedin" class="tsd-kind-icon">loggedin</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="uniplatsdk.html#loggedout" class="tsd-kind-icon">loggedout</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#login" class="tsd-kind-icon">login</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#loginbytoken" class="tsd-kind-icon">login<wbr>ByToken</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#logout" class="tsd-kind-icon">logout</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#model" class="tsd-kind-icon">model</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#org" class="tsd-kind-icon">org</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="uniplatsdk.html#post" class="tsd-kind-icon">post</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="uniplatsdk.html#proxyevents" class="tsd-kind-icon">proxy<wbr>Events</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#report" class="tsd-kind-icon">report</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#scene" class="tsd-kind-icon">scene</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#setinitdata" class="tsd-kind-icon">set<wbr>Init<wbr>Data</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#setunactivetimeout" class="tsd-kind-icon">set<wbr>UnActive<wbr>Timeout</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#uploadfile" class="tsd-kind-icon">upload<wbr>File</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#uploadfileforuniapp" class="tsd-kind-icon">upload<wbr>File<wbr>For<wbr>Uni<wbr>App</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#uploadfileforwxapp" class="tsd-kind-icon">upload<wbr>File<wbr>For<wbr>WxApp</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#uploadfileothers" class="tsd-kind-icon">upload<wbr>File<wbr>Others</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="uniplatsdk.html#uploadfilev2" class="tsd-kind-icon">upload<wbr>File<wbr>V2</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="uniplatsdk.html#waitforlogin" class="tsd-kind-icon">wait<wbr>For<wbr>Login</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new <wbr>Uniplat<wbr>Sdk<span class="tsd-signature-symbol">(</span>config<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.SdkConstructorParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="uniplatsdk.html" class="tsd-signature-type">UniplatSdk</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:31</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagDefault value">Default value</span> config: <span class="tsd-signature-type">dto.SdkConstructorParams</span><span class="tsd-signature-symbol"> = {sse: false,ssr: false,}</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="uniplatsdk.html" class="tsd-signature-type">UniplatSdk</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="_isloggedin" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> _is<wbr>Loggedin</h3>
					<div class="tsd-signature tsd-kind-icon">_is<wbr>Loggedin<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/index/index.ts:49</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="config" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> config</h3>
					<div class="tsd-signature tsd-kind-icon">config<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.SdkConstructorParams</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/index/index.ts:33</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class">
					<a name="configurationapi" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagReadonly">Readonly</span> configuration<wbr>Api</h3>
					<div class="tsd-signature tsd-kind-icon">configuration<wbr>Api<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>changeTokenWithXid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span>getAppConfig<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span>getAppRouters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span>getApplicationOrg<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span>getInitConfig<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span>getInitialApplicationData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span>getInitialData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span>getRouters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> = api.configurationApi</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/index/index.ts:52</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>change<wbr>Token<wbr>With<wbr>Xid<span class="tsd-signature-symbol">: </span>function</h5>
								<ul class="tsd-signatures tsd-kind-function tsd-parent-kind-type-literal">
									<li class="tsd-signature tsd-kind-icon">change<wbr>Token<wbr>With<wbr>Xid<span class="tsd-signature-symbol">(</span>xid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<aside class="tsd-sources">
											<ul>
												<li>Defined in src/model/index/api.ts:114</li>
											</ul>
										</aside>
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>xid: <span class="tsd-signature-type">string</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>get<wbr>App<wbr>Config<span class="tsd-signature-symbol">: </span>function</h5>
								<ul class="tsd-signatures tsd-kind-function tsd-parent-kind-type-literal">
									<li class="tsd-signature tsd-kind-icon">get<wbr>App<wbr>Config<span class="tsd-signature-symbol">(</span>application<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, entrance<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/index.html#initconfig" class="tsd-signature-type">InitConfig</a><span class="tsd-signature-symbol">&gt;</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<aside class="tsd-sources">
											<ul>
												<li>Defined in src/model/index/api.ts:91</li>
											</ul>
										</aside>
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>application: <span class="tsd-signature-type">string</span></h5>
											</li>
											<li>
												<h5>entrance: <span class="tsd-signature-type">string</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/index.html#initconfig" class="tsd-signature-type">InitConfig</a><span class="tsd-signature-symbol">&gt;</span></h4>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>get<wbr>App<wbr>Routers<span class="tsd-signature-symbol">: </span>function</h5>
								<ul class="tsd-signatures tsd-kind-function tsd-parent-kind-type-literal">
									<li class="tsd-signature tsd-kind-icon">get<wbr>App<wbr>Routers<span class="tsd-signature-symbol">(</span>application<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, entrance<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/index.html#entrance" class="tsd-signature-type">Entrance</a><span class="tsd-signature-symbol">&gt;</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<aside class="tsd-sources">
											<ul>
												<li>Defined in src/model/index/api.ts:103</li>
											</ul>
										</aside>
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>application: <span class="tsd-signature-type">string</span></h5>
											</li>
											<li>
												<h5>entrance: <span class="tsd-signature-type">string</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/index.html#entrance" class="tsd-signature-type">Entrance</a><span class="tsd-signature-symbol">&gt;</span></h4>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>get<wbr>Application<wbr>Org<span class="tsd-signature-symbol">: </span>function</h5>
								<ul class="tsd-signatures tsd-kind-function tsd-parent-kind-type-literal">
									<li class="tsd-signature tsd-kind-icon">get<wbr>Application<wbr>Org<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>application<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<aside class="tsd-sources">
											<ul>
												<li>Defined in src/model/index/api.ts:110</li>
											</ul>
										</aside>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>application<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>get<wbr>Init<wbr>Config<span class="tsd-signature-symbol">: </span>function</h5>
								<ul class="tsd-signatures tsd-kind-function tsd-parent-kind-type-literal">
									<li class="tsd-signature tsd-kind-icon">get<wbr>Init<wbr>Config<span class="tsd-signature-symbol">(</span>entrance<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/index.html#initconfig" class="tsd-signature-type">InitConfig</a><span class="tsd-signature-symbol">&gt;</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<aside class="tsd-sources">
											<ul>
												<li>Defined in src/model/index/api.ts:86</li>
											</ul>
										</aside>
										<div class="tsd-comment tsd-typography">
											<dl class="tsd-comment-tags">
												<dt>deprecated(请使用getappconfig)</dt>
												<dd></dd>
											</dl>
										</div>
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>entrance: <span class="tsd-signature-type">string</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/index.html#initconfig" class="tsd-signature-type">InitConfig</a><span class="tsd-signature-symbol">&gt;</span></h4>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>get<wbr>Initial<wbr>Application<wbr>Data<span class="tsd-signature-symbol">: </span>function</h5>
								<ul class="tsd-signatures tsd-kind-function tsd-parent-kind-type-literal">
									<li class="tsd-signature tsd-kind-icon">get<wbr>Initial<wbr>Application<wbr>Data<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/index.html#applicationconfig" class="tsd-signature-type">ApplicationConfig</a><span class="tsd-signature-symbol">&gt;</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<aside class="tsd-sources">
											<ul>
												<li>Defined in src/model/index/api.ts:80</li>
											</ul>
										</aside>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/index.html#applicationconfig" class="tsd-signature-type">ApplicationConfig</a><span class="tsd-signature-symbol">&gt;</span></h4>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>get<wbr>Initial<wbr>Data<span class="tsd-signature-symbol">: </span>function</h5>
								<ul class="tsd-signatures tsd-kind-function tsd-parent-kind-type-literal">
									<li class="tsd-signature tsd-kind-icon">get<wbr>Initial<wbr>Data<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/index.html#initialdata" class="tsd-signature-type">InitialData</a><span class="tsd-signature-symbol">&gt;</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<aside class="tsd-sources">
											<ul>
												<li>Defined in src/model/index/api.ts:77</li>
											</ul>
										</aside>
										<div class="tsd-comment tsd-typography">
											<dl class="tsd-comment-tags">
												<dt>deprecated(请使用getinitialapplicationdata)</dt>
												<dd><p>获取uniplat登录前的一些信息</p>
												</dd>
											</dl>
										</div>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/index.html#initialdata" class="tsd-signature-type">InitialData</a><span class="tsd-signature-symbol">&gt;</span></h4>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>get<wbr>Routers<span class="tsd-signature-symbol">: </span>function</h5>
								<ul class="tsd-signatures tsd-kind-function tsd-parent-kind-type-literal">
									<li class="tsd-signature tsd-kind-icon">get<wbr>Routers<span class="tsd-signature-symbol">(</span>entrance<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/index.html#entrance" class="tsd-signature-type">Entrance</a><span class="tsd-signature-symbol">&gt;</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<aside class="tsd-sources">
											<ul>
												<li>Defined in src/model/index/api.ts:98</li>
											</ul>
										</aside>
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>entrance: <span class="tsd-signature-type">string</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/index.html#entrance" class="tsd-signature-type">Entrance</a><span class="tsd-signature-symbol">&gt;</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class">
					<a name="custompluginprovider" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagReadonly">Readonly</span> custom<wbr>Plugin<wbr>Provider</h3>
					<div class="tsd-signature tsd-kind-icon">custom<wbr>Plugin<wbr>Provider<span class="tsd-signature-symbol">:</span> <a href="pluginprovider.html" class="tsd-signature-type">PluginProvider</a><span class="tsd-signature-symbol"> = new PluginProvider()</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/index/index.ts:67</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class">
					<a name="events" class="tsd-anchor"></a>
					<h3>events</h3>
					<div class="tsd-signature tsd-kind-icon">events<span class="tsd-signature-symbol">:</span> <a href="events.html" class="tsd-signature-type">Events</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/index/index.ts:55</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>用来添加事件监听的类</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class">
					<a name="global" class="tsd-anchor"></a>
					<h3>global</h3>
					<div class="tsd-signature tsd-kind-icon">global<span class="tsd-signature-symbol">:</span> <a href="global.html" class="tsd-signature-type">Global</a><span class="tsd-signature-symbol"> = global</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/index/index.ts:58</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>一个用于共享变量的类</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class">
					<a name="mediacontroller" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagReadonly">Readonly</span> media<wbr>Controller</h3>
					<div class="tsd-signature tsd-kind-icon">media<wbr>Controller<span class="tsd-signature-symbol">:</span> <a href="mediacontroller.html" class="tsd-signature-type">MediaController</a><span class="tsd-signature-symbol"> = mediaController</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/index/index.ts:65</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="seed" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> seed</h3>
					<div class="tsd-signature tsd-kind-icon">seed<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/index/index.ts:50</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Accessors</h2>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="isloggedin" class="tsd-anchor"></a>
					<h3>is<wbr>Logged<wbr>In</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> isLoggedIn<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:61</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>是否登录了</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="afterlogin" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> after<wbr>Login</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">after<wbr>Login<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:89</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="connect" class="tsd-anchor"></a>
					<h3>connect</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">connect<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.Index.connectParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:129</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>链接一个uniplat服务器</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.Index.connectParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="disconnect" class="tsd-anchor"></a>
					<h3>disconnect</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">disconnect<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:231</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>断开sdk与uniplat服务器的链接</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="domainservice" class="tsd-anchor"></a>
					<h3>domain<wbr>Service</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">domain<wbr>Service<span class="tsd-signature-symbol">(</span>subProjectName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, serviceName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, apiName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="domainservice.html" class="tsd-signature-type">DomainService</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:285</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取领域服务</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>subProjectName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>serviceName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>apiName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="domainservice.html" class="tsd-signature-type">DomainService</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="downloadfile" class="tsd-anchor"></a>
					<h3>download<wbr>File</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">download<wbr>File<span class="tsd-signature-symbol">(</span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ArrayBuffer</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:428</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>下载一个位于指定url的文件</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>url: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ArrayBuffer</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="get" class="tsd-anchor"></a>
					<h3>get</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">get&lt;T&gt;<span class="tsd-signature-symbol">(</span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, config<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">AxiosRequestConfig</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:109</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>url: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> config: <span class="tsd-signature-type">AxiosRequestConfig</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getauthlogin" class="tsd-anchor"></a>
					<h3>get<wbr>Auth<wbr>Login</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Auth<wbr>Login<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="authlogin.html" class="tsd-signature-type">AuthLogin</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:324</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取auth登录方法</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <a href="authlogin.html" class="tsd-signature-type">AuthLogin</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getaxios" class="tsd-anchor"></a>
					<h3>get<wbr>Axios</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Axios<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">AxiosInstance</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:105</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取sdk所用的axios实例</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">AxiosInstance</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getbindaccountmethods" class="tsd-anchor"></a>
					<h3>get<wbr>Bind<wbr>Account<wbr>Methods</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Bind<wbr>Account<wbr>Methods<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="bindaccount.html" class="tsd-signature-type">BindAccount</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:331</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取绑定小包和teammix账号的方法</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <a href="bindaccount.html" class="tsd-signature-type">BindAccount</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getloglist" class="tsd-anchor"></a>
					<h3>get<wbr>Log<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Log<wbr>List<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:162</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取日志列表</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getoauthmethods" class="tsd-anchor"></a>
					<h3>getOAuth<wbr>Methods</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">getOAuth<wbr>Methods<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="oauthlogin.html" class="tsd-signature-type">OAuthLogin</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:335</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <a href="oauthlogin.html" class="tsd-signature-type">OAuthLogin</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getpassportlogin" class="tsd-anchor"></a>
					<h3>get<wbr>Passport<wbr>Login</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Passport<wbr>Login<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="passportlogin.html" class="tsd-signature-type">PassportLogin</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:317</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取passport登录方法</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <a href="passportlogin.html" class="tsd-signature-type">PassportLogin</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getsseconnectivity" class="tsd-anchor"></a>
					<h3>getSSEConnectivity</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">getSSEConnectivity<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:155</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取sse链接状态</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getuserinfo" class="tsd-anchor"></a>
					<h3>get<wbr>User<wbr>Info</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>User<wbr>Info<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type">getUserInfo</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:240</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取登录用户信息</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="" class="tsd-signature-type">getUserInfo</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getuserinfobyjwt" class="tsd-anchor"></a>
					<h3>get<wbr>User<wbr>Info<wbr>ByJwt</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>User<wbr>Info<wbr>ByJwt<span class="tsd-signature-symbol">(</span>jwt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="uniplatsdk.html#getuserinfo" class="tsd-signature-type">getUserInfo</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:252</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取指定token对应的用户的信息</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>jwt: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="uniplatsdk.html#getuserinfo" class="tsd-signature-type">getUserInfo</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getverifyimage" class="tsd-anchor"></a>
					<h3>get<wbr>Verify<wbr>Image</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Verify<wbr>Image<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:169</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>如果使用unipalt登录，需要填写验证码</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getverifyimageandseed" class="tsd-anchor"></a>
					<h3>get<wbr>Verify<wbr>Image<wbr>And<wbr>Seed</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Verify<wbr>Image<wbr>And<wbr>Seed<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>img<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>seed<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:174</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{ </span>img<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>seed<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></h4>
							<ul class="tsd-parameters">
								<li class="tsd-parameter">
									<h5>img<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
								</li>
								<li class="tsd-parameter">
									<h5>seed<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="injectdependency" class="tsd-anchor"></a>
					<h3>inject<wbr>Dependency</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">inject<wbr>Dependency<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.Index.injectDependencyParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:120</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>注入broadcast-channel依赖</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.Index.injectDependencyParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="loggedin" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> loggedin</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">loggedin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:80</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="loggedout" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> loggedout</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">loggedout<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:84</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="login" class="tsd-anchor"></a>
					<h3>login</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">login<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.Index.loginParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/index.html#loginrequestresult" class="tsd-signature-type">loginRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:196</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>uniplat登录</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.Index.loginParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/index.html#loginrequestresult" class="tsd-signature-type">loginRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="loginbytoken" class="tsd-anchor"></a>
					<h3>login<wbr>ByToken</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">login<wbr>ByToken<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.Index.loginByTokenParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:186</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>当sdk外面已经登录了，直接传token给sdk，让sdk也登录</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.Index.loginByTokenParams</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="logout" class="tsd-anchor"></a>
					<h3>logout</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">logout<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:221</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>登出</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="model" class="tsd-anchor"></a>
					<h3>model</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">model<span class="tsd-signature-symbol">(</span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="model.html" class="tsd-signature-type">Model</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:274</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>name: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="model.html" class="tsd-signature-type">Model</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="org" class="tsd-anchor"></a>
					<h3>org</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">org<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Org</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:270</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Org</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="post" class="tsd-anchor"></a>
					<h3>post</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">post&lt;T&gt;<span class="tsd-signature-symbol">(</span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, data<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span>, config<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">AxiosRequestConfig</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:113</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>url: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> data: <span class="tsd-signature-type">unknown</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> config: <span class="tsd-signature-type">AxiosRequestConfig</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="proxyevents" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> proxy<wbr>Events</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">proxy<wbr>Events<span class="tsd-signature-symbol">(</span>oldAddTokenExpiring<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span>cb<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.callBackFromOutside</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:69</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>oldAddTokenExpiring: <span class="tsd-signature-symbol">(</span>cb<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.callBackFromOutside</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>cb<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.callBackFromOutside</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>cb: <span class="tsd-signature-type">dto.SSE.callBackFromOutside</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="report" class="tsd-anchor"></a>
					<h3>report</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">report<span class="tsd-signature-symbol">(</span>domain<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, reportName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, secondaryApi<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Report</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:278</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>domain: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>reportName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> secondaryApi: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Report</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="scene" class="tsd-anchor"></a>
					<h3>scene</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">scene<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="scene.html" class="tsd-signature-type">Scene</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:266</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取一个scene类</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <a href="scene.html" class="tsd-signature-type">Scene</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="setinitdata" class="tsd-anchor"></a>
					<h3>set<wbr>Init<wbr>Data</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Init<wbr>Data<span class="tsd-signature-symbol">(</span>initData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.Index.InitData</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:259</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>initData: <span class="tsd-signature-type">dto.Index.InitData</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="setunactivetimeout" class="tsd-anchor"></a>
					<h3>set<wbr>UnActive<wbr>Timeout</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">set<wbr>UnActive<wbr>Timeout<span class="tsd-signature-symbol">(</span>duration<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, trigger<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:444</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>设置用户在非活动状态下时限，如果达到此时限，则调用相应回调函数</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>duration: <span class="tsd-signature-type">number</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>时限，单位为毫秒</p>
									</div>
								</li>
								<li>
									<h5>trigger: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>回调</p>
									</div>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="uploadfile" class="tsd-anchor"></a>
					<h3>upload<wbr>File</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">upload<wbr>File<span class="tsd-signature-symbol">(</span>file<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">File</span>, config<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>getCancelSource<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>cancel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Canceler</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">; </span>onUploadProgress<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:345</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>上传文件</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>file: <span class="tsd-signature-type">File</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> config: <span class="tsd-signature-symbol">{ </span>getCancelSource<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>cancel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Canceler</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">; </span>onUploadProgress<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> get<wbr>Cancel<wbr>Source<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>cancel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Canceler</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
											<div class="tsd-comment tsd-typography">
												<div class="lead">
													<p>拿到取消方法</p>
												</div>
											</div>
											<ul class="tsd-parameters">
												<li class="tsd-parameter-signature">
													<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-variable">
														<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>cancel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Canceler</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
													</ul>
													<ul class="tsd-descriptions">
														<li class="tsd-description">
															<h4 class="tsd-parameters-title">Parameters</h4>
															<ul class="tsd-parameters">
																<li>
																	<h5>cancel: <span class="tsd-signature-type">Canceler</span></h5>
																</li>
															</ul>
															<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
														</li>
													</ul>
												</li>
											</ul>
										</li>
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> on<wbr>Upload<wbr>Progress<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
											<div class="tsd-comment tsd-typography">
												<div class="lead">
													<p>上传进度</p>
												</div>
											</div>
											<ul class="tsd-parameters">
												<li class="tsd-parameter-signature">
													<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-variable">
														<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
													</ul>
													<ul class="tsd-descriptions">
														<li class="tsd-description">
															<h4 class="tsd-parameters-title">Parameters</h4>
															<ul class="tsd-parameters">
																<li>
																	<h5>e: <span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></h5>
																	<ul class="tsd-parameters">
																		<li class="tsd-parameter">
																			<h5>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
																		</li>
																		<li class="tsd-parameter">
																			<h5>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
																		</li>
																		<li class="tsd-parameter">
																			<h5>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
																		</li>
																	</ul>
																</li>
															</ul>
															<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
														</li>
													</ul>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="uploadfileforuniapp" class="tsd-anchor"></a>
					<h3>upload<wbr>File<wbr>For<wbr>Uni<wbr>App</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">upload<wbr>File<wbr>For<wbr>Uni<wbr>App<span class="tsd-signature-symbol">(</span>file<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>base64<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>tempFile<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">ChooseFile</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">ChooseImageSuccessCallbackResultFile</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">ChooseFileSuccessCallbackResultFile</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">File</span>, getTask<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">UploadTask</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>fileName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fullUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:408</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>微信上传文件
									tempFile可以直接是base64图片</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>file: <span class="tsd-signature-symbol">{ </span>base64<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>tempFile<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">ChooseFile</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">ChooseImageSuccessCallbackResultFile</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">ChooseFileSuccessCallbackResultFile</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">File</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> getTask: <span class="tsd-signature-symbol">(</span>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">UploadTask</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">UploadTask</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>task: <span class="tsd-signature-type">UploadTask</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>fileName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fullUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="uploadfileforwxapp" class="tsd-anchor"></a>
					<h3>upload<wbr>File<wbr>For<wbr>WxApp</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">upload<wbr>File<wbr>For<wbr>WxApp<span class="tsd-signature-symbol">(</span>fileName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, path<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, buffer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Uint8Array</span>, getTask<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>any<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span>, modelName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>fileName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fullUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:386</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>微信上传文件</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>fileName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>path: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>size: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>buffer: <span class="tsd-signature-type">Uint8Array</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> getTask: <span class="tsd-signature-symbol">(</span>any<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>any<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>any: <span class="tsd-signature-type">any</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> modelName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>fileName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fullUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="uploadfileothers" class="tsd-anchor"></a>
					<h3>upload<wbr>File<wbr>Others</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">upload<wbr>File<wbr>Others<span class="tsd-signature-symbol">(</span>file<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">File</span>, modelName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>code<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>uri<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:435</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>薪酬，工资表上传/上传其他文件</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>file: <span class="tsd-signature-type">File</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> modelName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>code<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>uri<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="uploadfilev2" class="tsd-anchor"></a>
					<h3>upload<wbr>File<wbr>V2</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">upload<wbr>File<wbr>V2<span class="tsd-signature-symbol">(</span>file<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">File</span>, config<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>getCancelSource<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>cancel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Canceler</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">; </span>onUploadProgress<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol"> }</span>, modelName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>code<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fileName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fullUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:368</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>上传文件V2</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>file: <span class="tsd-signature-type">File</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> config: <span class="tsd-signature-symbol">{ </span>getCancelSource<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>cancel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Canceler</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">; </span>onUploadProgress<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> get<wbr>Cancel<wbr>Source<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>cancel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Canceler</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
											<ul class="tsd-parameters">
												<li class="tsd-parameter-signature">
													<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-variable">
														<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>cancel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Canceler</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
													</ul>
													<ul class="tsd-descriptions">
														<li class="tsd-description">
															<h4 class="tsd-parameters-title">Parameters</h4>
															<ul class="tsd-parameters">
																<li>
																	<h5>cancel: <span class="tsd-signature-type">Canceler</span></h5>
																</li>
															</ul>
															<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
														</li>
													</ul>
												</li>
											</ul>
										</li>
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> on<wbr>Upload<wbr>Progress<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
											<ul class="tsd-parameters">
												<li class="tsd-parameter-signature">
													<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-variable">
														<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
													</ul>
													<ul class="tsd-descriptions">
														<li class="tsd-description">
															<h4 class="tsd-parameters-title">Parameters</h4>
															<ul class="tsd-parameters">
																<li>
																	<h5>e: <span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></h5>
																	<ul class="tsd-parameters">
																		<li class="tsd-parameter">
																			<h5>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
																		</li>
																		<li class="tsd-parameter">
																			<h5>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
																		</li>
																		<li class="tsd-parameter">
																			<h5>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
																		</li>
																	</ul>
																</li>
															</ul>
															<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
														</li>
													</ul>
												</li>
											</ul>
										</li>
									</ul>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> modelName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>code<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fileName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fullUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="waitforlogin" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> wait<wbr>For<wbr>Login</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">wait<wbr>For<wbr>Login<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">[</span><a href="../globals.html#voidfunc" class="tsd-signature-type">voidFunc</a><span class="tsd-signature-symbol">, </span><a href="../globals.html#voidfunc" class="tsd-signature-type">voidFunc</a><span class="tsd-signature-symbol">]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/index/index.ts:293</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">[</span><a href="../globals.html#voidfunc" class="tsd-signature-type">voidFunc</a><span class="tsd-signature-symbol">, </span><a href="../globals.html#voidfunc" class="tsd-signature-type">voidFunc</a><span class="tsd-signature-symbol">]</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="uniplatsdk.html" class="tsd-kind-icon">Uniplat<wbr>Sdk</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class">
								<a href="uniplatsdk.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="uniplatsdk.html#_isloggedin" class="tsd-kind-icon">_is<wbr>Loggedin</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="uniplatsdk.html#config" class="tsd-kind-icon">config</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class">
								<a href="uniplatsdk.html#configurationapi" class="tsd-kind-icon">configuration<wbr>Api</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class">
								<a href="uniplatsdk.html#custompluginprovider" class="tsd-kind-icon">custom<wbr>Plugin<wbr>Provider</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class">
								<a href="uniplatsdk.html#events" class="tsd-kind-icon">events</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class">
								<a href="uniplatsdk.html#global" class="tsd-kind-icon">global</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class">
								<a href="uniplatsdk.html#mediacontroller" class="tsd-kind-icon">media<wbr>Controller</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="uniplatsdk.html#seed" class="tsd-kind-icon">seed</a>
							</li>
							<li class=" tsd-kind-get-signature tsd-parent-kind-class">
								<a href="uniplatsdk.html#isloggedin" class="tsd-kind-icon">is<wbr>Logged<wbr>In</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="uniplatsdk.html#afterlogin" class="tsd-kind-icon">after<wbr>Login</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#connect" class="tsd-kind-icon">connect</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#disconnect" class="tsd-kind-icon">disconnect</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#domainservice" class="tsd-kind-icon">domain<wbr>Service</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#downloadfile" class="tsd-kind-icon">download<wbr>File</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="uniplatsdk.html#get" class="tsd-kind-icon">get</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#getauthlogin" class="tsd-kind-icon">get<wbr>Auth<wbr>Login</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#getaxios" class="tsd-kind-icon">get<wbr>Axios</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#getbindaccountmethods" class="tsd-kind-icon">get<wbr>Bind<wbr>Account<wbr>Methods</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#getloglist" class="tsd-kind-icon">get<wbr>Log<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#getoauthmethods" class="tsd-kind-icon">getOAuth<wbr>Methods</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#getpassportlogin" class="tsd-kind-icon">get<wbr>Passport<wbr>Login</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#getsseconnectivity" class="tsd-kind-icon">getSSEConnectivity</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#getuserinfo" class="tsd-kind-icon">get<wbr>User<wbr>Info</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#getuserinfobyjwt" class="tsd-kind-icon">get<wbr>User<wbr>Info<wbr>ByJwt</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#getverifyimage" class="tsd-kind-icon">get<wbr>Verify<wbr>Image</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#getverifyimageandseed" class="tsd-kind-icon">get<wbr>Verify<wbr>Image<wbr>And<wbr>Seed</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#injectdependency" class="tsd-kind-icon">inject<wbr>Dependency</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="uniplatsdk.html#loggedin" class="tsd-kind-icon">loggedin</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="uniplatsdk.html#loggedout" class="tsd-kind-icon">loggedout</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#login" class="tsd-kind-icon">login</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#loginbytoken" class="tsd-kind-icon">login<wbr>ByToken</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#logout" class="tsd-kind-icon">logout</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#model" class="tsd-kind-icon">model</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#org" class="tsd-kind-icon">org</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="uniplatsdk.html#post" class="tsd-kind-icon">post</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="uniplatsdk.html#proxyevents" class="tsd-kind-icon">proxy<wbr>Events</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#report" class="tsd-kind-icon">report</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#scene" class="tsd-kind-icon">scene</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#setinitdata" class="tsd-kind-icon">set<wbr>Init<wbr>Data</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#setunactivetimeout" class="tsd-kind-icon">set<wbr>UnActive<wbr>Timeout</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#uploadfile" class="tsd-kind-icon">upload<wbr>File</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#uploadfileforuniapp" class="tsd-kind-icon">upload<wbr>File<wbr>For<wbr>Uni<wbr>App</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#uploadfileforwxapp" class="tsd-kind-icon">upload<wbr>File<wbr>For<wbr>WxApp</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#uploadfileothers" class="tsd-kind-icon">upload<wbr>File<wbr>Others</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="uniplatsdk.html#uploadfilev2" class="tsd-kind-icon">upload<wbr>File<wbr>V2</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="uniplatsdk.html#waitforlogin" class="tsd-kind-icon">wait<wbr>For<wbr>Login</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>