import axios from "../../core/axios/index"
import type * as dto from "../../def/index"

export default class SceneApi {
    public getSceneDatas(scenes: dto.Index.Scene[]) {
        return axios.get<dto.SceneTypes.SceneDatas>(
            `general/entrances/scene/datas?scenes=${encodeURIComponent(
                JSON.stringify(scenes)
            )}`
        )
    }

    public loadList(params: dto.SceneTypes.LoadListApiParams) {
        return axios.get<dto.SceneTypes.SceneDataList>(
            `general/entrances/scene/${
                params.scene
            }/datalist?keyword=${encodeURIComponent(
                params.keyword
            )}&limitBegin=${params.itemIndex}&limitSize=${
                params.itemSize
            }&parent=${params.parent || ""}`
        )
    }
}
