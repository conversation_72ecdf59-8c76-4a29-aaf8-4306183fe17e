import axios from "../../core/axios/index"
import type * as dto from "../../def/index"

export default class PasswordBoxApi {
    public query() {
        return axios.get<string[]>(`general/passwordbox/list`)
    }
    public delete(passwordName: string) {
        const formdata = new FormData()
        formdata.append("name", passwordName)
        return axios.post<void>(`general/passwordbox/delete`, formdata)
    }
    public add(params: dto.PasswordBoxTypes.addAPIParams) {
        const formdata = new FormData()
        formdata.append("name", params.name)
        formdata.append("name", params.password)
        return axios.post<void>(`general/passwordbox/add`, formdata)
    }
}
