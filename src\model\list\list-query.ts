import isEqual from "lodash/isEqual"

import { isSimpleRelationshipFilter } from "../../core/filters-filter"
import { getAllFilters } from "../../core/tools/filter"
import type * as dto from "../../def/index"

import { listApi, listApi2 } from "./api"
export type PagesColumns = {
    [pageName: string]: dto.column[]
}
export class ListQuery<RowType extends dto.ListRow = dto.ListRow> {
    private list_name: string
    private shownRows: dto.metaRow<RowType>[] = []
    private keyFieldValue?: string
    public rawFilters?: dto.metaFilter[]
    private pagesColumns: PagesColumns

    private currentPage?: {
        index: number
        size: number
    }

    private isMeta = false

    constructor(
        private api: listApi,
        listData: dto.ListTypes.constructor & {
            pagesColumns: {
                [pageName: string]: dto.column[]
            }
        },
        private listQueryParams: dto.ListTypes.fullfilledQueryProps,
        private doneCallback: (
            data: dto.ListTypes.IQueryResult<RowType>
        ) => void,
        private failedCallback: (e: string) => void,
        isMeta?: boolean
    ) {
        this.list_name = listData.list_name ?? ""
        this.pagesColumns = listData.pagesColumns
        this.isMeta = isMeta || false
        this.init()
    }

    public getSpecificPageMeta(params: dto.ListTypes.setColumnsForPagesParams) {
        return this.api.getPagesMeta({
            ...params,
            list_name: this.list_name,
            prefilters: this.listQueryParams.prefilters,
        })
    }

    public async updateFilterParam(
        property: string,
        filters: dto.filter[],
        item_index: number,
        item_size: number
    ) {
        const parameter = {
            item_index,
            item_size,
            name: this.list_name,
            prefilters: this.listQueryParams.prefilters,
            columns: this.listQueryParams.columns,
            order_obj: this.listQueryParams.order_obj,
            filters: getAllFilters(filters),
            tagFilters: this.listQueryParams.tagFilters,
            sorts: this.listQueryParams.sorts,
        }
        if (this.listQueryParams.fields) {
            Object.assign(parameter, { fields: this.listQueryParams.fields })
        }
        const data = await this.api.updateFilterParam(property, parameter)
        this.rawFilters =
            this.rawFilters?.map((cachedFilter) => {
                if (isSimpleRelationshipFilter(cachedFilter)) {
                    return cachedFilter
                }
                const humbleFilter = cachedFilter
                const newFilter = data.find(
                    (k) => humbleFilter.property === k.property
                )
                if (newFilter == null) return humbleFilter
                return {
                    ...humbleFilter,
                    ...newFilter,
                }
            }) ?? []
        return data
    }

    public getAllShownRowsKeys() {
        if (this.keyFieldValue == null) return []
        return this.shownRows.map((k) => {
            if (this.keyFieldValue == null) {
                throw new Error("不要乱修改上边的值")
            }
            return k[this.keyFieldValue].value
        })
    }

    private async getSpecificTabList(params: {
        tabName: string
        item_index: number
        item_size: number
    }) {
        const item_size = params.item_size
        const filterParams = getAllFilters(
            this.listQueryParams.filters,
            this.listQueryParams.slave_filters
        )

        const parametersObj = {
            item_index: params.item_index,
            item_size,
            columns: this.pagesColumns[params.tabName],
            name: this.list_name,
            filters: filterParams,
            prefilters: this.listQueryParams.prefilters,
            order_obj: this.listQueryParams.order_obj,
            tagFilters: this.listQueryParams.tagFilters,
            sorts: this.listQueryParams.sorts,
            workflowType: this.listQueryParams.workflowType,
            tabName: this.listQueryParams.tabName,
        }
        if (this.listQueryParams.fields) {
            Object.assign(parametersObj, {
                fields: this.listQueryParams.fields,
            })
        }
        const data = await this.api.getListDataByTab<RowType>(
            params.tabName,
            parametersObj
        )
        this.updateCurrentPage(params.item_index, item_size)
        this.shownRows = data.rows
        return data
    }

    private async getSingleTabPageList(item_index: number, item_size: number) {
        const filterParams = getAllFilters(
            this.listQueryParams.filters,
            this.listQueryParams.slave_filters
        )
        // const item_size = this.listQueryParams.item_size
        const parametersObj = {
            item_index,
            item_size,
            name: this.list_name,
            filters: filterParams,
            prefilters: this.listQueryParams.prefilters,
            order_obj: this.listQueryParams.order_obj,
            columns: this.listQueryParams.columns,
            tagFilters: this.listQueryParams.tagFilters,
            sorts: this.listQueryParams.sorts,
            workflowType: this.listQueryParams.workflowType,
            tabName: this.listQueryParams.tabName,
        }
        if (this.listQueryParams.fields) {
            Object.assign(parametersObj, {
                fields: this.listQueryParams.fields,
            })
        }
        const data = await this.api.getListDataOfSinglePage<RowType>(
            parametersObj
        )
        this.updateCurrentPage(item_index, item_size)
        this.shownRows = data.rows
        return data
    }
    private updateCurrentPage(index: number, size: number) {
        this.currentPage = {
            index,
            size,
        }
    }

    public isInFirstPage() {
        if (this.currentPage == null) return false
        return this.currentPage.index <= this.currentPage.size
    }

    private isGetSpecificTabListFuncType(
        func:
            | dto.ListTypes.getSpecificTabListFuncType
            | dto.ListTypes.getSingleTabPageListFuncType
    ): func is dto.ListTypes.getSpecificTabListFuncType {
        return func.length === 3
    }

    private async init() {
        const parametersObj = {
            name: this.list_name,
            item_index: this.listQueryParams.item_index,
            item_size: this.listQueryParams.item_size,
            prefilters: this.listQueryParams.prefilters,
            columns: this.listQueryParams.columns,
            order_obj: this.listQueryParams.order_obj,
            filters: Array.isArray(this.listQueryParams.filters)
                ? getAllFilters(
                      this.listQueryParams.filters.map((k) => ({
                          ...k,
                          visible: true,
                      }))
                  )
                : this.listQueryParams.filters,
            tagFilters: this.listQueryParams.tagFilters,
            sorts: this.listQueryParams.sorts,
            workflowType: this.listQueryParams.workflowType,
            tabName: this.listQueryParams.tabName,
            filters4Workflow: this.listQueryParams.filters4Workflow,
            router: this.listQueryParams.router,
        }
        if (this.listQueryParams.fields) {
            Object.assign(parametersObj, {
                fields: this.listQueryParams.fields,
            })
        }
        let query:
            | Promise<dto.ListTypes.getListDataRequestResult<RowType>>
            | Promise<dto.ListTypes.BaseListDataRequestResult>
        if (this.isMeta) {
            query = this.api.getListMeta(parametersObj)
        } else {
            query = this.api.getListData<RowType>(parametersObj)
        }
        const pageData = (await query.catch((e) =>
            this.failedCallback(e)
        )) as dto.ListTypes.getListDataRequestResult<RowType>
        if (!pageData) return
        this.updateCurrentPage(
            this.listQueryParams.item_index,
            this.listQueryParams.item_size
        )

        const meta = pageData.meta

        // 替换多标签页时的meta
        if (meta.pages) {
            // pagesColumns保存了每个页签的表头设置
            // 如果设置了表头则：
            // 获取在设置了表头之后，每个页签的专属field_groups
            // field_groups用于页面显示表头。
            await Promise.all(
                meta.pages.map(async (page) => {
                    const columns = this.pagesColumns[page.name]
                    if (columns == null) return
                    if (isEqual(columns, ["*"])) return
                    const { field_groups } = await this.getSpecificPageMeta({
                        page_name: page.name,
                        columns,
                    })
                    page.field_groups = field_groups
                })
            )
        }

        if (pageData.rows.length > 0) {
            this.shownRows = pageData.rows
        }
        this.keyFieldValue = meta.key_field

        let _getList:
            | dto.ListTypes.getSingleTabPageListFuncType<RowType>
            | dto.ListTypes.getSpecificTabListFuncType<RowType>
        if (meta.pages && meta.pages.length) {
            _getList = async (
                tabName: string,
                item_index: number,
                item_size: number
            ) => {
                let currentPage
                if (pageData.page_datas) {
                    currentPage = Object.values(pageData.page_datas).find(
                        (k) => k.name === tabName
                    )
                }
                return await this.getSpecificTabList({
                    tabName: currentPage ? currentPage.name : tabName,
                    item_index,
                    item_size,
                })
            }
        } else {
            _getList = this.getSingleTabPageList.bind(this)
        }

        function getList(
            tabName: string,
            item_index: number,
            item_size?: number,
            listQueryParams?: dto.ListTypes.queryProps
        ): Promise<dto.ListTypes.getListDataByTabRequestResult<RowType> | null>
        function getList(
            item_index: number,
            item_size?: number,
            listQueryParams?: dto.ListTypes.queryProps
        ): Promise<dto.ListTypes.getListDataOfSinglePageRequestResult<RowType>>
        function getList(
            this: ListQuery,
            a: string | number,
            b?: number,
            c?: number | dto.ListTypes.queryProps,
            d?: dto.ListTypes.queryProps
        ):
            | Promise<dto.ListTypes.getListDataByTabRequestResult<RowType> | null>
            | Promise<
                  dto.ListTypes.getListDataOfSinglePageRequestResult<RowType>
              > {
            if (typeof a === "string") {
                const tabName = a
                const item_index = b ?? 0
                const item_size = c ?? this.listQueryParams.item_size
                if (d) {
                    this.listQueryParams = this.fullfillParams(d)
                }
                if (
                    this.isGetSpecificTabListFuncType(_getList) &&
                    typeof item_size === "number"
                ) {
                    return _getList(tabName, item_index, item_size)
                } else {
                    throw new Error("不可能走到这里来")
                }
            } else {
                const item_size = b ?? this.listQueryParams.item_size
                if (typeof c !== "number") {
                    c &&
                        (this.listQueryParams = this.fullfillParams(
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            c || ({} as any)
                        ))
                }
                if (!this.isGetSpecificTabListFuncType(_getList)) {
                    return _getList(a, item_size)
                } else {
                    throw new Error("不可能走到这里来")
                }
            }
        }
        this.rawFilters = pageData.meta.filters
        this.doneCallback({
            pageData: pageData,
            getList: getList.bind(this),
        })
    }

    private isPageIndex(
        params: dto.ListTypes.queryProps
    ): params is dto.ListTypes.queryPropsPage {
        return (params as dto.ListTypes.queryPropsPage).pageIndex != null
    }

    public fullfillParams(params: dto.ListTypes.queryProps) {
        const result = {
            ...params,
            prefilters: params.prefilters || [],
            columns: params.columns || ["*"],
            order_obj: params.order_obj || {},
            filters: params.filters || [],
            tagFilters: params.tagFilters || [],
            sorts: params.sorts || [],
        }

        if (params.fields) {
            result.fields = params.fields
        }

        if (this.isPageIndex(params)) {
            if (params.pageIndex <= 0) {
                throw new Error("页码不能小于1")
            }
            const item_index = (params.pageIndex - 1) * params.item_size
            return {
                ...result,
                item_index,
            } as dto.ListTypes.fullfilledQueryProps
        }
        return result as dto.ListTypes.fullfilledQueryProps
    }

    public getPageCounts(listQueryParams) {
        const params = {
            name: this.list_name,
            ...listQueryParams,
            filters: getAllFilters(
                listQueryParams.filters.map((k) => ({ ...k, visible: true })),
                listQueryParams.slave_filters
            ),
        }
        return this.api.getPageRecordCount(params)
    }

    public updateListName(name: string) {
        this.list_name = name
        return this
    }
}

export class ListQuery2 {
    private list_name: string
    private shownRows: dto.metaRow2[] = []
    public rawFilters?: dto.metaFilter[]
    private pagesColumns: PagesColumns

    private currentPage?: {
        index: number
        size: number
    }

    private isMeta = false

    constructor(
        private api: listApi2,
        listData: dto.ListTypes.constructor & {
            pagesColumns: {
                [pageName: string]: dto.column[]
            }
        },
        private listQueryParams: dto.ListTypes.fullfilledQueryProps,
        private doneCallback: (data: dto.ListTypes.IQueryResult2) => void,
        private failedCallback: (e: string) => void,
        isMeta?: boolean
    ) {
        this.list_name = listData.list_name ?? ""
        this.pagesColumns = listData.pagesColumns
        this.isMeta = isMeta || false
        this.init()
    }

    public getSpecificPageMeta(params: dto.ListTypes.setColumnsForPagesParams) {
        return this.api.getPagesMeta({
            ...params,
            list_name: this.list_name,
            prefilters: this.listQueryParams.prefilters,
        })
    }

    public async updateFilterParam(
        property: string,
        filters: dto.filter[],
        item_index: number,
        item_size: number
    ) {
        const parameter = {
            item_index,
            item_size,
            name: this.list_name,
            prefilters: this.listQueryParams.prefilters,
            columns: this.listQueryParams.columns,
            order_obj: this.listQueryParams.order_obj,
            filters: getAllFilters(filters, this.listQueryParams.slave_filters),
            tagFilters: this.listQueryParams.tagFilters,
            sorts: this.listQueryParams.sorts,
        }
        if (this.listQueryParams.fields) {
            Object.assign(parameter, { fields: this.listQueryParams.fields })
        }
        const data = await this.api.updateFilterParam(property, parameter)
        this.rawFilters =
            this.rawFilters?.map((cachedFilter) => {
                if (isSimpleRelationshipFilter(cachedFilter)) {
                    return cachedFilter
                }
                const humbleFilter = cachedFilter
                const newFilter = data.find(
                    (k) => humbleFilter.property === k.property
                )
                if (newFilter == null) return humbleFilter
                return {
                    ...humbleFilter,
                    ...newFilter,
                }
            }) ?? []
        return data
    }

    public getAllShownRowsKeys() {
        return this.shownRows.map((k) => k.keyValue)
    }

    private async getSpecificTabList(params: {
        tabName: string
        item_index: number
        item_size: number
    }) {
        const item_size = params.item_size
        const filterParams = getAllFilters(
            this.listQueryParams.filters,
            this.listQueryParams.slave_filters
        )

        const parametersObj = {
            item_index: params.item_index,
            item_size,
            columns: this.pagesColumns[params.tabName],
            name: this.list_name,
            filters: filterParams,
            prefilters: this.listQueryParams.prefilters,
            order_obj: this.listQueryParams.order_obj,
            tagFilters: this.listQueryParams.tagFilters,
            sorts: this.listQueryParams.sorts,
            workflowType: this.listQueryParams.workflowType,
            tabName: this.listQueryParams.tabName,
        }
        if (this.listQueryParams.fields) {
            Object.assign(parametersObj, {
                fields: this.listQueryParams.fields,
            })
        }
        const data = await this.api.getListDataByTab(
            params.tabName,
            parametersObj
        )
        this.updateCurrentPage(params.item_index, item_size)
        this.shownRows = data.rows
        return data
    }

    private async getSingleTabPageList(item_index: number, item_size: number) {
        const filterParams = getAllFilters(
            this.listQueryParams.filters,
            this.listQueryParams.slave_filters
        )
        const parametersObj = {
            item_index,
            item_size,
            name: this.list_name,
            filters: filterParams,
            prefilters: this.listQueryParams.prefilters,
            order_obj: this.listQueryParams.order_obj,
            columns: this.listQueryParams.columns,
            tagFilters: this.listQueryParams.tagFilters,
            sorts: this.listQueryParams.sorts,
            workflowType: this.listQueryParams.workflowType,
            tabName: this.listQueryParams.tabName,
        }
        if (this.listQueryParams.fields) {
            Object.assign(parametersObj, {
                fields: this.listQueryParams.fields,
            })
        }
        const data = await this.api.getListDataOfSinglePage(parametersObj)
        this.updateCurrentPage(item_index, item_size)
        this.shownRows = data.rows
        return data
    }
    private updateCurrentPage(index: number, size: number) {
        this.currentPage = {
            index,
            size,
        }
    }

    public isInFirstPage() {
        if (this.currentPage == null) return false
        return this.currentPage.index <= this.currentPage.size
    }

    private isGetSpecificTabListFuncType(
        func:
            | dto.ListTypes.getSpecificTabListFuncType2
            | dto.ListTypes.getSingleTabPageListFuncType2
    ): func is dto.ListTypes.getSpecificTabListFuncType2 {
        return func.length === 3
    }

    private async init() {
        const parametersObj = {
            name: this.list_name,
            item_index: this.listQueryParams.item_index,
            item_size: this.listQueryParams.item_size,
            prefilters: this.listQueryParams.prefilters,
            columns: this.listQueryParams.columns,
            order_obj: this.listQueryParams.order_obj,
            filters: Array.isArray(this.listQueryParams.filters)
                ? getAllFilters(
                      this.listQueryParams.filters.map((k) => ({
                          ...k,
                          visible: true,
                      }))
                  )
                : this.listQueryParams.filters,
            tagFilters: this.listQueryParams.tagFilters,
            sorts: this.listQueryParams.sorts,
            workflowType: this.listQueryParams.workflowType,
            tabName: this.listQueryParams.tabName,
            filters4Workflow: this.listQueryParams.filters4Workflow,
            router: this.listQueryParams.router,
        }
        if (this.listQueryParams.fields) {
            Object.assign(parametersObj, {
                fields: this.listQueryParams.fields,
            })
        }
        let query:
            | Promise<dto.ListTypes.getListDataRequestResult2>
            | Promise<dto.ListTypes.BaseListDataRequestResult>
        if (this.isMeta) {
            query = this.api.getListMeta(parametersObj)
        } else {
            query = this.api.getListData(parametersObj)
        }
        const pageData = (await query.catch((e) =>
            this.failedCallback(e)
        )) as dto.ListTypes.getListDataRequestResult2
        if (!pageData) return
        this.updateCurrentPage(
            this.listQueryParams.item_index,
            this.listQueryParams.item_size
        )

        const meta = pageData.meta

        // 替换多标签页时的meta
        if (meta.pages) {
            // pagesColumns保存了每个页签的表头设置
            // 如果设置了表头则：
            // 获取在设置了表头之后，每个页签的专属field_groups
            // field_groups用于页面显示表头。
            await Promise.all(
                meta.pages.map(async (page) => {
                    const columns = this.pagesColumns[page.name]
                    if (columns == null) return
                    if (isEqual(columns, ["*"])) return
                    const { field_groups } = await this.getSpecificPageMeta({
                        page_name: page.name,
                        columns,
                    })
                    page.field_groups = field_groups
                })
            )
        }

        if (pageData.rows.length > 0) {
            this.shownRows = pageData.rows
        }

        let _getList:
            | dto.ListTypes.getSingleTabPageListFuncType2
            | dto.ListTypes.getSpecificTabListFuncType2
        if (meta.pages && meta.pages.length) {
            _getList = async (
                tabName: string,
                item_index: number,
                item_size: number
            ) => {
                let currentPage
                if (pageData.pageDatas) {
                    currentPage = Object.values(pageData.pageDatas).find(
                        (k) => k.name === tabName
                    )
                }
                return await this.getSpecificTabList({
                    tabName: currentPage ? currentPage.name : tabName,
                    item_index,
                    item_size,
                })
            }
        } else {
            _getList = this.getSingleTabPageList.bind(this)
        }

        function getList(
            tabName: string,
            item_index: number,
            item_size?: number,
            listQueryParams?: dto.ListTypes.queryProps
        ): Promise<dto.ListTypes.getListDataByTabRequestResult2 | null>
        function getList(
            item_index: number,
            item_size?: number,
            listQueryParams?: dto.ListTypes.queryProps
        ): Promise<dto.ListTypes.getListDataOfSinglePageRequestResult2>
        function getList(
            this: ListQuery2,
            a: string | number,
            b?: number,
            c?: number | dto.ListTypes.queryProps,
            d?: dto.ListTypes.queryProps
        ):
            | Promise<dto.ListTypes.getListDataByTabRequestResult2 | null>
            | Promise<dto.ListTypes.getListDataOfSinglePageRequestResult2> {
            if (typeof a === "string") {
                const tabName = a
                const item_index = b ?? 0
                const item_size = c ?? this.listQueryParams.item_size
                if (d) {
                    this.listQueryParams = this.fullfillParams(d)
                }
                if (
                    this.isGetSpecificTabListFuncType(_getList) &&
                    typeof item_size === "number"
                ) {
                    return _getList(tabName, item_index, item_size)
                } else {
                    throw new Error("不可能走到这里来")
                }
            } else {
                const item_size = b ?? this.listQueryParams.item_size
                if (typeof c !== "number") {
                    c &&
                        (this.listQueryParams = this.fullfillParams(
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            c || ({} as any)
                        ))
                }
                if (!this.isGetSpecificTabListFuncType(_getList)) {
                    return _getList(a, item_size)
                } else {
                    throw new Error("不可能走到这里来")
                }
            }
        }
        this.rawFilters = pageData.meta.filters
        this.doneCallback({
            pageData: pageData,
            getList: getList.bind(this),
        })
    }

    private isPageIndex(
        params: dto.ListTypes.queryProps
    ): params is dto.ListTypes.queryPropsPage {
        return (params as dto.ListTypes.queryPropsPage).pageIndex != null
    }

    public fullfillParams(params: dto.ListTypes.queryProps) {
        const result = {
            ...params,
            prefilters: params.prefilters || [],
            columns: params.columns || ["*"],
            order_obj: params.order_obj || {},
            filters: params.filters || [],
            tagFilters: params.tagFilters || [],
            sorts: params.sorts || [],
        }
        if (params.fields) {
            Object.assign(result, { fields: params.fields })
        }
        if (params.slave_filters) {
            Object.assign(result, { slave_filters: params.slave_filters })
        }
        if (this.isPageIndex(params)) {
            if (params.pageIndex <= 0) {
                throw new Error("页码不能小于1")
            }
            const item_index = (params.pageIndex - 1) * params.item_size
            return {
                ...result,
                item_index,
            } as dto.ListTypes.fullfilledQueryProps
        }
        return result as dto.ListTypes.fullfilledQueryProps
    }

    public getPageCounts(listQueryParams) {
        const params = {
            name: this.list_name,
            ...listQueryParams,
            filters: getAllFilters(
                listQueryParams.filters.map((k) => ({
                    ...k,
                    visible: true,
                })),
                listQueryParams.slave_filters
            ),
        }
        return this.api.getPageRecordCount(params)
    }

    public updateListName(name: string) {
        this.list_name = name
        return this
    }
}
