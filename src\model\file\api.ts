import axios from "../../core/axios/index"
import { getAllFilters } from "../../core/tools/filter"
import { encodeParams4Parameters } from "../../core/tools/form"
import type * as dto from "../../def/index"

export default class FileApi {
    constructor(private modelName: string) {}

    public async fetchFileDomainTree() {
        return await axios.get<dto.FileModel.DomainTree[]>(
            `/general/file/domainTree`
        )
    }
    public async fetchFileDatasources() {
        return await axios.get<dto.FileModel.DataSources[]>(
            `/general/file/datasources`
        )
    }

    public async fetchListMeta(parameters: dto.ListTypes.fullfilledQueryProps) {
        const parametersObj = {
            name: "",
            item_index: parameters.item_index,
            item_size: parameters.item_size,
            prefilters: parameters.prefilters,
            columns: parameters.columns,
            order_obj: parameters.order_obj,
            filters: getAllFilters(
                parameters.filters.map((k) => ({ ...k, visible: true }))
            ),
            tagFilters: parameters.tagFilters,
            sorts: parameters.sorts,
            workflowType: parameters.workflowType,
            tabName: parameters.tabName,
            filters4Workflow: parameters.filters4Workflow,
            router: parameters.router,
        }
        return await axios.get<dto.ListTypes.getListDataRequestResult>(
            `general/model/${
                this.modelName
            }/list/meta?${encodeParams4Parameters(parametersObj)}`
        )
    }
}
