import { AnonymousModel } from "../../core/anonymous-api"

import TagManagerApi from "./api"
export class TagManager extends AnonymousModel {
    protected api: TagManagerApi
    constructor(model_name: string) {
        super()
        this.api = new TagManagerApi(model_name)
    }

    public getTagGroups() {
        return this.api.getTagGroups()
    }

    public query(keyValue: string) {
        return this.api.query(keyValue)
    }
}
