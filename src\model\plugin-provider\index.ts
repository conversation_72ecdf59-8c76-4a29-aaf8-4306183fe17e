import axios from "../../core/axios/index"

export class PluginProvider {
    public post<TResult>(
        model: string,
        action: string,
        data?: { [key: string]: unknown }
    ) {
        const q: string[] = []
        if (data) {
            for (const p in data) {
                q.push(
                    `${p}=${encodeURIComponent(
                        JSON.stringify(data[p] as Record<string, unknown>)
                    )}`
                )
            }
        }
        return axios.post<TResult>(
            `/general/model/${model}/custom/${action}?${q.join("&")}`
        )
    }

    public get<TResult>(
        model: string,
        action: string,
        parameters?: { [key: string]: number | string }
    ) {
        const q: string[] = []
        if (parameters) {
            for (const p in parameters) {
                q.push(`${p}=${encodeURIComponent(parameters[p])}`)
            }
        }
        return axios.get<TResult>(
            `/general/model/${model}/custom/${action}?${q.join("&")}`
        )
    }
}
