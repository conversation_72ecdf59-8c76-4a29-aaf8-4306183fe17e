// type WantedClass = (channelName: string) => void;
import type * as dto from "../def/index"
type wantedClassInstance = {
    postMessage: (msg: dto.SSE.msg) => void
    onmessage: (e: dto.SSE.msg) => void
}
type WantedClass = {
    new (channelName: string): wantedClassInstance
}
function Channel() {
    let wantedClass: WantedClass
    return {
        save(wanted: WantedClass) {
            wantedClass = wanted
        },
        getClass() {
            return wantedClass
        },
    }
}

export const channel = Channel()
