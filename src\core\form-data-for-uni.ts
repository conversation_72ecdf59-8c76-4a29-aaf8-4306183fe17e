// const mimeMap = require('./mimeMap.js')
// import mimeMap from "./mimeMap"

type Data = {
    [key: string]: string
}

export default class FormDataForUni {
    public data: Data = {}

    public append = (name: string, value: string) => {
        this.data[name] = value
        return true
    }

    public getData = () => convert(this.data)
}

function convert(data: Data) {
    const boundaryKey = "wxmpFormBoundary" + randString() // 数据分割符，一般是随机的字符串
    const boundary = "--" + boundaryKey
    const endBoundary = boundary + "--"

    let postArray: number[] = []
    //拼接参数
    if (data && Object.prototype.toString.call(data) == "[object Object]") {
        for (const key in data) {
            postArray = postArray.concat(
                formDataArray(boundary, key, data[key])
            )
        }
    }
    //结尾
    const endBoundaryArray: number[] = []
    for (let i = 0; i < endBoundary.length; i++) {
        // 最后取出结束boundary的charCode
        const d = utf8CodeAt(endBoundary, i)
        endBoundaryArray.push(...d)
    }
    postArray = postArray.concat(endBoundaryArray)
    return {
        contentType: "multipart/form-data; boundary=" + boundaryKey,
        buffer: new Uint8Array(postArray).buffer,
    }
}

function randString() {
    let res = ""
    for (let i = 0; i < 17; i++) {
        const n = Math.random() * 62
        if (n <= 9) {
            res += n
        } else if (n <= 35) {
            res += String.fromCharCode(n + 55)
        } else {
            res += String.fromCharCode(n + 61)
        }
    }
    return res
}

function formDataArray(boundary: string, name: string, value: string) {
    let dataString = ""

    dataString += boundary + "\r\n"
    dataString += 'Content-Disposition: form-data; name="' + name + '"'
    dataString += "\r\n\r\n"
    dataString += value

    const dataArray: number[] = []
    for (let i = 0; i < dataString.length; i++) {
        dataArray.push(...utf8CodeAt(dataString, i))
    }

    dataArray.push(...utf8CodeAt("\r", 0))
    dataArray.push(...utf8CodeAt("\n", 0))

    return dataArray
}

function utf8CodeAt(str: string, i: number) {
    const out: number[] = []
    let p = 0
    let c = str.charCodeAt(i)
    if (c < 128) {
        out[p++] = c
    } else if (c < 2048) {
        out[p++] = (c >> 6) | 192
        out[p++] = (c & 63) | 128
    } else if (
        (c & 0xfc00) == 0xd800 &&
        i + 1 < str.length &&
        (str.charCodeAt(i + 1) & 0xfc00) == 0xdc00
    ) {
        // Surrogate Pair
        c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff)
        out[p++] = (c >> 18) | 240
        out[p++] = ((c >> 12) & 63) | 128
        out[p++] = ((c >> 6) & 63) | 128
        out[p++] = (c & 63) | 128
    } else {
        out[p++] = (c >> 12) | 224
        out[p++] = ((c >> 6) & 63) | 128
        out[p++] = (c & 63) | 128
    }
    return out
}
