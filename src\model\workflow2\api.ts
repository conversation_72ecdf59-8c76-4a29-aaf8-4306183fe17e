import axios from "../../core/axios/index"
import { global } from "../../core/global"
import { encodeParams, encodeParams4Parameters } from "../../core/tools/form"
import type * as dto from "../../def/index"

import {
    ChangeMasterRequest,
    CreateProcessRequest,
    EditProcessRequest,
    ProcessConfigInfo,
    ProcessDetail2,
    ProcessDetail3,
    TaskDefaultDealerRequest,
    WorkflowBatchRequest,
    WorkflowUser,
} from "./model"
import { CheckEditParam } from "./workflow2"

const enum WorkflowParamsType {
    State = "state",
    Task = "task",
}
export default class Workflow2Api {
    constructor(private modelName: string) {}

    public queryWorkflowDealerList2() {
        return axios.post<Array<WorkflowUser>>(
            `general/workflow2/${this.modelName}/dealer/list2`
        )
    }

    /**
     * 工作流批量处理
     */
    public workflowBatchRequest(request: WorkflowBatchRequest) {
        return axios.post<WorkflowUser>(
            `general/workflow2/${this.modelName}/workflow/batch/`,
            request
        )
    }

    /**
     * 获取任务的默认处理人
     */
    public getTaskDefaultDealer(request: TaskDefaultDealerRequest) {
        return axios.post<WorkflowUser>(
            `general/workflow2/${this.modelName}/task/default/dealer`,
            request
        )
    }

    public changeMaster(request: ChangeMasterRequest) {
        return axios.post<boolean>(
            `general/workflow2/${this.modelName}/master/change`,
            request
        )
    }

    private buildParams(param: string) {
        return {
            params: param ? { params: encodeParams(param) } : {},
        }
    }

    public queryWorkflowDealerList(param: string) {
        return axios.post<Array<WorkflowUser>>(
            `general/workflow2/dealer/list`,
            {},
            this.buildParams(param)
        )
    }

    public saveProcessDef2(request: EditProcessRequest) {
        return axios.post<boolean>(
            `general/workflow2/${this.modelName}/process/save`,
            request
        )
    }

    public createProcessDef2(request: CreateProcessRequest) {
        return axios.post<boolean>(
            `general/workflow2/${this.modelName}/process/create2`,
            request
        )
    }

    public processDefDetail(processName: string) {
        return axios.get<ProcessConfigInfo>(
            `general/workflow2/${this.modelName}/process/def`,
            { params: { processName } }
        )
    }

    /**
     * 请求撤回操作
     * @param processId 流程ID
     * @returns true 成功
     */
    public rollbackProcess(processId: number) {
        const param = { processId }
        return axios.post<boolean>(
            `general/workflow2/${this.modelName}/process/rollback`,
            param
        )
    }

    public queryProcessByAssociateId(associateId: string | number) {
        return axios.get<dto.workflow2.AssociateProcessInfo[]>(
            `general/workflow2/${this.modelName}/process/associateId/${associateId}`
        )
    }

    public updateProcessDesc(id: number, desc: string) {
        const requestBody = { processId: id, desc }
        return axios.post<boolean>(
            `general/workflow2/${this.modelName}/process/desc`,
            requestBody
        )
    }

    public updateSummary(id: number, summary: string) {
        const requestBody = { processId: id, summary }
        return axios.post<boolean>(
            `general/workflow2/${this.modelName}/process/summary`,
            requestBody
        )
    }

    public updateOnline(online: boolean, processName: string) {
        const params = `?online=${online}&processName=${processName}`
        return axios.get<boolean>(
            `general/workflow2/${this.modelName}/dealer/online${params}`
        )
    }

    public getAssociateId<T>(param: dto.workflow2.ListQueryParam) {
        return axios.post<T>(
            `general/workflow2/${this.modelName}/query/associateId`,
            param
        )
    }

    public checkStateEditable(param: CheckEditParam) {
        const params = `?params=${encodeParams(param)}`
        return axios.get<boolean>(
            `general/workflow2/${this.modelName}/state/editable${params}`
        )
    }

    public checkTaskEditable(param: CheckEditParam) {
        const params = `?params=${encodeParams(param)}`
        return axios.get<boolean>(
            `general/workflow2/${this.modelName}/task/editable${params}`
        )
    }

    public listProcessInstancePanel(associateId: number) {
        return axios.get<dto.workflow2.ProcessInstancePanel[]>(
            `general/workflow2/${this.modelName}/process/${associateId}/list`
        )
    }

    public listModelActions() {
        return axios.get<dto.workflow2.ActionInfo[]>(
            `general/workflow2/${this.modelName}/actions`
        )
    }

    public listAllProcessName() {
        return axios.get<string[]>(
            `general/workflow2/${this.modelName}/query/process/list`
        )
    }

    public listAllProcessNameWithRight() {
        return axios.get<string[]>(
            `general/workflow2/${this.modelName}/query/process/list/right`
        )
    }

    public listAllProcessDef() {
        return axios.get<dto.workflow2.ProcessInfo[]>(
            `general/workflow2/${this.modelName}/process/def/list`
        )
    }

    public addRemark(param: dto.workflow2.AddRemarkParam) {
        return axios.post<boolean>(
            `general/workflow2/${this.modelName}/remark/add`,
            param
        )
    }

    public queryDealerList<T>(processName: string, state: string) {
        return axios.get<T>(`general/workflow2/${this.modelName}/dealer/list`, {
            params: { processName, state },
        })
    }

    public startProcessBatch(
        param: dto.workflow2.BatchParam<dto.workflow2.StartProcessParam>
    ) {
        const params = `?params=${encodeParams(param)}`
        return axios.get<dto.workflow2.BatchResult>(
            `general/workflow2/${this.modelName}/process/start/batch${params}`
        )
    }

    public startProcessCheck(processName: string, ids: string) {
        const params = `?ids=${ids}`
        return axios.get<dto.workflow2.StartCheckResult>(
            `general/workflow2/${this.modelName}/process/${processName}/start/check${params}`
        )
    }

    public queryProcessExistCount(processName: string, ids: string) {
        const params = `?ids=${encodeParams(ids)}`
        return axios.get<number>(
            `general/workflow2/${this.modelName}/process/${processName}/exist/count${params}`
        )
    }

    public queryProcessWithCount(param: dto.workflow2.ListQueryParam) {
        const params = `?params=${encodeParams(param)}`
        return axios.get<dto.workflow2.ProcessWithCount[]>(
            `general/workflow2/${this.modelName}/process/count${params}`
        )
    }

    public list(param: dto.workflow2.ListQueryParam) {
        const params = `?params=${encodeParams(param)}`
        return axios.get<dto.workflow2.ListResult>(
            `general/workflow2/${this.modelName}/query/list${params}`
        )
    }

    /**
     * @deprecated 请使用detail3
     */
    public detail(processId: number, detailName: string) {
        return axios.get<dto.workflow2.ProcessDetail>(
            `general/workflow2/${this.modelName}/query/${processId}/detail/${detailName}`
        )
    }

    /**
     * @deprecated 请使用detail3
     */
    public detail2(processId: number, detailName: string) {
        return axios.get<ProcessDetail2>(
            `general/workflow2/${this.modelName}/query/${processId}/detail2/${detailName}`
        )
    }

    public detail3(processId: number, detailName: string) {
        return axios.get<ProcessDetail3>(
            `general/workflow2/${this.modelName}/query/${processId}/detail3/${detailName}`
        )
    }

    public listOperation(
        processId: number,
        param: dto.workflow2.listOperationParam
    ) {
        let params = ``
        if (param) {
            params = `?params=${encodeParams(param)}`
        }
        return axios.get<dto.workflow2.ProcessOperation[]>(
            `general/workflow2/${this.modelName}/query/${processId}/operation${params}`
        )
    }

    public listFinishTask(processId: number) {
        return axios.get<dto.workflow2.WorkRecord[]>(
            `general/workflow2/${this.modelName}/query/${processId}/record`
        )
    }

    public listRemark(processId: number) {
        return axios.get<dto.workflow2.Remark[]>(
            `general/workflow2/${this.modelName}/query/${processId}/remark`
        )
    }

    public getProcessInfo(processName: string) {
        return axios.get<dto.workflow2.ProcessInfo>(
            `general/workflow2/${this.modelName}/process/${processName}/info`
        )
    }

    public createProcess(processDef: dto.workflow2.ProcessEditParam) {
        return axios.post<boolean>(
            `general/workflow2/${this.modelName}/process/create`,
            processDef
        )
    }

    public editProcess(processDef: dto.workflow2.ProcessEditParam) {
        return axios.post<boolean>(
            `general/workflow2/${this.modelName}/process/edit`,
            processDef
        )
    }

    public startProcess(param: dto.workflow2.StartProcessParam) {
        const params = `?params=${encodeParams(param)}`
        return axios.get<boolean>(
            `general/workflow2/${this.modelName}/process/start${params}`
        )
    }

    public changeProcessState(param: dto.workflow2.ChangeProcessStateParam) {
        const params = `?params=${encodeParams(param)}`
        return axios.get<boolean>(
            `general/workflow2/${this.modelName}/state/change${params}`
        )
    }

    public changeProcessStateBatch(
        param: dto.workflow2.BatchParam<dto.workflow2.ChangeProcessStateParam>
    ) {
        const params = `?params=${encodeParams(param)}`
        return axios.get<dto.workflow2.BatchResult>(
            `general/workflow2/${this.modelName}/state/change/batch${params}`
        )
    }

    public createTask(param: dto.workflow2.UpdateTaskParam) {
        const params = `?params=${encodeParams(param)}`
        return axios.get<boolean>(
            `general/workflow2/${this.modelName}/task/create${params}`
        )
    }

    public createTaskBatch(
        param: dto.workflow2.BatchParam<dto.workflow2.UpdateTaskParam>
    ) {
        return axios.post<dto.workflow2.BatchResult>(
            `general/workflow2/${this.modelName}/task/create/batch`,
            param
        )
    }

    public editTask(param: dto.workflow2.UpdateTaskParam) {
        const params = `?params=${encodeParams(param)}`
        return axios.get<boolean>(
            `general/workflow2/${this.modelName}/task/edit${params}`
        )
    }

    public editTaskBatch(
        param: dto.workflow2.BatchParam<dto.workflow2.UpdateTaskParam>
    ) {
        return axios.post<dto.workflow2.BatchResult>(
            `general/workflow2/${this.modelName}/task/edit/batch`,
            param
        )
    }

    public changeTask(param: dto.workflow2.ChangeTaskParam) {
        const params = `?params=${encodeParams(param)}`
        return axios.get<boolean>(
            `general/workflow2/${this.modelName}/task/change${params}`
        )
    }

    public changeTaskBatch(
        param: dto.workflow2.BatchParam<dto.workflow2.ChangeTaskParam>
    ) {
        return axios.post<dto.workflow2.BatchResult>(
            `general/workflow2/${this.modelName}/task/change/batch`,
            param
        )
    }

    public cancelTask(param: dto.workflow2.CancelTaskParam) {
        const params = `?params=${encodeParams(param)}`
        return axios.get<boolean>(
            `general/workflow2/${this.modelName}/task/cancel${params}`
        )
    }

    public cancelTaskBatch(
        param: dto.workflow2.BatchParam<dto.workflow2.CancelTaskParam>
    ) {
        return axios.post<dto.workflow2.BatchResult>(
            `general/workflow2/${this.modelName}/task/cancel/batch`,
            param
        )
    }

    public finishTask(param: dto.workflow2.FinishTaskParam) {
        const params = `?params=${encodeParams(param)}`
        return axios.get<boolean>(
            `general/workflow2/${this.modelName}/task/finish${params}`
        )
    }

    public finishTaskBatch(
        param: dto.workflow2.BatchParam<dto.workflow2.FinishTaskParam>
    ) {
        return axios.post<dto.workflow2.BatchResult>(
            `general/workflow2/${this.modelName}/task/finish/batch`,
            param
        )
    }

    public homeTodoListCount(datasource: string) {
        return axios.post<number>(
            `general/workflow2/todo/count?datasource=${datasource}`
        )
    }

    public homeMasterListCount(datasource: string) {
        return axios.post<number>(
            `general/workflow2/master/count?datasource=${datasource}`
        )
    }

    public datasouceList() {
        return axios.post<Array<dto.workflow2.DatasourceWithCount>>(
            `general/workflow2/datasource/count`
        )
    }

    public homeCount(datasource: string) {
        return axios.post<dto.workflow2.HomeWorkflowCount>(
            `general/workflow2/home/<USER>
        )
    }

    public createExportUrl(parameters: dto.workflow2.exportExcelParams) {
        return `${global.baseUrl}general/workflow2/sse/${
            this.modelName
        }/export?${encodeParams4Parameters(parameters, true)}`
    }

    public getDefaultTemplateUrl(
        list_name: string | undefined,
        page_name?: string
    ) {
        const parameters: { name?: string; page_name: string } = {
            page_name,
        }
        if (list_name) {
            parameters.name = list_name
        }
        return axios.get<string>(
            `general/model/${
                this.modelName
            }/exportListTemplate?${encodeParams4Parameters(parameters)}`
        )
    }

    public updateStateFilterParam(processName: string): Promise<string[]> {
        return this.getWorkflowProcessList(
            WorkflowParamsType.State,
            processName
        )
    }

    public updateTaskFilterParam(processName: string): Promise<string[]> {
        return this.getWorkflowProcessList(WorkflowParamsType.Task, processName)
    }

    private getWorkflowProcessList(property: string, processName: string) {
        return axios.get<string[]>(
            `general/workflow2/${this.modelName}/query/${property}/list?processName=${processName}`
        )
    }
}
