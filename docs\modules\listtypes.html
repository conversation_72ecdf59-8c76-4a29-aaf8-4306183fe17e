<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>ListTypes | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="listtypes.html">ListTypes</a>
				</li>
			</ul>
			<h1>Namespace ListTypes</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Enumerations</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-enum tsd-parent-kind-namespace"><a href="../enums/listtypes.filtermatchtype.html" class="tsd-kind-icon">filter<wbr>Match<wbr>Type</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Type aliases</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#booleanfilter" class="tsd-kind-icon">Boolean<wbr>Filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#cascaderfilter" class="tsd-kind-icon">Cascader<wbr>Filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#checkboxfilter" class="tsd-kind-icon">Check<wbr>Boxfilter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#combinefulltextfilter" class="tsd-kind-icon">Combine<wbr>Fulltext<wbr>Filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#combotextfilter" class="tsd-kind-icon">Combo<wbr>Text<wbr>Filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#datebetweenfilter" class="tsd-kind-icon">Date<wbr>Between<wbr>Filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#datefilter" class="tsd-kind-icon">Date<wbr>Filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#enumfilter" class="tsd-kind-icon">Enumfilter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#enumradiofilter" class="tsd-kind-icon">Enumradio<wbr>Filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#fulltextfilter" class="tsd-kind-icon">Fulltext<wbr>Filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#ipage" class="tsd-kind-icon">IPage</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter"><a href="listtypes.html#iqueryresult" class="tsd-kind-icon">IQuery<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#keyvaluefilter" class="tsd-kind-icon">Key<wbr>Value<wbr>Filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#keyvaluefilters" class="tsd-kind-icon">Key<wbr>Value<wbr>Filters</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#numberfilter" class="tsd-kind-icon">Number<wbr>Filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#prefiltersobject" class="tsd-kind-icon">Prefilters<wbr>Object</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#searchfilter" class="tsd-kind-icon">Search<wbr>Filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#textdatafilter" class="tsd-kind-icon">Text<wbr>Data<wbr>Filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#textfilter" class="tsd-kind-icon">Text<wbr>Filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#textmonthfilter" class="tsd-kind-icon">Text<wbr>Month<wbr>Filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#treefilter" class="tsd-kind-icon">Tree<wbr>Filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#constructor" class="tsd-kind-icon">constructor</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#exportexcelparams" class="tsd-kind-icon">export<wbr>Excel<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#exporttoexcel" class="tsd-kind-icon">export<wbr>ToExcel</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#filtergroup" class="tsd-kind-icon">filter<wbr>Group</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#fullfilledqueryprops" class="tsd-kind-icon">fullfilled<wbr>Query<wbr>Props</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#getlistcountrequestresult" class="tsd-kind-icon">get<wbr>List<wbr>Count<wbr>Request<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#getlistdatabytabparams" class="tsd-kind-icon">get<wbr>List<wbr>Data<wbr>ByTab<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter"><a href="listtypes.html#getlistdatabytabrequestresult" class="tsd-kind-icon">get<wbr>List<wbr>Data<wbr>ByTab<wbr>Request<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#getlistdataofsinglepageparams" class="tsd-kind-icon">get<wbr>List<wbr>Data<wbr>OfSingle<wbr>Page<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter"><a href="listtypes.html#getlistdataofsinglepagerequestresult" class="tsd-kind-icon">get<wbr>List<wbr>Data<wbr>OfSingle<wbr>Page<wbr>Request<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter"><a href="listtypes.html#getlistdatarequestresult" class="tsd-kind-icon">get<wbr>List<wbr>Data<wbr>Request<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter"><a href="listtypes.html#getsingletabpagelistfunctype" class="tsd-kind-icon">get<wbr>Single<wbr>Tab<wbr>Page<wbr>List<wbr>Func<wbr>Type</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter"><a href="listtypes.html#getspecifictablistfunctype" class="tsd-kind-icon">get<wbr>Specific<wbr>Tab<wbr>List<wbr>Func<wbr>Type</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#getworkflow" class="tsd-kind-icon">get<wbr>Workflow</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#initprops" class="tsd-kind-icon">init<wbr>Props</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#queryprops" class="tsd-kind-icon">query<wbr>Props</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#querypropsbase" class="tsd-kind-icon">query<wbr>Props<wbr>Base</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#querypropseasy" class="tsd-kind-icon">query<wbr>Props<wbr>Easy</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#querypropshard" class="tsd-kind-icon">query<wbr>Props<wbr>Hard</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#querypropsindex" class="tsd-kind-icon">query<wbr>Props<wbr>Index</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#querypropspage" class="tsd-kind-icon">query<wbr>Props<wbr>Page</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#setcolumnsforpagesapiresult" class="tsd-kind-icon">set<wbr>Columns<wbr>For<wbr>Pages<wbr>Api<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#setcolumnsforpagesparams" class="tsd-kind-icon">set<wbr>Columns<wbr>For<wbr>Pages<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#updateaction" class="tsd-kind-icon">update<wbr>Action</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#updateactionrequestresult" class="tsd-kind-icon">update<wbr>Action<wbr>Request<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="listtypes.html#updatefilterparamrequestresult" class="tsd-kind-icon">update<wbr>Filter<wbr>Param<wbr>Request<wbr>Result</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Type aliases</h2>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="booleanfilter" class="tsd-anchor"></a>
					<h3>Boolean<wbr>Filter</h3>
					<div class="tsd-signature tsd-kind-icon">Boolean<wbr>Filter<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#keyvaluefilter" class="tsd-signature-type">KeyValueFilter</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1283</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="cascaderfilter" class="tsd-anchor"></a>
					<h3>Cascader<wbr>Filter</h3>
					<div class="tsd-signature tsd-kind-icon">Cascader<wbr>Filter<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#keyvaluefilter" class="tsd-signature-type">KeyValueFilter</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1291</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="checkboxfilter" class="tsd-anchor"></a>
					<h3>Check<wbr>Boxfilter</h3>
					<div class="tsd-signature tsd-kind-icon">Check<wbr>Boxfilter<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#enumfilter" class="tsd-signature-type">Enumfilter</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1289</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="combinefulltextfilter" class="tsd-anchor"></a>
					<h3>Combine<wbr>Fulltext<wbr>Filter</h3>
					<div class="tsd-signature tsd-kind-icon">Combine<wbr>Fulltext<wbr>Filter<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#fulltextfilter" class="tsd-signature-type">FulltextFilter</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1297</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="combotextfilter" class="tsd-anchor"></a>
					<h3>Combo<wbr>Text<wbr>Filter</h3>
					<div class="tsd-signature tsd-kind-icon">Combo<wbr>Text<wbr>Filter<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#keyvaluefilter" class="tsd-signature-type">KeyValueFilter</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>match<span class="tsd-signature-symbol">?: </span><a href="../enums/listtypes.filtermatchtype.html" class="tsd-signature-type">filterMatchType</a><span class="tsd-signature-symbol">; </span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1267</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="datebetweenfilter" class="tsd-anchor"></a>
					<h3>Date<wbr>Between<wbr>Filter</h3>
					<div class="tsd-signature tsd-kind-icon">Date<wbr>Between<wbr>Filter<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#keyvaluefilter" class="tsd-signature-type">KeyValueFilter</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1271</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="datefilter" class="tsd-anchor"></a>
					<h3>Date<wbr>Filter</h3>
					<div class="tsd-signature tsd-kind-icon">Date<wbr>Filter<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#keyvaluefilter" class="tsd-signature-type">KeyValueFilter</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1257</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="enumfilter" class="tsd-anchor"></a>
					<h3>Enumfilter</h3>
					<div class="tsd-signature tsd-kind-icon">Enumfilter<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#keyvaluefilter" class="tsd-signature-type">KeyValueFilter</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1286</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="enumradiofilter" class="tsd-anchor"></a>
					<h3>Enumradio<wbr>Filter</h3>
					<div class="tsd-signature tsd-kind-icon">Enumradio<wbr>Filter<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#textfilter" class="tsd-signature-type">TextFilter</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1266</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="fulltextfilter" class="tsd-anchor"></a>
					<h3>Fulltext<wbr>Filter</h3>
					<div class="tsd-signature tsd-kind-icon">Fulltext<wbr>Filter<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#keyvaluefilter" class="tsd-signature-type">KeyValueFilter</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1294</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="ipage" class="tsd-anchor"></a>
					<h3>IPage</h3>
					<div class="tsd-signature tsd-kind-icon">IPage<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>columns<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>pageName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1577</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>columns<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>page<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
					<a name="iqueryresult" class="tsd-anchor"></a>
					<h3>IQuery<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">IQuery<wbr>Result&lt;RowType&gt;<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>pageCount<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#getlistcountrequestresult" class="tsd-signature-type">getListCountRequestResult</a><span class="tsd-signature-symbol">; </span>pageData<span class="tsd-signature-symbol">: </span><a href="listtypes.html#getlistdatarequestresult" class="tsd-signature-type">getListDataRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>getList<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1207</li>
						</ul>
					</aside>
					<h4 class="tsd-type-parameters-title">Type parameters</h4>
					<ul class="tsd-type-parameters">
						<li>
							<h4>RowType<span class="tsd-signature-symbol">: </span><a href="../globals.html#listrow" class="tsd-signature-type">ListRow</a> = <span class="tsd-signature-type">ListRow</span></h4>
						</li>
					</ul>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> page<wbr>Count<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#getlistcountrequestresult" class="tsd-signature-type">getListCountRequestResult</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5>page<wbr>Data<span class="tsd-signature-symbol">: </span><a href="listtypes.html#getlistdatarequestresult" class="tsd-signature-type">getListDataRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>get<wbr>List<span class="tsd-signature-symbol">: </span>function</h5>
								<ul class="tsd-signatures tsd-kind-function tsd-parent-kind-type-literal">
									<li class="tsd-signature tsd-kind-icon">get<wbr>List<span class="tsd-signature-symbol">(</span>tabName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, item_size<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span>, listQueryParams<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#queryprops" class="tsd-signature-type">queryProps</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#getlistdatabytabrequestresult" class="tsd-signature-type">getListDataByTabRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span><span class="tsd-signature-symbol">&gt;</span></li>
									<li class="tsd-signature tsd-kind-icon">get<wbr>List<span class="tsd-signature-symbol">(</span>item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, item_size<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span>, listQueryParams<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#queryprops" class="tsd-signature-type">queryProps</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#getlistdataofsinglepagerequestresult" class="tsd-signature-type">getListDataOfSinglePageRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
									<li class="tsd-signature tsd-kind-icon">get<wbr>List<span class="tsd-signature-symbol">(</span>a<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span>, b<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span>, c<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#queryprops" class="tsd-signature-type">queryProps</a>, d<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#queryprops" class="tsd-signature-type">queryProps</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#getlistdatabytabrequestresult" class="tsd-signature-type">getListDataByTabRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#getlistdataofsinglepagerequestresult" class="tsd-signature-type">getListDataOfSinglePageRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<aside class="tsd-sources">
											<ul>
												<li>Defined in src/def/index.ts:1209</li>
											</ul>
										</aside>
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>tabName: <span class="tsd-signature-type">string</span></h5>
											</li>
											<li>
												<h5>item_index: <span class="tsd-signature-type">number</span></h5>
											</li>
											<li>
												<h5><span class="tsd-flag ts-flagOptional">Optional</span> item_size: <span class="tsd-signature-type">number</span></h5>
											</li>
											<li>
												<h5><span class="tsd-flag ts-flagOptional">Optional</span> listQueryParams: <a href="listtypes.html#queryprops" class="tsd-signature-type">queryProps</a></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#getlistdatabytabrequestresult" class="tsd-signature-type">getListDataByTabRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span><span class="tsd-signature-symbol">&gt;</span></h4>
									</li>
									<li class="tsd-description">
										<aside class="tsd-sources">
											<ul>
												<li>Defined in src/def/index.ts:1215</li>
											</ul>
										</aside>
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>item_index: <span class="tsd-signature-type">number</span></h5>
											</li>
											<li>
												<h5><span class="tsd-flag ts-flagOptional">Optional</span> item_size: <span class="tsd-signature-type">number</span></h5>
											</li>
											<li>
												<h5><span class="tsd-flag ts-flagOptional">Optional</span> listQueryParams: <a href="listtypes.html#queryprops" class="tsd-signature-type">queryProps</a></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#getlistdataofsinglepagerequestresult" class="tsd-signature-type">getListDataOfSinglePageRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
									</li>
									<li class="tsd-description">
										<aside class="tsd-sources">
											<ul>
												<li>Defined in src/def/index.ts:1220</li>
											</ul>
										</aside>
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>a: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span></h5>
											</li>
											<li>
												<h5><span class="tsd-flag ts-flagOptional">Optional</span> b: <span class="tsd-signature-type">number</span></h5>
											</li>
											<li>
												<h5><span class="tsd-flag ts-flagOptional">Optional</span> c: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#queryprops" class="tsd-signature-type">queryProps</a></h5>
											</li>
											<li>
												<h5><span class="tsd-flag ts-flagOptional">Optional</span> d: <a href="listtypes.html#queryprops" class="tsd-signature-type">queryProps</a></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#getlistdatabytabrequestresult" class="tsd-signature-type">getListDataByTabRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#getlistdataofsinglepagerequestresult" class="tsd-signature-type">getListDataOfSinglePageRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="keyvaluefilter" class="tsd-anchor"></a>
					<h3>Key<wbr>Value<wbr>Filter</h3>
					<div class="tsd-signature tsd-kind-icon">Key<wbr>Value<wbr>Filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1254</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="keyvaluefilters" class="tsd-anchor"></a>
					<h3>Key<wbr>Value<wbr>Filters</h3>
					<div class="tsd-signature tsd-kind-icon">Key<wbr>Value<wbr>Filters<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#datefilter" class="tsd-signature-type">DateFilter</a><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#textfilter" class="tsd-signature-type">TextFilter</a><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#textdatafilter" class="tsd-signature-type">TextDataFilter</a><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#textmonthfilter" class="tsd-signature-type">TextMonthFilter</a><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#enumradiofilter" class="tsd-signature-type">EnumradioFilter</a><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#combotextfilter" class="tsd-signature-type">ComboTextFilter</a><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#datebetweenfilter" class="tsd-signature-type">DateBetweenFilter</a><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#numberfilter" class="tsd-signature-type">NumberFilter</a><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#searchfilter" class="tsd-signature-type">SearchFilter</a><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#booleanfilter" class="tsd-signature-type">BooleanFilter</a><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#enumfilter" class="tsd-signature-type">Enumfilter</a><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#checkboxfilter" class="tsd-signature-type">CheckBoxfilter</a><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#cascaderfilter" class="tsd-signature-type">CascaderFilter</a><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#fulltextfilter" class="tsd-signature-type">FulltextFilter</a><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#combinefulltextfilter" class="tsd-signature-type">CombineFulltextFilter</a><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#treefilter" class="tsd-signature-type">TreeFilter</a><span class="tsd-signature-symbol">&gt;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1302</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="numberfilter" class="tsd-anchor"></a>
					<h3>Number<wbr>Filter</h3>
					<div class="tsd-signature tsd-kind-icon">Number<wbr>Filter<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#keyvaluefilter" class="tsd-signature-type">KeyValueFilter</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>max<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>min<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1274</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="prefiltersobject" class="tsd-anchor"></a>
					<h3>Prefilters<wbr>Object</h3>
					<div class="tsd-signature tsd-kind-icon">Prefilters<wbr>Object<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{}</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1246</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-index-signature">
								<h5><span class="tsd-signature-symbol">[</span>property: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="searchfilter" class="tsd-anchor"></a>
					<h3>Search<wbr>Filter</h3>
					<div class="tsd-signature tsd-kind-icon">Search<wbr>Filter<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#keyvaluefilter" class="tsd-signature-type">KeyValueFilter</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1280</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="textdatafilter" class="tsd-anchor"></a>
					<h3>Text<wbr>Data<wbr>Filter</h3>
					<div class="tsd-signature tsd-kind-icon">Text<wbr>Data<wbr>Filter<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#textfilter" class="tsd-signature-type">TextFilter</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1264</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="textfilter" class="tsd-anchor"></a>
					<h3>Text<wbr>Filter</h3>
					<div class="tsd-signature tsd-kind-icon">Text<wbr>Filter<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#keyvaluefilter" class="tsd-signature-type">KeyValueFilter</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>match<span class="tsd-signature-symbol">?: </span><a href="../enums/listtypes.filtermatchtype.html" class="tsd-signature-type">filterMatchType</a><span class="tsd-signature-symbol">; </span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1260</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="textmonthfilter" class="tsd-anchor"></a>
					<h3>Text<wbr>Month<wbr>Filter</h3>
					<div class="tsd-signature tsd-kind-icon">Text<wbr>Month<wbr>Filter<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#textfilter" class="tsd-signature-type">TextFilter</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1265</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="treefilter" class="tsd-anchor"></a>
					<h3>Tree<wbr>Filter</h3>
					<div class="tsd-signature tsd-kind-icon">Tree<wbr>Filter<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#keyvaluefilter" class="tsd-signature-type">KeyValueFilter</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1298</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<div class="tsd-signature tsd-kind-icon">constructor<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>list_name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>model_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1491</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> list_<wbr>name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>model_<wbr>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="exportexcelparams" class="tsd-anchor"></a>
					<h3>export<wbr>Excel<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">export<wbr>Excel<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>export_template<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>item_index<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>item_size<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>page_name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>pages<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#ipage" class="tsd-signature-type">IPage</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tabName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>template_name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>workflowType<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1567</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> export_<wbr>template<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> item_<wbr>index<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> item_<wbr>size<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> page_<wbr>name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> pages<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#ipage" class="tsd-signature-type">IPage</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> tab<wbr>Name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> template_<wbr>name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> workflow<wbr>Type<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="exporttoexcel" class="tsd-anchor"></a>
					<h3>export<wbr>ToExcel</h3>
					<div class="tsd-signature tsd-kind-icon">export<wbr>ToExcel<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#filterstype" class="tsd-signature-type">FiltersType</a><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>order_obj<span class="tsd-signature-symbol">: </span><a href="../globals.html#orderobj" class="tsd-signature-type">orderObj</a><span class="tsd-signature-symbol">; </span>page_name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><a href="listtypes.html#exporttoexcel.__type-6.prefilters" class="tsd-signature-type">prefilters</a><span class="tsd-signature-symbol">; </span>tagFilters<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1495</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#filterstype" class="tsd-signature-type">FiltersType</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>order_<wbr>obj<span class="tsd-signature-symbol">: </span><a href="../globals.html#orderobj" class="tsd-signature-type">orderObj</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> page_<wbr>name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>prefilters<span class="tsd-signature-symbol">: </span><a href="listtypes.html#exporttoexcel.__type-6.prefilters" class="tsd-signature-type">prefilters</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tag<wbr>Filters<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="filtergroup" class="tsd-anchor"></a>
					<h3>filter<wbr>Group</h3>
					<div class="tsd-signature tsd-kind-icon">filter<wbr>Group<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>by_date<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>columns<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>ext_properties<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">; </span>full_property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>width<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1343</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>by_<wbr>date<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>columns<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>ext_properties<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">; </span>full_property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>width<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="fullfilledqueryprops" class="tsd-anchor"></a>
					<h3>fullfilled<wbr>Query<wbr>Props</h3>
					<div class="tsd-signature tsd-kind-icon">fullfilled<wbr>Query<wbr>Props<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>columns<span class="tsd-signature-symbol">: </span><a href="../globals.html#orderobj.__type.column" class="tsd-signature-type">column</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>filters<span class="tsd-signature-symbol">: </span><a href="../globals.html#filter" class="tsd-signature-type">filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>filters4Workflow<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">; </span>item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>order_obj<span class="tsd-signature-symbol">: </span><a href="../globals.html#orderobj" class="tsd-signature-type">orderObj</a><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><a href="listtypes.html#fullfilledqueryprops.__type-8.prefilters-1" class="tsd-signature-type">prefilters</a><span class="tsd-signature-symbol">; </span>router<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>sorts<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"desc"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"asc"</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tabName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>tagFilters<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>workflowType<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1553</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>columns<span class="tsd-signature-symbol">: </span><a href="../globals.html#orderobj.__type.column" class="tsd-signature-type">column</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>filters<span class="tsd-signature-symbol">: </span><a href="../globals.html#filter" class="tsd-signature-type">filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> filters4<wbr>Workflow<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>item_<wbr>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>item_<wbr>size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>order_<wbr>obj<span class="tsd-signature-symbol">: </span><a href="../globals.html#orderobj" class="tsd-signature-type">orderObj</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5>prefilters<span class="tsd-signature-symbol">: </span><a href="listtypes.html#fullfilledqueryprops.__type-8.prefilters-1" class="tsd-signature-type">prefilters</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5>router<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>sorts<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"desc"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"asc"</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> tab<wbr>Name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tag<wbr>Filters<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> workflow<wbr>Type<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="getlistcountrequestresult" class="tsd-anchor"></a>
					<h3>get<wbr>List<wbr>Count<wbr>Request<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">get<wbr>List<wbr>Count<wbr>Request<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>page_datas<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1461</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> page_<wbr>datas<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{}</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter-index-signature">
										<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-symbol">{ </span>item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>rows<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol"> }</span></h5>
										<ul class="tsd-parameters">
											<li class="tsd-parameter">
												<h5>item_<wbr>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>item_<wbr>size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>record_<wbr>count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>rows<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-symbol">]</span></h5>
											</li>
										</ul>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="getlistdatabytabparams" class="tsd-anchor"></a>
					<h3>get<wbr>List<wbr>Data<wbr>ByTab<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">get<wbr>List<wbr>Data<wbr>ByTab<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>columns<span class="tsd-signature-symbol">: </span><a href="../globals.html#orderobj.__type.column" class="tsd-signature-type">column</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>filters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">; </span>item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>order_obj<span class="tsd-signature-symbol">: </span><a href="../globals.html#orderobj" class="tsd-signature-type">orderObj</a><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><a href="listtypes.html#getlistdatabytabparams.__type-11.prefilters-2" class="tsd-signature-type">prefilters</a><span class="tsd-signature-symbol">; </span>tagFilters<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1321</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>columns<span class="tsd-signature-symbol">: </span><a href="../globals.html#orderobj.__type.column" class="tsd-signature-type">column</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>filters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>item_<wbr>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>item_<wbr>size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>order_<wbr>obj<span class="tsd-signature-symbol">: </span><a href="../globals.html#orderobj" class="tsd-signature-type">orderObj</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5>prefilters<span class="tsd-signature-symbol">: </span><a href="listtypes.html#getlistdatabytabparams.__type-11.prefilters-2" class="tsd-signature-type">prefilters</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tag<wbr>Filters<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
					<a name="getlistdatabytabrequestresult" class="tsd-anchor"></a>
					<h3>get<wbr>List<wbr>Data<wbr>ByTab<wbr>Request<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">get<wbr>List<wbr>Data<wbr>ByTab<wbr>Request<wbr>Result&lt;T&gt;<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>customSummaries<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>rows<span class="tsd-signature-symbol">: </span><a href="../globals.html#metarow" class="tsd-signature-type">metaRow</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>summaries<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>summary<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1473</li>
						</ul>
					</aside>
					<h4 class="tsd-type-parameters-title">Type parameters</h4>
					<ul class="tsd-type-parameters">
						<li>
							<h4>T<span class="tsd-signature-symbol">: </span><a href="../globals.html#listrow" class="tsd-signature-type">ListRow</a> = <span class="tsd-signature-type">ListRow</span></h4>
						</li>
					</ul>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>custom<wbr>Summaries<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter-index-signature">
										<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span></h5>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>item_<wbr>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>item_<wbr>size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>record_<wbr>count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>rows<span class="tsd-signature-symbol">: </span><a href="../globals.html#metarow" class="tsd-signature-type">metaRow</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>summaries<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>summary<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="getlistdataofsinglepageparams" class="tsd-anchor"></a>
					<h3>get<wbr>List<wbr>Data<wbr>OfSingle<wbr>Page<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">get<wbr>List<wbr>Data<wbr>OfSingle<wbr>Page<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>columns<span class="tsd-signature-symbol">: </span><a href="../globals.html#orderobj.__type.column" class="tsd-signature-type">column</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>filters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">; </span>item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>order_obj<span class="tsd-signature-symbol">: </span><a href="../globals.html#orderobj" class="tsd-signature-type">orderObj</a><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><a href="listtypes.html#getlistdataofsinglepageparams.__type-14.prefilters-3" class="tsd-signature-type">prefilters</a><span class="tsd-signature-symbol">; </span>tagFilters<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1332</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>columns<span class="tsd-signature-symbol">: </span><a href="../globals.html#orderobj.__type.column" class="tsd-signature-type">column</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>filters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>item_<wbr>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>item_<wbr>size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>order_<wbr>obj<span class="tsd-signature-symbol">: </span><a href="../globals.html#orderobj" class="tsd-signature-type">orderObj</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5>prefilters<span class="tsd-signature-symbol">: </span><a href="listtypes.html#getlistdataofsinglepageparams.__type-14.prefilters-3" class="tsd-signature-type">prefilters</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tag<wbr>Filters<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
					<a name="getlistdataofsinglepagerequestresult" class="tsd-anchor"></a>
					<h3>get<wbr>List<wbr>Data<wbr>OfSingle<wbr>Page<wbr>Request<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">get<wbr>List<wbr>Data<wbr>OfSingle<wbr>Page<wbr>Request<wbr>Result&lt;T&gt;<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#getlistdatarequestresult" class="tsd-signature-type">getListDataRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1483</li>
						</ul>
					</aside>
					<h4 class="tsd-type-parameters-title">Type parameters</h4>
					<ul class="tsd-type-parameters">
						<li>
							<h4>T<span class="tsd-signature-symbol">: </span><a href="../globals.html#listrow" class="tsd-signature-type">ListRow</a> = <span class="tsd-signature-type">ListRow</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
					<a name="getlistdatarequestresult" class="tsd-anchor"></a>
					<h3>get<wbr>List<wbr>Data<wbr>Request<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">get<wbr>List<wbr>Data<wbr>Request<wbr>Result&lt;T&gt;<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>customProperties<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>customSummaries<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>information<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span><span class="tsd-signature-symbol">; </span>item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>meta<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>actions<span class="tsd-signature-symbol">?: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>bottom_actions<span class="tsd-signature-symbol">?: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>bottom_section_refs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>checkboxes<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>detail_action_visible<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>exportBtnEnable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>export_template<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>field_groups<span class="tsd-signature-symbol">: </span><a href="../globals.html#field_group" class="tsd-signature-type">field_group</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>filters<span class="tsd-signature-symbol">: </span><a href="../globals.html#metafilter" class="tsd-signature-type">metaFilter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>group_sums<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>intents<span class="tsd-signature-symbol">: </span><a href="../globals.html#rowintent" class="tsd-signature-type">RowIntent</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>key_field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>lists<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>log_action_visible<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>manualLoadData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>miniDetailExpandFirst<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>miniDetailFlag<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>modelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>normalFilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>notifies<span class="tsd-signature-symbol">?: </span><a href="../globals.html#notifymeta" class="tsd-signature-type">NotifyMeta</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>pages<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>bottom_actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>checkboxs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>field_groups<span class="tsd-signature-symbol">: </span><a href="../globals.html#field_group" class="tsd-signature-type">field_group</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>intents<span class="tsd-signature-symbol">: </span><a href="../globals.html#rowintent" class="tsd-signature-type">RowIntent</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>row_actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>section_refs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>showActionsAndIntentsLimit<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>sortDefMetas<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"desc"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"asc"</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>systemSchemes<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>content<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>template_names<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>title_template<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>top_section_refs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tree<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>depth<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>searchProperties<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>summary<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>modelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>treeModelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>tree_with_data<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>dataModelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fields<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>foreignKey<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>template<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>treeForeignKey<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>treeModelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>operator<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">null</span><span class="tsd-signature-symbol">; </span>page_datas<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>recordCount4Tabs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>rows<span class="tsd-signature-symbol">: </span><a href="../globals.html#metarow" class="tsd-signature-type">metaRow</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>states<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span>subProjectName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>summaries<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>summary<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>system_templates<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tagGroups<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#taggroup-1" class="tsd-signature-type">TagGroup</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>title<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1362</li>
						</ul>
					</aside>
					<h4 class="tsd-type-parameters-title">Type parameters</h4>
					<ul class="tsd-type-parameters">
						<li>
							<h4>T<span class="tsd-signature-symbol">: </span><a href="../globals.html#listrow" class="tsd-signature-type">ListRow</a> = <span class="tsd-signature-type">ListRow</span></h4>
						</li>
					</ul>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>custom<wbr>Properties<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter-index-signature">
										<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-type">any</span></h5>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>custom<wbr>Summaries<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter-index-signature">
										<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-type">any</span></h5>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>information<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>item_<wbr>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>item_<wbr>size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>meta<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>actions<span class="tsd-signature-symbol">?: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>bottom_actions<span class="tsd-signature-symbol">?: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>bottom_section_refs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>checkboxes<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>detail_action_visible<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>exportBtnEnable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>export_template<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>field_groups<span class="tsd-signature-symbol">: </span><a href="../globals.html#field_group" class="tsd-signature-type">field_group</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>filters<span class="tsd-signature-symbol">: </span><a href="../globals.html#metafilter" class="tsd-signature-type">metaFilter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>group_sums<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>intents<span class="tsd-signature-symbol">: </span><a href="../globals.html#rowintent" class="tsd-signature-type">RowIntent</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>key_field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>lists<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>log_action_visible<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>manualLoadData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>miniDetailExpandFirst<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>miniDetailFlag<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>modelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>normalFilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>notifies<span class="tsd-signature-symbol">?: </span><a href="../globals.html#notifymeta" class="tsd-signature-type">NotifyMeta</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>pages<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>bottom_actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>checkboxs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>field_groups<span class="tsd-signature-symbol">: </span><a href="../globals.html#field_group" class="tsd-signature-type">field_group</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>intents<span class="tsd-signature-symbol">: </span><a href="../globals.html#rowintent" class="tsd-signature-type">RowIntent</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>row_actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>section_refs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>showActionsAndIntentsLimit<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>sortDefMetas<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"desc"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"asc"</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>systemSchemes<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>content<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>template_names<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>title_template<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>top_section_refs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tree<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>depth<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>searchProperties<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>summary<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>modelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>treeModelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>tree_with_data<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>dataModelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fields<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>foreignKey<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>template<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>treeForeignKey<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>treeModelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter">
										<h5><span class="tsd-flag ts-flagOptional">Optional</span> actions<span class="tsd-signature-symbol">?: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5><span class="tsd-flag ts-flagOptional">Optional</span> bottom_<wbr>actions<span class="tsd-signature-symbol">?: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>bottom_<wbr>section_<wbr>refs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>checkboxes<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>detail_<wbr>action_<wbr>visible<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>export<wbr>Btn<wbr>Enable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5><span class="tsd-flag ts-flagOptional">Optional</span> export_<wbr>template<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>field_<wbr>groups<span class="tsd-signature-symbol">: </span><a href="../globals.html#field_group" class="tsd-signature-type">field_group</a><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>filters<span class="tsd-signature-symbol">: </span><a href="../globals.html#metafilter" class="tsd-signature-type">metaFilter</a><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>group_<wbr>sums<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>intents<span class="tsd-signature-symbol">: </span><a href="../globals.html#rowintent" class="tsd-signature-type">RowIntent</a><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>key_<wbr>field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>lists<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>log_<wbr>action_<wbr>visible<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>manual<wbr>Load<wbr>Data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>mini<wbr>Detail<wbr>Expand<wbr>First<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>mini<wbr>Detail<wbr>Flag<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>model<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>normal<wbr>Filters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5><span class="tsd-flag ts-flagOptional">Optional</span> notifies<span class="tsd-signature-symbol">?: </span><a href="../globals.html#notifymeta" class="tsd-signature-type">NotifyMeta</a><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>pages<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>bottom_actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>checkboxs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>field_groups<span class="tsd-signature-symbol">: </span><a href="../globals.html#field_group" class="tsd-signature-type">field_group</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>intents<span class="tsd-signature-symbol">: </span><a href="../globals.html#rowintent" class="tsd-signature-type">RowIntent</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>row_<wbr>actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>section_<wbr>refs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>show<wbr>Actions<wbr>And<wbr>Intents<wbr>Limit<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5><span class="tsd-flag ts-flagOptional">Optional</span> sort<wbr>Def<wbr>Metas<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"desc"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"asc"</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>system<wbr>Schemes<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>content<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5><span class="tsd-flag ts-flagOptional">Optional</span> template_<wbr>names<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>title_<wbr>template<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>top_<wbr>section_<wbr>refs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5><span class="tsd-flag ts-flagOptional">Optional</span> tree<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>depth<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>searchProperties<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>summary<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>modelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>treeModelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5>
										<ul class="tsd-parameters">
											<li class="tsd-parameter-index-signature">
												<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-type">unknown</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5><span class="tsd-flag ts-flagOptional">Optional</span> depth<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5><span class="tsd-flag ts-flagOptional">Optional</span> search<wbr>Properties<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>summary<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>modelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5>
												<ul class="tsd-parameters">
													<li class="tsd-parameter">
														<h5>field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
													</li>
													<li class="tsd-parameter">
														<h5>model<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
													</li>
												</ul>
											</li>
											<li class="tsd-parameter">
												<h5>tree<wbr>Model<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
											</li>
										</ul>
									</li>
									<li class="tsd-parameter">
										<h5><span class="tsd-flag ts-flagOptional">Optional</span> tree_<wbr>with_<wbr>data<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>dataModelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fields<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>foreignKey<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>template<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>treeForeignKey<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>treeModelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5>
										<ul class="tsd-parameters">
											<li class="tsd-parameter">
												<h5>data<wbr>Model<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>fields<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>foreign<wbr>Key<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>template<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>tree<wbr>Foreign<wbr>Key<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>tree<wbr>Model<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
											</li>
										</ul>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>operator<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">null</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> page_<wbr>datas<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{}</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter-index-signature">
										<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-symbol">{ </span>item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>rows<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">; </span>system_templates<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>pageName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>path<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></h5>
										<ul class="tsd-parameters">
											<li class="tsd-parameter">
												<h5>item_<wbr>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>item_<wbr>size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>record_<wbr>count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>rows<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-symbol">]</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5><span class="tsd-flag ts-flagOptional">Optional</span> system_<wbr>templates<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>pageName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>path<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h5>
											</li>
										</ul>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>record<wbr>Count4<wbr>Tabs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>record_<wbr>count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>rows<span class="tsd-signature-symbol">: </span><a href="../globals.html#metarow" class="tsd-signature-type">metaRow</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>states<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>sub<wbr>Project<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>summaries<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>summary<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>system_<wbr>templates<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tag<wbr>Groups<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#taggroup-1" class="tsd-signature-type">TagGroup</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> title<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
					<a name="getsingletabpagelistfunctype" class="tsd-anchor"></a>
					<h3>get<wbr>Single<wbr>Tab<wbr>Page<wbr>List<wbr>Func<wbr>Type</h3>
					<div class="tsd-signature tsd-kind-icon">get<wbr>Single<wbr>Tab<wbr>Page<wbr>List<wbr>Func<wbr>Type&lt;RowType&gt;<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, listQueryParams<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#queryprops" class="tsd-signature-type">queryProps</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#getlistdataofsinglepagerequestresult" class="tsd-signature-type">getListDataOfSinglePageRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1199</li>
						</ul>
					</aside>
					<h4 class="tsd-type-parameters-title">Type parameters</h4>
					<ul class="tsd-type-parameters">
						<li>
							<h4>RowType<span class="tsd-signature-symbol">: </span><a href="../globals.html#listrow" class="tsd-signature-type">ListRow</a> = <span class="tsd-signature-type">ListRow</span></h4>
						</li>
					</ul>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, listQueryParams<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#queryprops" class="tsd-signature-type">queryProps</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#getlistdataofsinglepagerequestresult" class="tsd-signature-type">getListDataOfSinglePageRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>item_index: <span class="tsd-signature-type">number</span></h5>
											</li>
											<li>
												<h5>item_size: <span class="tsd-signature-type">number</span></h5>
											</li>
											<li>
												<h5><span class="tsd-flag ts-flagOptional">Optional</span> listQueryParams: <a href="listtypes.html#queryprops" class="tsd-signature-type">queryProps</a></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#getlistdataofsinglepagerequestresult" class="tsd-signature-type">getListDataOfSinglePageRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
					<a name="getspecifictablistfunctype" class="tsd-anchor"></a>
					<h3>get<wbr>Specific<wbr>Tab<wbr>List<wbr>Func<wbr>Type</h3>
					<div class="tsd-signature tsd-kind-icon">get<wbr>Specific<wbr>Tab<wbr>List<wbr>Func<wbr>Type&lt;RowType&gt;<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>tabName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, listQueryParams<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#queryprops" class="tsd-signature-type">queryProps</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#getlistdatabytabrequestresult" class="tsd-signature-type">getListDataByTabRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span><span class="tsd-signature-symbol">&gt;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1192</li>
						</ul>
					</aside>
					<h4 class="tsd-type-parameters-title">Type parameters</h4>
					<ul class="tsd-type-parameters">
						<li>
							<h4>RowType<span class="tsd-signature-symbol">: </span><a href="../globals.html#listrow" class="tsd-signature-type">ListRow</a> = <span class="tsd-signature-type">ListRow</span></h4>
						</li>
					</ul>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>tabName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, listQueryParams<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#queryprops" class="tsd-signature-type">queryProps</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#getlistdatabytabrequestresult" class="tsd-signature-type">getListDataByTabRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span><span class="tsd-signature-symbol">&gt;</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>tabName: <span class="tsd-signature-type">string</span></h5>
											</li>
											<li>
												<h5>item_index: <span class="tsd-signature-type">number</span></h5>
											</li>
											<li>
												<h5>item_size: <span class="tsd-signature-type">number</span></h5>
											</li>
											<li>
												<h5><span class="tsd-flag ts-flagOptional">Optional</span> listQueryParams: <a href="listtypes.html#queryprops" class="tsd-signature-type">queryProps</a></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#getlistdatabytabrequestresult" class="tsd-signature-type">getListDataByTabRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span><span class="tsd-signature-symbol">&gt;</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="getworkflow" class="tsd-anchor"></a>
					<h3>get<wbr>Workflow</h3>
					<div class="tsd-signature tsd-kind-icon">get<wbr>Workflow<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tabName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1509</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tab<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="initprops" class="tsd-anchor"></a>
					<h3>init<wbr>Props</h3>
					<div class="tsd-signature tsd-kind-icon">init<wbr>Props<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>columns<span class="tsd-signature-symbol">?: </span><a href="../globals.html#orderobj.__type.column" class="tsd-signature-type">column</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>filters<span class="tsd-signature-symbol">?: </span><a href="tools.html#filterstype" class="tsd-signature-type">FiltersType</a><span class="tsd-signature-symbol">; </span>item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>order_obj<span class="tsd-signature-symbol">?: </span><a href="../globals.html#orderobj" class="tsd-signature-type">orderObj</a><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#initprops.__type-26.prefilters-4" class="tsd-signature-type">prefilters</a><span class="tsd-signature-symbol">; </span>tagFilters<span class="tsd-signature-symbol">?: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1513</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> columns<span class="tsd-signature-symbol">?: </span><a href="../globals.html#orderobj.__type.column" class="tsd-signature-type">column</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> filters<span class="tsd-signature-symbol">?: </span><a href="tools.html#filterstype" class="tsd-signature-type">FiltersType</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5>item_<wbr>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>item_<wbr>size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> order_<wbr>obj<span class="tsd-signature-symbol">?: </span><a href="../globals.html#orderobj" class="tsd-signature-type">orderObj</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> prefilters<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#initprops.__type-26.prefilters-4" class="tsd-signature-type">prefilters</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> tag<wbr>Filters<span class="tsd-signature-symbol">?: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="queryprops" class="tsd-anchor"></a>
					<h3>query<wbr>Props</h3>
					<div class="tsd-signature tsd-kind-icon">query<wbr>Props<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#querypropspage" class="tsd-signature-type">queryPropsPage</a><span class="tsd-signature-symbol"> | </span><a href="listtypes.html#querypropsindex" class="tsd-signature-type">queryPropsIndex</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1551</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="querypropsbase" class="tsd-anchor"></a>
					<h3>query<wbr>Props<wbr>Base</h3>
					<div class="tsd-signature tsd-kind-icon">query<wbr>Props<wbr>Base<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>columns<span class="tsd-signature-symbol">?: </span><a href="../globals.html#orderobj.__type.column" class="tsd-signature-type">column</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>filters<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#keyvaluefilters" class="tsd-signature-type">KeyValueFilters</a><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#filter" class="tsd-signature-type">filter</a><span class="tsd-signature-symbol"> | </span><a href="treetypes.html#filter" class="tsd-signature-type">filter</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>filters4Workflow<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">; </span>item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>order_obj<span class="tsd-signature-symbol">?: </span><a href="../globals.html#orderobj" class="tsd-signature-type">orderObj</a><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#querypropsbase.__type-27.prefilters-5" class="tsd-signature-type">prefilters</a><span class="tsd-signature-symbol">; </span>router<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>sorts<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"desc"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"asc"</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tabName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>tagFilters<span class="tsd-signature-symbol">?: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>workflowType<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1531</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> columns<span class="tsd-signature-symbol">?: </span><a href="../globals.html#orderobj.__type.column" class="tsd-signature-type">column</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> filters<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#keyvaluefilters" class="tsd-signature-type">KeyValueFilters</a><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#filter" class="tsd-signature-type">filter</a><span class="tsd-signature-symbol"> | </span><a href="treetypes.html#filter" class="tsd-signature-type">filter</a><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> filters4<wbr>Workflow<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>item_<wbr>size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> order_<wbr>obj<span class="tsd-signature-symbol">?: </span><a href="../globals.html#orderobj" class="tsd-signature-type">orderObj</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> prefilters<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#querypropsbase.__type-27.prefilters-5" class="tsd-signature-type">prefilters</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> router<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> sorts<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"desc"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"asc"</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> tab<wbr>Name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> tag<wbr>Filters<span class="tsd-signature-symbol">?: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> workflow<wbr>Type<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="querypropseasy" class="tsd-anchor"></a>
					<h3>query<wbr>Props<wbr>Easy</h3>
					<div class="tsd-signature tsd-kind-icon">query<wbr>Props<wbr>Easy<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#querypropspage" class="tsd-signature-type">queryPropsPage</a><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">"filters"</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>filters<span class="tsd-signature-symbol">?: </span><a href="listtypes.html#keyvaluefilters" class="tsd-signature-type">KeyValueFilters</a><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1524</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="querypropshard" class="tsd-anchor"></a>
					<h3>query<wbr>Props<wbr>Hard</h3>
					<div class="tsd-signature tsd-kind-icon">query<wbr>Props<wbr>Hard<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><a href="listtypes.html#querypropsindex" class="tsd-signature-type">queryPropsIndex</a><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">"filters"</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>filters<span class="tsd-signature-symbol">?: </span><a href="../globals.html#filter" class="tsd-signature-type">filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1527</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="querypropsindex" class="tsd-anchor"></a>
					<h3>query<wbr>Props<wbr>Index</h3>
					<div class="tsd-signature tsd-kind-icon">query<wbr>Props<wbr>Index<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#querypropsbase" class="tsd-signature-type">queryPropsBase</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1547</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="querypropspage" class="tsd-anchor"></a>
					<h3>query<wbr>Props<wbr>Page</h3>
					<div class="tsd-signature tsd-kind-icon">query<wbr>Props<wbr>Page<span class="tsd-signature-symbol">:</span> <a href="listtypes.html#querypropsbase" class="tsd-signature-type">queryPropsBase</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>pageIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1544</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="setcolumnsforpagesapiresult" class="tsd-anchor"></a>
					<h3>set<wbr>Columns<wbr>For<wbr>Pages<wbr>Api<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">set<wbr>Columns<wbr>For<wbr>Pages<wbr>Api<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>bottom_actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>checkboxs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>field_groups<span class="tsd-signature-symbol">: </span><a href="../globals.html#field_group" class="tsd-signature-type">field_group</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>filters<span class="tsd-signature-symbol">: </span><a href="../globals.html#filter" class="tsd-signature-type">filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><a href="listtypes.html#setcolumnsforpagesapiresult.__type-28.prefilters-6" class="tsd-signature-type">prefilters</a><span class="tsd-signature-symbol">; </span>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1235</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>bottom_<wbr>actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>checkboxs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>field_<wbr>groups<span class="tsd-signature-symbol">: </span><a href="../globals.html#field_group" class="tsd-signature-type">field_group</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>filters<span class="tsd-signature-symbol">: </span><a href="../globals.html#filter" class="tsd-signature-type">filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>prefilters<span class="tsd-signature-symbol">: </span><a href="listtypes.html#setcolumnsforpagesapiresult.__type-28.prefilters-6" class="tsd-signature-type">prefilters</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="setcolumnsforpagesparams" class="tsd-anchor"></a>
					<h3>set<wbr>Columns<wbr>For<wbr>Pages<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">set<wbr>Columns<wbr>For<wbr>Pages<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>columns<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>page_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1231</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>columns<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>page_<wbr>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="updateaction" class="tsd-anchor"></a>
					<h3>update<wbr>Action</h3>
					<div class="tsd-signature tsd-kind-icon">update<wbr>Action<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>actionParams<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">; </span>pageName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>selectedList<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1504</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> action<wbr>Params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> page<wbr>Name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>selected<wbr>List<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="updateactionrequestresult" class="tsd-anchor"></a>
					<h3>update<wbr>Action<wbr>Request<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">update<wbr>Action<wbr>Request<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>actions<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>action_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>forward<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1355</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>actions<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>action_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>forward<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="updatefilterparamrequestresult" class="tsd-anchor"></a>
					<h3>update<wbr>Filter<wbr>Param<wbr>Request<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">update<wbr>Filter<wbr>Param<wbr>Request<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1487</li>
						</ul>
					</aside>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
					<li class="current tsd-kind-namespace">
						<a href="listtypes.html">List<wbr>Types</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
					<li class=" tsd-kind-enum tsd-parent-kind-namespace">
						<a href="../enums/listtypes.filtermatchtype.html" class="tsd-kind-icon">filter<wbr>Match<wbr>Type</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#booleanfilter" class="tsd-kind-icon">Boolean<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#cascaderfilter" class="tsd-kind-icon">Cascader<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#checkboxfilter" class="tsd-kind-icon">Check<wbr>Boxfilter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#combinefulltextfilter" class="tsd-kind-icon">Combine<wbr>Fulltext<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#combotextfilter" class="tsd-kind-icon">Combo<wbr>Text<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#datebetweenfilter" class="tsd-kind-icon">Date<wbr>Between<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#datefilter" class="tsd-kind-icon">Date<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#enumfilter" class="tsd-kind-icon">Enumfilter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#enumradiofilter" class="tsd-kind-icon">Enumradio<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#fulltextfilter" class="tsd-kind-icon">Fulltext<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#ipage" class="tsd-kind-icon">IPage</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
						<a href="listtypes.html#iqueryresult" class="tsd-kind-icon">IQuery<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#keyvaluefilter" class="tsd-kind-icon">Key<wbr>Value<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#keyvaluefilters" class="tsd-kind-icon">Key<wbr>Value<wbr>Filters</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#numberfilter" class="tsd-kind-icon">Number<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#prefiltersobject" class="tsd-kind-icon">Prefilters<wbr>Object</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#searchfilter" class="tsd-kind-icon">Search<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#textdatafilter" class="tsd-kind-icon">Text<wbr>Data<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#textfilter" class="tsd-kind-icon">Text<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#textmonthfilter" class="tsd-kind-icon">Text<wbr>Month<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#treefilter" class="tsd-kind-icon">Tree<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#constructor" class="tsd-kind-icon">constructor</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#exportexcelparams" class="tsd-kind-icon">export<wbr>Excel<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#exporttoexcel" class="tsd-kind-icon">export<wbr>ToExcel</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#filtergroup" class="tsd-kind-icon">filter<wbr>Group</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#fullfilledqueryprops" class="tsd-kind-icon">fullfilled<wbr>Query<wbr>Props</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#getlistcountrequestresult" class="tsd-kind-icon">get<wbr>List<wbr>Count<wbr>Request<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#getlistdatabytabparams" class="tsd-kind-icon">get<wbr>List<wbr>Data<wbr>ByTab<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
						<a href="listtypes.html#getlistdatabytabrequestresult" class="tsd-kind-icon">get<wbr>List<wbr>Data<wbr>ByTab<wbr>Request<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#getlistdataofsinglepageparams" class="tsd-kind-icon">get<wbr>List<wbr>Data<wbr>OfSingle<wbr>Page<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
						<a href="listtypes.html#getlistdataofsinglepagerequestresult" class="tsd-kind-icon">get<wbr>List<wbr>Data<wbr>OfSingle<wbr>Page<wbr>Request<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
						<a href="listtypes.html#getlistdatarequestresult" class="tsd-kind-icon">get<wbr>List<wbr>Data<wbr>Request<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
						<a href="listtypes.html#getsingletabpagelistfunctype" class="tsd-kind-icon">get<wbr>Single<wbr>Tab<wbr>Page<wbr>List<wbr>Func<wbr>Type</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
						<a href="listtypes.html#getspecifictablistfunctype" class="tsd-kind-icon">get<wbr>Specific<wbr>Tab<wbr>List<wbr>Func<wbr>Type</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#getworkflow" class="tsd-kind-icon">get<wbr>Workflow</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#initprops" class="tsd-kind-icon">init<wbr>Props</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#queryprops" class="tsd-kind-icon">query<wbr>Props</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#querypropsbase" class="tsd-kind-icon">query<wbr>Props<wbr>Base</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#querypropseasy" class="tsd-kind-icon">query<wbr>Props<wbr>Easy</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#querypropshard" class="tsd-kind-icon">query<wbr>Props<wbr>Hard</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#querypropsindex" class="tsd-kind-icon">query<wbr>Props<wbr>Index</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#querypropspage" class="tsd-kind-icon">query<wbr>Props<wbr>Page</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#setcolumnsforpagesapiresult" class="tsd-kind-icon">set<wbr>Columns<wbr>For<wbr>Pages<wbr>Api<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#setcolumnsforpagesparams" class="tsd-kind-icon">set<wbr>Columns<wbr>For<wbr>Pages<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#updateaction" class="tsd-kind-icon">update<wbr>Action</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#updateactionrequestresult" class="tsd-kind-icon">update<wbr>Action<wbr>Request<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="listtypes.html#updatefilterparamrequestresult" class="tsd-kind-icon">update<wbr>Filter<wbr>Param<wbr>Request<wbr>Result</a>
					</li>
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>