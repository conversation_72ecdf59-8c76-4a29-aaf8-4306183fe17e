{"name": "uniplat-sdk", "version": "0.1.741-private", "description": "uniplat-sdk", "main": "build/main/index", "typings": "build/main/index", "repository": {"type": "git", "url": "https://registry.npmjs.org/uniplat-sdk.git"}, "publishConfig": {"registry": "https://registry.npmjs.org"}, "license": "MIT", "keywords": [], "scripts": {"pub": "npm run build && node publish/pub.js && npm publish && node publish/dev.js", "pub:test": "npm run build && node publish/pub-test.js && npm publish &&  node publish/dev-test.js", "build": "run-p build:*", "build:main": "tsc -p tsconfig.json", "build:module": "tsc -p tsconfig.module.json", "fix": "run-s fix:*", "fix:prettier": "prettier \"src/**/*.ts\" --write", "fix:lint": "eslint src --ext .ts --fix", "test": "run-s build test:*", "test:lint": "eslint src --ext .ts", "test:prettier": "prettier \"src/**/*.ts\" --list-different", "test:spelling": "cspell \"{README.md,.github/*.md,src/**/*.ts}\"", "test:unit": "nyc --silent ava", "check-cli": "run-s test diff-integration-tests check-integration-tests", "check-integration-tests": "run-s check-integration-test:*", "diff-integration-tests": "mkdir -p diff && rm -rf diff/test && cp -r test diff/test && rm -rf diff/test/test-*/.git && cd diff && git init --quiet && git add -A && git commit --quiet --no-verify --allow-empty -m 'WIP' && echo '\\n\\nCommitted most recent integration test output in the \"diff\" directory. Review the changes with \"cd diff && git diff HEAD\" or your preferred git diff viewer.'", "watch:build": "tsc -p tsconfig.json -w", "watch:test": "nyc --silent ava --watch", "cov": "run-s build test:unit cov:html cov:lcov && open-cli coverage/index.html", "cov:html": "nyc report --reporter=html", "cov:lcov": "nyc report --reporter=lcov", "cov:send": "run-s cov:lcov && codecov", "cov:check": "nyc report && nyc check-coverage --lines 100 --functions 100 --branches 100", "doc": "run-s doc:html && open-cli docs/index.html && git add docs && git commit -m 'update docs'", "doc:html": "typedoc src/ --exclude **/*.spec.ts --target ES6 --mode file --out docs", "doc:json": "typedoc src/ --exclude **/*.spec.ts --target ES6 --mode file --json docs/typedoc.json", "doc:publish": "gh-pages -m \"[ci skip] Updates\" -d docs", "version": "standard-version", "reset-hard": "git clean -dfx && git reset --hard && npm i", "prepare-release": "run-s reset-hard test cov:check doc:html version doc:publish", "mjs": "webpack --mode production", "watchmjs": "webpack --mode production --watch", "node-dev": "ts-node-dev ./src/unit-test.ts"}, "engines": {"node": ">=10"}, "dependencies": {"@types/crypto-js": "^4.2.2", "axios": "^0.21.1", "broadcast-channel": "^3.5.3", "crypto-js": "^4.2.0", "event-source-polyfill": "^1.0.22", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "qs": "^6.9.6", "ts-md5": "^1.2.9", "tslib": "^2.3.0"}, "devDependencies": {"@ava/typescript": "^1.1.1", "@dcloudio/types": "^2.5.15", "@istanbuljs/nyc-config-typescript": "^1.0.1", "@types/lodash": "^4.14.172", "@types/node": "^15.9.0", "@types/qs": "^6.9.15", "@typescript-eslint/eslint-plugin": "^4.0.1", "@typescript-eslint/parser": "^4.0.1", "ava": "^3.12.1", "codecov": "^3.5.0", "cspell": "^4.1.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^7.8.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-functional": "^3.0.2", "eslint-plugin-import": "^2.22.0", "gh-pages": "^3.1.0", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "open-cli": "^6.0.1", "prettier": "^2.1.1", "standard-version": "^9.0.0", "ts-loader": "^9.5.1", "ts-node": "^9.0.0", "ts-node-dev": "^2.0.0", "typedoc": "^0.19.0", "typescript": "^4.0.2", "typescript-transform-paths": "^2.2.4", "uglifyjs-webpack-plugin": "^2.2.0", "vue": "^2.6.14", "webpack": "^5.91.0", "webpack-cli": "^5.1.4"}, "files": ["build/main", "build/module", "!**/*.spec.*", "!**/*.json", "CHANGELOG.md", "LICENSE", "README.md"], "ava": {"failFast": true, "timeout": "60s", "typescript": {"rewritePaths": {"src/": "build/main/"}}, "files": ["!build/module/**"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "nyc": {"extends": "@istanbuljs/nyc-config-typescript", "exclude": ["**/*.spec.js"]}}