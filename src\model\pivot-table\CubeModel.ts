export type FieldDef = {
    type: string
    fieldType: string
    label: string
    property: string
    full_property?: string
    width?: number
    defaultValue?: unknown
    ext_properties?: {
        mapping?: {
            model_name: string
            name: string
            is_lazy: boolean
            mapping_values?: { key: string; value: string | number }[]
        }
    }
}

export type FieldItem = FieldDef & {
    dragEnter?: boolean
    kind?: SummaryTypeEnum
    dateKind?: DateGroupTypeEnum
}

export type GroupFieldsItem = {
    field: string
    fullName: string
    label: string
    kind?: string
    total?: boolean
    display?: string
}

export type GroupFieldFilter = {
    property: string
    kind: string
    value: unknown
    display?: string
}

export type SummaryFieldsItem = {
    field: string
    summary: SummaryTypeEnum
}

export enum SummaryTypeEnum {
    Count = "count",
    DistinctCount = "distinct_count",
    Avg = "avg",
    Min = "min",
    Max = "max",
    Sum = "sum",
    Contact = "group_concat",
}

export const summaryMap = new Map<SummaryTypeEnum, string>([
    [SummaryTypeEnum.Sum, "求和"],
    [SummaryTypeEnum.Count, "计数"],
    [SummaryTypeEnum.DistinctCount, "去重计数"],
    [SummaryTypeEnum.Avg, "平均值"],
    [SummaryTypeEnum.Min, "最小值"],
    [SummaryTypeEnum.Max, "最大值"],
    [SummaryTypeEnum.Contact, "合并文本"],
])

export enum DateGroupTypeEnum {
    Day = "day",
    Day2 = "day2",
    Week = "week",
    Month = "month",
    Month2 = "month2",
    Year = "year",
}

export const dateGroupTypeMap = new Map<DateGroupTypeEnum, string>([
    [DateGroupTypeEnum.Day, "按年月日"],
    [DateGroupTypeEnum.Day2, "按日"],
    [DateGroupTypeEnum.Week, "按周"],
    [DateGroupTypeEnum.Month, "按年月"],
    [DateGroupTypeEnum.Month2, "按月"],
    [DateGroupTypeEnum.Year, "按年"],
])

export type HeaderItem = {
    property: string
    path: string
    propertyPath: string
    value?: { display: string; value: string }
    columns?: HeaderItem[]
}

export type ChartSchemaItem = {
    fieldtype: string // dimension, measure
    label: string
    prop: string
    stack?: string // 这个用处未知
    sub_columns?: ChartSchemaItem[]
}

export const chartTypeList = [
    {
        name: "表格",
        icon: "chart_table",
        type: "table",
        desc: "任意维度和数值",
    },
    {
        name: "折线图",
        icon: "chart_line",
        type: "line",
        desc: "1或2个维度;1或多个数值",
    },
    {
        name: "柱状图",
        icon: "chart_bar",
        type: "bar",
        desc: "1或2个维度;1或多个数值",
    },
    {
        name: "饼图",
        icon: "chart_pie",
        type: "pie",
        desc: "1个维度;1或2个数值",
    },
]

export type defaultScheme = {
    columns: string[]
    rows: string[]
    filters: { property: string; value: unknown }[]
    groupFields: {
        property: string
        fullName: string
        kind: DateGroupTypeEnum
    }[]
    summaryFields: {
        property: string
        fullName: string
        summary: SummaryTypeEnum
    }[]
}
