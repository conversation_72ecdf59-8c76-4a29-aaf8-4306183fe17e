import axios from "../../core/axios/index"

import { CreateChatResult } from "./model"
export default class Chat<PERSON>pi {
    constructor(
        private model_name: string,
        private id: number | string,
        private orgID: string
    ) {}

    private buildHeader() {
        return { headers: { CurrentOrg: this.orgID } }
    }

    public createChat(autoJoin?: boolean, title?: string) {
        return axios.post<CreateChatResult>(
            `/general/model/${this.model_name}/${this.id}/createChat/${
                autoJoin ? 1 : 0
            }?title=${title || ""}`,
            {},
            this.buildHeader()
        )
    }

    public addMember<T>(uids: number[], msgId = 0) {
        return axios.post<T>(
            `/general/model/${this.model_name}/${this.id}/addMember/${this.orgID}?msgId=${msgId}`,
            uids
        )
    }

    public removeMember<T>(uids: number[]) {
        return axios.post<T>(
            `/general/model/${this.model_name}/${this.id}/removeMember/${this.orgID}`,
            uids
        )
    }

    public sendMsg<T>(type: string, msg: T) {
        return axios.post<T>(
            `/general/model/${this.model_name}/${this.id}/sendMsg`,
            { type, msg },
            this.buildHeader()
        )
    }

    public startChat<T>() {
        return axios.post<T>(
            `/general/model/${this.model_name}/${this.id}/startChat`,
            {},
            this.buildHeader()
        )
    }

    public finishChat<T>() {
        return axios.post<T>(
            `/general/model/${this.model_name}/${this.id}/finishChat`,
            {},
            this.buildHeader()
        )
    }

    public addCs<T>(uids: number[]) {
        return axios.post<T>(
            `/general/model/${this.model_name}/${this.id}/addCs/${this.orgID}`,
            uids
        )
    }

    public removeCs<T>(uids: number[]) {
        return axios.post<T>(
            `/general/model/${this.model_name}/${this.id}/removeCs/${this.orgID}`,
            uids
        )
    }

    public userExitChat<T>() {
        return axios.post<T>(
            `/general/model/${this.model_name}/${this.id}/userExitChat`,
            {},
            this.buildHeader()
        )
    }

    public csExitChat<T>() {
        return axios.post<T>(
            `/general/model/${this.model_name}/${this.id}/csExitChat`,
            {},
            this.buildHeader()
        )
    }
}
