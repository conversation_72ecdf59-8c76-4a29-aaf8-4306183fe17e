!function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=2)}([function(e,t,r){var n,i;
/**
 * lunr - http://lunrjs.com - A bit like Solr, but much smaller and not as bright - 2.3.9
 * Copyright (C) 2020 Oliver Nightingale
 * @license MIT
 */!function(){var s,o,a,u,l,c,h,d,f,p,y,m,v,g,x,w,L,E,b,S,k,Q,O,P,T,_,C=function(e){var t=new C.Builder;return t.pipeline.add(C.trimmer,C.stopWordFilter,C.stemmer),t.searchPipeline.add(C.stemmer),e.call(t,t),t.build()};C.version="2.3.9"
/*!
 * lunr.utils
 * Copyright (C) 2020 Oliver Nightingale
 */,C.utils={},C.utils.warn=(s=this,function(e){s.console&&console.warn&&console.warn(e)}),C.utils.asString=function(e){return null==e?"":e.toString()},C.utils.clone=function(e){if(null==e)return e;for(var t=Object.create(null),r=Object.keys(e),n=0;n<r.length;n++){var i=r[n],s=e[i];if(Array.isArray(s))t[i]=s.slice();else{if("string"!=typeof s&&"number"!=typeof s&&"boolean"!=typeof s)throw new TypeError("clone is not deep and does not support nested objects");t[i]=s}}return t},C.FieldRef=function(e,t,r){this.docRef=e,this.fieldName=t,this._stringValue=r},C.FieldRef.joiner="/",C.FieldRef.fromString=function(e){var t=e.indexOf(C.FieldRef.joiner);if(-1===t)throw"malformed field ref string";var r=e.slice(0,t),n=e.slice(t+1);return new C.FieldRef(n,r,e)},C.FieldRef.prototype.toString=function(){return null==this._stringValue&&(this._stringValue=this.fieldName+C.FieldRef.joiner+this.docRef),this._stringValue}
/*!
 * lunr.Set
 * Copyright (C) 2020 Oliver Nightingale
 */,C.Set=function(e){if(this.elements=Object.create(null),e){this.length=e.length;for(var t=0;t<this.length;t++)this.elements[e[t]]=!0}else this.length=0},C.Set.complete={intersect:function(e){return e},union:function(){return this},contains:function(){return!0}},C.Set.empty={intersect:function(){return this},union:function(e){return e},contains:function(){return!1}},C.Set.prototype.contains=function(e){return!!this.elements[e]},C.Set.prototype.intersect=function(e){var t,r,n,i=[];if(e===C.Set.complete)return this;if(e===C.Set.empty)return e;this.length<e.length?(t=this,r=e):(t=e,r=this),n=Object.keys(t.elements);for(var s=0;s<n.length;s++){var o=n[s];o in r.elements&&i.push(o)}return new C.Set(i)},C.Set.prototype.union=function(e){return e===C.Set.complete?C.Set.complete:e===C.Set.empty?this:new C.Set(Object.keys(this.elements).concat(Object.keys(e.elements)))},C.idf=function(e,t){var r=0;for(var n in e)"_index"!=n&&(r+=Object.keys(e[n]).length);var i=(t-r+.5)/(r+.5);return Math.log(1+Math.abs(i))},C.Token=function(e,t){this.str=e||"",this.metadata=t||{}},C.Token.prototype.toString=function(){return this.str},C.Token.prototype.update=function(e){return this.str=e(this.str,this.metadata),this},C.Token.prototype.clone=function(e){return e=e||function(e){return e},new C.Token(e(this.str,this.metadata),this.metadata)}
/*!
 * lunr.tokenizer
 * Copyright (C) 2020 Oliver Nightingale
 */,C.tokenizer=function(e,t){if(null==e||null==e)return[];if(Array.isArray(e))return e.map((function(e){return new C.Token(C.utils.asString(e).toLowerCase(),C.utils.clone(t))}));for(var r=e.toString().toLowerCase(),n=r.length,i=[],s=0,o=0;s<=n;s++){var a=s-o;if(r.charAt(s).match(C.tokenizer.separator)||s==n){if(a>0){var u=C.utils.clone(t)||{};u.position=[o,a],u.index=i.length,i.push(new C.Token(r.slice(o,s),u))}o=s+1}}return i},C.tokenizer.separator=/[\s\-]+/
/*!
 * lunr.Pipeline
 * Copyright (C) 2020 Oliver Nightingale
 */,C.Pipeline=function(){this._stack=[]},C.Pipeline.registeredFunctions=Object.create(null),C.Pipeline.registerFunction=function(e,t){t in this.registeredFunctions&&C.utils.warn("Overwriting existing registered function: "+t),e.label=t,C.Pipeline.registeredFunctions[e.label]=e},C.Pipeline.warnIfFunctionNotRegistered=function(e){e.label&&e.label in this.registeredFunctions||C.utils.warn("Function is not registered with pipeline. This may cause problems when serialising the index.\n",e)},C.Pipeline.load=function(e){var t=new C.Pipeline;return e.forEach((function(e){var r=C.Pipeline.registeredFunctions[e];if(!r)throw new Error("Cannot load unregistered function: "+e);t.add(r)})),t},C.Pipeline.prototype.add=function(){var e=Array.prototype.slice.call(arguments);e.forEach((function(e){C.Pipeline.warnIfFunctionNotRegistered(e),this._stack.push(e)}),this)},C.Pipeline.prototype.after=function(e,t){C.Pipeline.warnIfFunctionNotRegistered(t);var r=this._stack.indexOf(e);if(-1==r)throw new Error("Cannot find existingFn");r+=1,this._stack.splice(r,0,t)},C.Pipeline.prototype.before=function(e,t){C.Pipeline.warnIfFunctionNotRegistered(t);var r=this._stack.indexOf(e);if(-1==r)throw new Error("Cannot find existingFn");this._stack.splice(r,0,t)},C.Pipeline.prototype.remove=function(e){var t=this._stack.indexOf(e);-1!=t&&this._stack.splice(t,1)},C.Pipeline.prototype.run=function(e){for(var t=this._stack.length,r=0;r<t;r++){for(var n=this._stack[r],i=[],s=0;s<e.length;s++){var o=n(e[s],s,e);if(null!=o&&""!==o)if(Array.isArray(o))for(var a=0;a<o.length;a++)i.push(o[a]);else i.push(o)}e=i}return e},C.Pipeline.prototype.runString=function(e,t){var r=new C.Token(e,t);return this.run([r]).map((function(e){return e.toString()}))},C.Pipeline.prototype.reset=function(){this._stack=[]},C.Pipeline.prototype.toJSON=function(){return this._stack.map((function(e){return C.Pipeline.warnIfFunctionNotRegistered(e),e.label}))}
/*!
 * lunr.Vector
 * Copyright (C) 2020 Oliver Nightingale
 */,C.Vector=function(e){this._magnitude=0,this.elements=e||[]},C.Vector.prototype.positionForIndex=function(e){if(0==this.elements.length)return 0;for(var t=0,r=this.elements.length/2,n=r-t,i=Math.floor(n/2),s=this.elements[2*i];n>1&&(s<e&&(t=i),s>e&&(r=i),s!=e);)n=r-t,i=t+Math.floor(n/2),s=this.elements[2*i];return s==e||s>e?2*i:s<e?2*(i+1):void 0},C.Vector.prototype.insert=function(e,t){this.upsert(e,t,(function(){throw"duplicate index"}))},C.Vector.prototype.upsert=function(e,t,r){this._magnitude=0;var n=this.positionForIndex(e);this.elements[n]==e?this.elements[n+1]=r(this.elements[n+1],t):this.elements.splice(n,0,e,t)},C.Vector.prototype.magnitude=function(){if(this._magnitude)return this._magnitude;for(var e=0,t=this.elements.length,r=1;r<t;r+=2){var n=this.elements[r];e+=n*n}return this._magnitude=Math.sqrt(e)},C.Vector.prototype.dot=function(e){for(var t=0,r=this.elements,n=e.elements,i=r.length,s=n.length,o=0,a=0,u=0,l=0;u<i&&l<s;)(o=r[u])<(a=n[l])?u+=2:o>a?l+=2:o==a&&(t+=r[u+1]*n[l+1],u+=2,l+=2);return t},C.Vector.prototype.similarity=function(e){return this.dot(e)/this.magnitude()||0},C.Vector.prototype.toArray=function(){for(var e=new Array(this.elements.length/2),t=1,r=0;t<this.elements.length;t+=2,r++)e[r]=this.elements[t];return e},C.Vector.prototype.toJSON=function(){return this.elements}
/*!
 * lunr.stemmer
 * Copyright (C) 2020 Oliver Nightingale
 * Includes code from - http://tartarus.org/~martin/PorterStemmer/js.txt
 */,C.stemmer=(o={ational:"ate",tional:"tion",enci:"ence",anci:"ance",izer:"ize",bli:"ble",alli:"al",entli:"ent",eli:"e",ousli:"ous",ization:"ize",ation:"ate",ator:"ate",alism:"al",iveness:"ive",fulness:"ful",ousness:"ous",aliti:"al",iviti:"ive",biliti:"ble",logi:"log"},a={icate:"ic",ative:"",alize:"al",iciti:"ic",ical:"ic",ful:"",ness:""},u="[aeiouy]",l="[^aeiou][^aeiouy]*",c=new RegExp("^([^aeiou][^aeiouy]*)?[aeiouy][aeiou]*[^aeiou][^aeiouy]*"),h=new RegExp("^([^aeiou][^aeiouy]*)?[aeiouy][aeiou]*[^aeiou][^aeiouy]*[aeiouy][aeiou]*[^aeiou][^aeiouy]*"),d=new RegExp("^([^aeiou][^aeiouy]*)?[aeiouy][aeiou]*[^aeiou][^aeiouy]*([aeiouy][aeiou]*)?$"),f=new RegExp("^([^aeiou][^aeiouy]*)?[aeiouy]"),p=/^(.+?)(ss|i)es$/,y=/^(.+?)([^s])s$/,m=/^(.+?)eed$/,v=/^(.+?)(ed|ing)$/,g=/.$/,x=/(at|bl|iz)$/,w=new RegExp("([^aeiouylsz])\\1$"),L=new RegExp("^"+l+u+"[^aeiouwxy]$"),E=/^(.+?[^aeiou])y$/,b=/^(.+?)(ational|tional|enci|anci|izer|bli|alli|entli|eli|ousli|ization|ation|ator|alism|iveness|fulness|ousness|aliti|iviti|biliti|logi)$/,S=/^(.+?)(icate|ative|alize|iciti|ical|ful|ness)$/,k=/^(.+?)(al|ance|ence|er|ic|able|ible|ant|ement|ment|ent|ou|ism|ate|iti|ous|ive|ize)$/,Q=/^(.+?)(s|t)(ion)$/,O=/^(.+?)e$/,P=/ll$/,T=new RegExp("^"+l+u+"[^aeiouwxy]$"),_=function(e){var t,r,n,i,s,u,l;if(e.length<3)return e;if("y"==(n=e.substr(0,1))&&(e=n.toUpperCase()+e.substr(1)),s=y,(i=p).test(e)?e=e.replace(i,"$1$2"):s.test(e)&&(e=e.replace(s,"$1$2")),s=v,(i=m).test(e)){var _=i.exec(e);(i=c).test(_[1])&&(i=g,e=e.replace(i,""))}else s.test(e)&&(t=(_=s.exec(e))[1],(s=f).test(t)&&(u=w,l=L,(s=x).test(e=t)?e+="e":u.test(e)?(i=g,e=e.replace(i,"")):l.test(e)&&(e+="e")));return(i=E).test(e)&&(e=(t=(_=i.exec(e))[1])+"i"),(i=b).test(e)&&(t=(_=i.exec(e))[1],r=_[2],(i=c).test(t)&&(e=t+o[r])),(i=S).test(e)&&(t=(_=i.exec(e))[1],r=_[2],(i=c).test(t)&&(e=t+a[r])),s=Q,(i=k).test(e)?(t=(_=i.exec(e))[1],(i=h).test(t)&&(e=t)):s.test(e)&&(t=(_=s.exec(e))[1]+_[2],(s=h).test(t)&&(e=t)),(i=O).test(e)&&(t=(_=i.exec(e))[1],s=d,u=T,((i=h).test(t)||s.test(t)&&!u.test(t))&&(e=t)),s=h,(i=P).test(e)&&s.test(e)&&(i=g,e=e.replace(i,"")),"y"==n&&(e=n.toLowerCase()+e.substr(1)),e},function(e){return e.update(_)}),C.Pipeline.registerFunction(C.stemmer,"stemmer")
/*!
 * lunr.stopWordFilter
 * Copyright (C) 2020 Oliver Nightingale
 */,C.generateStopWordFilter=function(e){var t=e.reduce((function(e,t){return e[t]=t,e}),{});return function(e){if(e&&t[e.toString()]!==e.toString())return e}},C.stopWordFilter=C.generateStopWordFilter(["a","able","about","across","after","all","almost","also","am","among","an","and","any","are","as","at","be","because","been","but","by","can","cannot","could","dear","did","do","does","either","else","ever","every","for","from","get","got","had","has","have","he","her","hers","him","his","how","however","i","if","in","into","is","it","its","just","least","let","like","likely","may","me","might","most","must","my","neither","no","nor","not","of","off","often","on","only","or","other","our","own","rather","said","say","says","she","should","since","so","some","than","that","the","their","them","then","there","these","they","this","tis","to","too","twas","us","wants","was","we","were","what","when","where","which","while","who","whom","why","will","with","would","yet","you","your"]),C.Pipeline.registerFunction(C.stopWordFilter,"stopWordFilter")
/*!
 * lunr.trimmer
 * Copyright (C) 2020 Oliver Nightingale
 */,C.trimmer=function(e){return e.update((function(e){return e.replace(/^\W+/,"").replace(/\W+$/,"")}))},C.Pipeline.registerFunction(C.trimmer,"trimmer")
/*!
 * lunr.TokenSet
 * Copyright (C) 2020 Oliver Nightingale
 */,C.TokenSet=function(){this.final=!1,this.edges={},this.id=C.TokenSet._nextId,C.TokenSet._nextId+=1},C.TokenSet._nextId=1,C.TokenSet.fromArray=function(e){for(var t=new C.TokenSet.Builder,r=0,n=e.length;r<n;r++)t.insert(e[r]);return t.finish(),t.root},C.TokenSet.fromClause=function(e){return"editDistance"in e?C.TokenSet.fromFuzzyString(e.term,e.editDistance):C.TokenSet.fromString(e.term)},C.TokenSet.fromFuzzyString=function(e,t){for(var r=new C.TokenSet,n=[{node:r,editsRemaining:t,str:e}];n.length;){var i=n.pop();if(i.str.length>0){var s,o=i.str.charAt(0);o in i.node.edges?s=i.node.edges[o]:(s=new C.TokenSet,i.node.edges[o]=s),1==i.str.length&&(s.final=!0),n.push({node:s,editsRemaining:i.editsRemaining,str:i.str.slice(1)})}if(0!=i.editsRemaining){if("*"in i.node.edges)var a=i.node.edges["*"];else{a=new C.TokenSet;i.node.edges["*"]=a}if(0==i.str.length&&(a.final=!0),n.push({node:a,editsRemaining:i.editsRemaining-1,str:i.str}),i.str.length>1&&n.push({node:i.node,editsRemaining:i.editsRemaining-1,str:i.str.slice(1)}),1==i.str.length&&(i.node.final=!0),i.str.length>=1){if("*"in i.node.edges)var u=i.node.edges["*"];else{u=new C.TokenSet;i.node.edges["*"]=u}1==i.str.length&&(u.final=!0),n.push({node:u,editsRemaining:i.editsRemaining-1,str:i.str.slice(1)})}if(i.str.length>1){var l,c=i.str.charAt(0),h=i.str.charAt(1);h in i.node.edges?l=i.node.edges[h]:(l=new C.TokenSet,i.node.edges[h]=l),1==i.str.length&&(l.final=!0),n.push({node:l,editsRemaining:i.editsRemaining-1,str:c+i.str.slice(2)})}}}return r},C.TokenSet.fromString=function(e){for(var t=new C.TokenSet,r=t,n=0,i=e.length;n<i;n++){var s=e[n],o=n==i-1;if("*"==s)t.edges[s]=t,t.final=o;else{var a=new C.TokenSet;a.final=o,t.edges[s]=a,t=a}}return r},C.TokenSet.prototype.toArray=function(){for(var e=[],t=[{prefix:"",node:this}];t.length;){var r=t.pop(),n=Object.keys(r.node.edges),i=n.length;r.node.final&&(r.prefix.charAt(0),e.push(r.prefix));for(var s=0;s<i;s++){var o=n[s];t.push({prefix:r.prefix.concat(o),node:r.node.edges[o]})}}return e},C.TokenSet.prototype.toString=function(){if(this._str)return this._str;for(var e=this.final?"1":"0",t=Object.keys(this.edges).sort(),r=t.length,n=0;n<r;n++){var i=t[n];e=e+i+this.edges[i].id}return e},C.TokenSet.prototype.intersect=function(e){for(var t=new C.TokenSet,r=void 0,n=[{qNode:e,output:t,node:this}];n.length;){r=n.pop();for(var i=Object.keys(r.qNode.edges),s=i.length,o=Object.keys(r.node.edges),a=o.length,u=0;u<s;u++)for(var l=i[u],c=0;c<a;c++){var h=o[c];if(h==l||"*"==l){var d=r.node.edges[h],f=r.qNode.edges[l],p=d.final&&f.final,y=void 0;h in r.output.edges?(y=r.output.edges[h]).final=y.final||p:((y=new C.TokenSet).final=p,r.output.edges[h]=y),n.push({qNode:f,output:y,node:d})}}}return t},C.TokenSet.Builder=function(){this.previousWord="",this.root=new C.TokenSet,this.uncheckedNodes=[],this.minimizedNodes={}},C.TokenSet.Builder.prototype.insert=function(e){var t,r=0;if(e<this.previousWord)throw new Error("Out of order word insertion");for(var n=0;n<e.length&&n<this.previousWord.length&&e[n]==this.previousWord[n];n++)r++;this.minimize(r),t=0==this.uncheckedNodes.length?this.root:this.uncheckedNodes[this.uncheckedNodes.length-1].child;for(n=r;n<e.length;n++){var i=new C.TokenSet,s=e[n];t.edges[s]=i,this.uncheckedNodes.push({parent:t,char:s,child:i}),t=i}t.final=!0,this.previousWord=e},C.TokenSet.Builder.prototype.finish=function(){this.minimize(0)},C.TokenSet.Builder.prototype.minimize=function(e){for(var t=this.uncheckedNodes.length-1;t>=e;t--){var r=this.uncheckedNodes[t],n=r.child.toString();n in this.minimizedNodes?r.parent.edges[r.char]=this.minimizedNodes[n]:(r.child._str=n,this.minimizedNodes[n]=r.child),this.uncheckedNodes.pop()}}
/*!
 * lunr.Index
 * Copyright (C) 2020 Oliver Nightingale
 */,C.Index=function(e){this.invertedIndex=e.invertedIndex,this.fieldVectors=e.fieldVectors,this.tokenSet=e.tokenSet,this.fields=e.fields,this.pipeline=e.pipeline},C.Index.prototype.search=function(e){return this.query((function(t){new C.QueryParser(e,t).parse()}))},C.Index.prototype.query=function(e){for(var t=new C.Query(this.fields),r=Object.create(null),n=Object.create(null),i=Object.create(null),s=Object.create(null),o=Object.create(null),a=0;a<this.fields.length;a++)n[this.fields[a]]=new C.Vector;e.call(t,t);for(a=0;a<t.clauses.length;a++){var u=t.clauses[a],l=null,c=C.Set.empty;l=u.usePipeline?this.pipeline.runString(u.term,{fields:u.fields}):[u.term];for(var h=0;h<l.length;h++){var d=l[h];u.term=d;var f=C.TokenSet.fromClause(u),p=this.tokenSet.intersect(f).toArray();if(0===p.length&&u.presence===C.Query.presence.REQUIRED){for(var y=0;y<u.fields.length;y++){s[R=u.fields[y]]=C.Set.empty}break}for(var m=0;m<p.length;m++){var v=p[m],g=this.invertedIndex[v],x=g._index;for(y=0;y<u.fields.length;y++){var w=g[R=u.fields[y]],L=Object.keys(w),E=v+"/"+R,b=new C.Set(L);if(u.presence==C.Query.presence.REQUIRED&&(c=c.union(b),void 0===s[R]&&(s[R]=C.Set.complete)),u.presence!=C.Query.presence.PROHIBITED){if(n[R].upsert(x,u.boost,(function(e,t){return e+t})),!i[E]){for(var S=0;S<L.length;S++){var k,Q=L[S],O=new C.FieldRef(Q,R),P=w[Q];void 0===(k=r[O])?r[O]=new C.MatchData(v,R,P):k.add(v,R,P)}i[E]=!0}}else void 0===o[R]&&(o[R]=C.Set.empty),o[R]=o[R].union(b)}}}if(u.presence===C.Query.presence.REQUIRED)for(y=0;y<u.fields.length;y++){s[R=u.fields[y]]=s[R].intersect(c)}}var T=C.Set.complete,_=C.Set.empty;for(a=0;a<this.fields.length;a++){var R;s[R=this.fields[a]]&&(T=T.intersect(s[R])),o[R]&&(_=_.union(o[R]))}var I=Object.keys(r),j=[],F=Object.create(null);if(t.isNegated()){I=Object.keys(this.fieldVectors);for(a=0;a<I.length;a++){O=I[a];var N=C.FieldRef.fromString(O);r[O]=new C.MatchData}}for(a=0;a<I.length;a++){var D=(N=C.FieldRef.fromString(I[a])).docRef;if(T.contains(D)&&!_.contains(D)){var A,z=this.fieldVectors[N],q=n[N.fieldName].similarity(z);if(void 0!==(A=F[D]))A.score+=q,A.matchData.combine(r[N]);else{var V={ref:D,score:q,matchData:r[N]};F[D]=V,j.push(V)}}}return j.sort((function(e,t){return t.score-e.score}))},C.Index.prototype.toJSON=function(){var e=Object.keys(this.invertedIndex).sort().map((function(e){return[e,this.invertedIndex[e]]}),this),t=Object.keys(this.fieldVectors).map((function(e){return[e,this.fieldVectors[e].toJSON()]}),this);return{version:C.version,fields:this.fields,fieldVectors:t,invertedIndex:e,pipeline:this.pipeline.toJSON()}},C.Index.load=function(e){var t={},r={},n=e.fieldVectors,i=Object.create(null),s=e.invertedIndex,o=new C.TokenSet.Builder,a=C.Pipeline.load(e.pipeline);e.version!=C.version&&C.utils.warn("Version mismatch when loading serialised index. Current version of lunr '"+C.version+"' does not match serialized index '"+e.version+"'");for(var u=0;u<n.length;u++){var l=(h=n[u])[0],c=h[1];r[l]=new C.Vector(c)}for(u=0;u<s.length;u++){var h,d=(h=s[u])[0],f=h[1];o.insert(d),i[d]=f}return o.finish(),t.fields=e.fields,t.fieldVectors=r,t.invertedIndex=i,t.tokenSet=o.root,t.pipeline=a,new C.Index(t)}
/*!
 * lunr.Builder
 * Copyright (C) 2020 Oliver Nightingale
 */,C.Builder=function(){this._ref="id",this._fields=Object.create(null),this._documents=Object.create(null),this.invertedIndex=Object.create(null),this.fieldTermFrequencies={},this.fieldLengths={},this.tokenizer=C.tokenizer,this.pipeline=new C.Pipeline,this.searchPipeline=new C.Pipeline,this.documentCount=0,this._b=.75,this._k1=1.2,this.termIndex=0,this.metadataWhitelist=[]},C.Builder.prototype.ref=function(e){this._ref=e},C.Builder.prototype.field=function(e,t){if(/\//.test(e))throw new RangeError("Field '"+e+"' contains illegal character '/'");this._fields[e]=t||{}},C.Builder.prototype.b=function(e){this._b=e<0?0:e>1?1:e},C.Builder.prototype.k1=function(e){this._k1=e},C.Builder.prototype.add=function(e,t){var r=e[this._ref],n=Object.keys(this._fields);this._documents[r]=t||{},this.documentCount+=1;for(var i=0;i<n.length;i++){var s=n[i],o=this._fields[s].extractor,a=o?o(e):e[s],u=this.tokenizer(a,{fields:[s]}),l=this.pipeline.run(u),c=new C.FieldRef(r,s),h=Object.create(null);this.fieldTermFrequencies[c]=h,this.fieldLengths[c]=0,this.fieldLengths[c]+=l.length;for(var d=0;d<l.length;d++){var f=l[d];if(null==h[f]&&(h[f]=0),h[f]+=1,null==this.invertedIndex[f]){var p=Object.create(null);p._index=this.termIndex,this.termIndex+=1;for(var y=0;y<n.length;y++)p[n[y]]=Object.create(null);this.invertedIndex[f]=p}null==this.invertedIndex[f][s][r]&&(this.invertedIndex[f][s][r]=Object.create(null));for(var m=0;m<this.metadataWhitelist.length;m++){var v=this.metadataWhitelist[m],g=f.metadata[v];null==this.invertedIndex[f][s][r][v]&&(this.invertedIndex[f][s][r][v]=[]),this.invertedIndex[f][s][r][v].push(g)}}}},C.Builder.prototype.calculateAverageFieldLengths=function(){for(var e=Object.keys(this.fieldLengths),t=e.length,r={},n={},i=0;i<t;i++){var s=C.FieldRef.fromString(e[i]),o=s.fieldName;n[o]||(n[o]=0),n[o]+=1,r[o]||(r[o]=0),r[o]+=this.fieldLengths[s]}var a=Object.keys(this._fields);for(i=0;i<a.length;i++){var u=a[i];r[u]=r[u]/n[u]}this.averageFieldLength=r},C.Builder.prototype.createFieldVectors=function(){for(var e={},t=Object.keys(this.fieldTermFrequencies),r=t.length,n=Object.create(null),i=0;i<r;i++){for(var s=C.FieldRef.fromString(t[i]),o=s.fieldName,a=this.fieldLengths[s],u=new C.Vector,l=this.fieldTermFrequencies[s],c=Object.keys(l),h=c.length,d=this._fields[o].boost||1,f=this._documents[s.docRef].boost||1,p=0;p<h;p++){var y,m,v,g=c[p],x=l[g],w=this.invertedIndex[g]._index;void 0===n[g]?(y=C.idf(this.invertedIndex[g],this.documentCount),n[g]=y):y=n[g],m=y*((this._k1+1)*x)/(this._k1*(1-this._b+this._b*(a/this.averageFieldLength[o]))+x),m*=d,m*=f,v=Math.round(1e3*m)/1e3,u.insert(w,v)}e[s]=u}this.fieldVectors=e},C.Builder.prototype.createTokenSet=function(){this.tokenSet=C.TokenSet.fromArray(Object.keys(this.invertedIndex).sort())},C.Builder.prototype.build=function(){return this.calculateAverageFieldLengths(),this.createFieldVectors(),this.createTokenSet(),new C.Index({invertedIndex:this.invertedIndex,fieldVectors:this.fieldVectors,tokenSet:this.tokenSet,fields:Object.keys(this._fields),pipeline:this.searchPipeline})},C.Builder.prototype.use=function(e){var t=Array.prototype.slice.call(arguments,1);t.unshift(this),e.apply(this,t)},C.MatchData=function(e,t,r){for(var n=Object.create(null),i=Object.keys(r||{}),s=0;s<i.length;s++){var o=i[s];n[o]=r[o].slice()}this.metadata=Object.create(null),void 0!==e&&(this.metadata[e]=Object.create(null),this.metadata[e][t]=n)},C.MatchData.prototype.combine=function(e){for(var t=Object.keys(e.metadata),r=0;r<t.length;r++){var n=t[r],i=Object.keys(e.metadata[n]);null==this.metadata[n]&&(this.metadata[n]=Object.create(null));for(var s=0;s<i.length;s++){var o=i[s],a=Object.keys(e.metadata[n][o]);null==this.metadata[n][o]&&(this.metadata[n][o]=Object.create(null));for(var u=0;u<a.length;u++){var l=a[u];null==this.metadata[n][o][l]?this.metadata[n][o][l]=e.metadata[n][o][l]:this.metadata[n][o][l]=this.metadata[n][o][l].concat(e.metadata[n][o][l])}}}},C.MatchData.prototype.add=function(e,t,r){if(!(e in this.metadata))return this.metadata[e]=Object.create(null),void(this.metadata[e][t]=r);if(t in this.metadata[e])for(var n=Object.keys(r),i=0;i<n.length;i++){var s=n[i];s in this.metadata[e][t]?this.metadata[e][t][s]=this.metadata[e][t][s].concat(r[s]):this.metadata[e][t][s]=r[s]}else this.metadata[e][t]=r},C.Query=function(e){this.clauses=[],this.allFields=e},C.Query.wildcard=new String("*"),C.Query.wildcard.NONE=0,C.Query.wildcard.LEADING=1,C.Query.wildcard.TRAILING=2,C.Query.presence={OPTIONAL:1,REQUIRED:2,PROHIBITED:3},C.Query.prototype.clause=function(e){return"fields"in e||(e.fields=this.allFields),"boost"in e||(e.boost=1),"usePipeline"in e||(e.usePipeline=!0),"wildcard"in e||(e.wildcard=C.Query.wildcard.NONE),e.wildcard&C.Query.wildcard.LEADING&&e.term.charAt(0)!=C.Query.wildcard&&(e.term="*"+e.term),e.wildcard&C.Query.wildcard.TRAILING&&e.term.slice(-1)!=C.Query.wildcard&&(e.term=e.term+"*"),"presence"in e||(e.presence=C.Query.presence.OPTIONAL),this.clauses.push(e),this},C.Query.prototype.isNegated=function(){for(var e=0;e<this.clauses.length;e++)if(this.clauses[e].presence!=C.Query.presence.PROHIBITED)return!1;return!0},C.Query.prototype.term=function(e,t){if(Array.isArray(e))return e.forEach((function(e){this.term(e,C.utils.clone(t))}),this),this;var r=t||{};return r.term=e.toString(),this.clause(r),this},C.QueryParseError=function(e,t,r){this.name="QueryParseError",this.message=e,this.start=t,this.end=r},C.QueryParseError.prototype=new Error,C.QueryLexer=function(e){this.lexemes=[],this.str=e,this.length=e.length,this.pos=0,this.start=0,this.escapeCharPositions=[]},C.QueryLexer.prototype.run=function(){for(var e=C.QueryLexer.lexText;e;)e=e(this)},C.QueryLexer.prototype.sliceString=function(){for(var e=[],t=this.start,r=this.pos,n=0;n<this.escapeCharPositions.length;n++)r=this.escapeCharPositions[n],e.push(this.str.slice(t,r)),t=r+1;return e.push(this.str.slice(t,this.pos)),this.escapeCharPositions.length=0,e.join("")},C.QueryLexer.prototype.emit=function(e){this.lexemes.push({type:e,str:this.sliceString(),start:this.start,end:this.pos}),this.start=this.pos},C.QueryLexer.prototype.escapeCharacter=function(){this.escapeCharPositions.push(this.pos-1),this.pos+=1},C.QueryLexer.prototype.next=function(){if(this.pos>=this.length)return C.QueryLexer.EOS;var e=this.str.charAt(this.pos);return this.pos+=1,e},C.QueryLexer.prototype.width=function(){return this.pos-this.start},C.QueryLexer.prototype.ignore=function(){this.start==this.pos&&(this.pos+=1),this.start=this.pos},C.QueryLexer.prototype.backup=function(){this.pos-=1},C.QueryLexer.prototype.acceptDigitRun=function(){var e,t;do{t=(e=this.next()).charCodeAt(0)}while(t>47&&t<58);e!=C.QueryLexer.EOS&&this.backup()},C.QueryLexer.prototype.more=function(){return this.pos<this.length},C.QueryLexer.EOS="EOS",C.QueryLexer.FIELD="FIELD",C.QueryLexer.TERM="TERM",C.QueryLexer.EDIT_DISTANCE="EDIT_DISTANCE",C.QueryLexer.BOOST="BOOST",C.QueryLexer.PRESENCE="PRESENCE",C.QueryLexer.lexField=function(e){return e.backup(),e.emit(C.QueryLexer.FIELD),e.ignore(),C.QueryLexer.lexText},C.QueryLexer.lexTerm=function(e){if(e.width()>1&&(e.backup(),e.emit(C.QueryLexer.TERM)),e.ignore(),e.more())return C.QueryLexer.lexText},C.QueryLexer.lexEditDistance=function(e){return e.ignore(),e.acceptDigitRun(),e.emit(C.QueryLexer.EDIT_DISTANCE),C.QueryLexer.lexText},C.QueryLexer.lexBoost=function(e){return e.ignore(),e.acceptDigitRun(),e.emit(C.QueryLexer.BOOST),C.QueryLexer.lexText},C.QueryLexer.lexEOS=function(e){e.width()>0&&e.emit(C.QueryLexer.TERM)},C.QueryLexer.termSeparator=C.tokenizer.separator,C.QueryLexer.lexText=function(e){for(;;){var t=e.next();if(t==C.QueryLexer.EOS)return C.QueryLexer.lexEOS;if(92!=t.charCodeAt(0)){if(":"==t)return C.QueryLexer.lexField;if("~"==t)return e.backup(),e.width()>0&&e.emit(C.QueryLexer.TERM),C.QueryLexer.lexEditDistance;if("^"==t)return e.backup(),e.width()>0&&e.emit(C.QueryLexer.TERM),C.QueryLexer.lexBoost;if("+"==t&&1===e.width())return e.emit(C.QueryLexer.PRESENCE),C.QueryLexer.lexText;if("-"==t&&1===e.width())return e.emit(C.QueryLexer.PRESENCE),C.QueryLexer.lexText;if(t.match(C.QueryLexer.termSeparator))return C.QueryLexer.lexTerm}else e.escapeCharacter()}},C.QueryParser=function(e,t){this.lexer=new C.QueryLexer(e),this.query=t,this.currentClause={},this.lexemeIdx=0},C.QueryParser.prototype.parse=function(){this.lexer.run(),this.lexemes=this.lexer.lexemes;for(var e=C.QueryParser.parseClause;e;)e=e(this);return this.query},C.QueryParser.prototype.peekLexeme=function(){return this.lexemes[this.lexemeIdx]},C.QueryParser.prototype.consumeLexeme=function(){var e=this.peekLexeme();return this.lexemeIdx+=1,e},C.QueryParser.prototype.nextClause=function(){var e=this.currentClause;this.query.clause(e),this.currentClause={}},C.QueryParser.parseClause=function(e){var t=e.peekLexeme();if(null!=t)switch(t.type){case C.QueryLexer.PRESENCE:return C.QueryParser.parsePresence;case C.QueryLexer.FIELD:return C.QueryParser.parseField;case C.QueryLexer.TERM:return C.QueryParser.parseTerm;default:var r="expected either a field or a term, found "+t.type;throw t.str.length>=1&&(r+=" with value '"+t.str+"'"),new C.QueryParseError(r,t.start,t.end)}},C.QueryParser.parsePresence=function(e){var t=e.consumeLexeme();if(null!=t){switch(t.str){case"-":e.currentClause.presence=C.Query.presence.PROHIBITED;break;case"+":e.currentClause.presence=C.Query.presence.REQUIRED;break;default:var r="unrecognised presence operator'"+t.str+"'";throw new C.QueryParseError(r,t.start,t.end)}var n=e.peekLexeme();if(null==n){r="expecting term or field, found nothing";throw new C.QueryParseError(r,t.start,t.end)}switch(n.type){case C.QueryLexer.FIELD:return C.QueryParser.parseField;case C.QueryLexer.TERM:return C.QueryParser.parseTerm;default:r="expecting term or field, found '"+n.type+"'";throw new C.QueryParseError(r,n.start,n.end)}}},C.QueryParser.parseField=function(e){var t=e.consumeLexeme();if(null!=t){if(-1==e.query.allFields.indexOf(t.str)){var r=e.query.allFields.map((function(e){return"'"+e+"'"})).join(", "),n="unrecognised field '"+t.str+"', possible fields: "+r;throw new C.QueryParseError(n,t.start,t.end)}e.currentClause.fields=[t.str];var i=e.peekLexeme();if(null==i){n="expecting term, found nothing";throw new C.QueryParseError(n,t.start,t.end)}switch(i.type){case C.QueryLexer.TERM:return C.QueryParser.parseTerm;default:n="expecting term, found '"+i.type+"'";throw new C.QueryParseError(n,i.start,i.end)}}},C.QueryParser.parseTerm=function(e){var t=e.consumeLexeme();if(null!=t){e.currentClause.term=t.str.toLowerCase(),-1!=t.str.indexOf("*")&&(e.currentClause.usePipeline=!1);var r=e.peekLexeme();if(null!=r)switch(r.type){case C.QueryLexer.TERM:return e.nextClause(),C.QueryParser.parseTerm;case C.QueryLexer.FIELD:return e.nextClause(),C.QueryParser.parseField;case C.QueryLexer.EDIT_DISTANCE:return C.QueryParser.parseEditDistance;case C.QueryLexer.BOOST:return C.QueryParser.parseBoost;case C.QueryLexer.PRESENCE:return e.nextClause(),C.QueryParser.parsePresence;default:var n="Unexpected lexeme type '"+r.type+"'";throw new C.QueryParseError(n,r.start,r.end)}else e.nextClause()}},C.QueryParser.parseEditDistance=function(e){var t=e.consumeLexeme();if(null!=t){var r=parseInt(t.str,10);if(isNaN(r)){var n="edit distance must be numeric";throw new C.QueryParseError(n,t.start,t.end)}e.currentClause.editDistance=r;var i=e.peekLexeme();if(null!=i)switch(i.type){case C.QueryLexer.TERM:return e.nextClause(),C.QueryParser.parseTerm;case C.QueryLexer.FIELD:return e.nextClause(),C.QueryParser.parseField;case C.QueryLexer.EDIT_DISTANCE:return C.QueryParser.parseEditDistance;case C.QueryLexer.BOOST:return C.QueryParser.parseBoost;case C.QueryLexer.PRESENCE:return e.nextClause(),C.QueryParser.parsePresence;default:n="Unexpected lexeme type '"+i.type+"'";throw new C.QueryParseError(n,i.start,i.end)}else e.nextClause()}},C.QueryParser.parseBoost=function(e){var t=e.consumeLexeme();if(null!=t){var r=parseInt(t.str,10);if(isNaN(r)){var n="boost must be numeric";throw new C.QueryParseError(n,t.start,t.end)}e.currentClause.boost=r;var i=e.peekLexeme();if(null!=i)switch(i.type){case C.QueryLexer.TERM:return e.nextClause(),C.QueryParser.parseTerm;case C.QueryLexer.FIELD:return e.nextClause(),C.QueryParser.parseField;case C.QueryLexer.EDIT_DISTANCE:return C.QueryParser.parseEditDistance;case C.QueryLexer.BOOST:return C.QueryParser.parseBoost;case C.QueryLexer.PRESENCE:return e.nextClause(),C.QueryParser.parsePresence;default:n="Unexpected lexeme type '"+i.type+"'";throw new C.QueryParseError(n,i.start,i.end)}else e.nextClause()}},void 0===(i="function"==typeof(n=function(){return C})?n.call(t,r,t,e):n)||(e.exports=i)}()},function(e,t,r){},function(e,t,r){"use strict";r.r(t);var n=[];function i(e,t){n.push({selector:t,constructor:e})}var s,o,a=function(){function e(){this.createComponents(document.body)}return e.prototype.createComponents=function(e){n.forEach((function(t){e.querySelectorAll(t.selector).forEach((function(e){e.dataset.hasInstance||(new t.constructor({el:e}),e.dataset.hasInstance=String(!0))}))}))},e}(),u=function(e){this.el=e.el},l=r(0),c=(s=function(e,t){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}s(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});!function(e){e[e.Idle=0]="Idle",e[e.Loading=1]="Loading",e[e.Ready=2]="Ready",e[e.Failure=3]="Failure"}(o||(o={}));var h=function(e){function t(t){var r=e.call(this,t)||this;r.query="",r.loadingState=o.Idle,r.hasFocus=!1,r.preventPress=!1,r.data=null,r.index=null,r.resultClicked=!1;var n=document.querySelector("#tsd-search-field"),i=document.querySelector(".results");if(!n||!i)throw new Error("The input field or the result list wrapper are not found");return r.field=n,r.results=i,r.base=r.el.dataset.base+"/",r.bindEvents(),r}return c(t,e),t.prototype.loadIndex=function(){var e=this;if(this.loadingState==o.Idle&&!this.data){setTimeout((function(){e.loadingState==o.Idle&&e.setLoadingState(o.Loading)}),500);var t=this.el.dataset.index;t?fetch(t).then((function(e){if(!e.ok)throw new Error("The search index is missing");return e.json()})).then((function(t){e.data=t,e.index=l.Index.load(t.index),e.setLoadingState(o.Ready)})).catch((function(t){console.error(t),e.setLoadingState(o.Failure)})):this.setLoadingState(o.Failure)}},t.prototype.updateResults=function(){if(this.loadingState==o.Ready&&(this.results.textContent="",this.query&&this.index&&this.data)){var e=this.index.search("*"+this.query+"*");0===e.length&&(e=this.index.search("*"+this.query+"~1*"));for(var t=0,r=Math.min(10,e.length);t<r;t++){var n=this.data.rows[Number(e[t].ref)],i=n.name.replace(new RegExp(this.query,"i"),(function(e){return"<b>"+e+"</b>"})),s=n.parent||"";(s=s.replace(new RegExp(this.query,"i"),(function(e){return"<b>"+e+"</b>"})))&&(i='<span class="parent">'+s+".</span>"+i);var a=document.createElement("li");a.classList.value=n.classes,a.innerHTML='\n                    <a href="'+(this.base+n.url)+'" class="tsd-kind-icon">'+i+"</a>\n                ",this.results.appendChild(a)}}},t.prototype.setLoadingState=function(e){this.loadingState!=e&&(this.el.classList.remove(o[this.loadingState].toLowerCase()),this.loadingState=e,this.el.classList.add(o[this.loadingState].toLowerCase()),this.updateResults())},t.prototype.setHasFocus=function(e){this.hasFocus!=e&&(this.hasFocus=e,this.el.classList.toggle("has-focus"),e?(this.setQuery(""),this.field.value=""):this.field.value=this.query)},t.prototype.setQuery=function(e){this.query=e.trim(),this.updateResults()},t.prototype.setCurrentResult=function(e){var t=this.results.querySelector(".current");if(t){var r=1==e?t.nextElementSibling:t.previousElementSibling;r&&(t.classList.remove("current"),r.classList.add("current"))}else(t=this.results.querySelector(1==e?"li:first-child":"li:last-child"))&&t.classList.add("current")},t.prototype.gotoCurrentResult=function(){var e=this.results.querySelector(".current");if(e||(e=this.results.querySelector("li:first-child")),e){var t=e.querySelector("a");t&&(window.location.href=t.href),this.field.blur()}},t.prototype.bindEvents=function(){var e=this;this.results.addEventListener("mousedown",(function(){e.resultClicked=!0})),this.results.addEventListener("mouseup",(function(){e.resultClicked=!1,e.setHasFocus(!1)})),this.field.addEventListener("focusin",(function(){e.setHasFocus(!0),e.loadIndex()})),this.field.addEventListener("focusout",(function(){e.resultClicked?e.resultClicked=!1:setTimeout((function(){return e.setHasFocus(!1)}),100)})),this.field.addEventListener("input",(function(){e.setQuery(e.field.value)})),this.field.addEventListener("keydown",(function(t){13==t.keyCode||27==t.keyCode||38==t.keyCode||40==t.keyCode?(e.preventPress=!0,t.preventDefault(),13==t.keyCode?e.gotoCurrentResult():27==t.keyCode?e.field.blur():38==t.keyCode?e.setCurrentResult(-1):40==t.keyCode&&e.setCurrentResult(1)):e.preventPress=!1})),this.field.addEventListener("keypress",(function(t){e.preventPress&&t.preventDefault()})),document.body.addEventListener("keydown",(function(t){t.altKey||t.ctrlKey||t.metaKey||!e.hasFocus&&t.keyCode>47&&t.keyCode<112&&e.field.focus()}))},t}(u),d=function(){function e(){this.listeners={}}return e.prototype.addEventListener=function(e,t){e in this.listeners||(this.listeners[e]=[]),this.listeners[e].push(t)},e.prototype.removeEventListener=function(e,t){if(e in this.listeners)for(var r=this.listeners[e],n=0,i=r.length;n<i;n++)if(r[n]===t)return void r.splice(n,1)},e.prototype.dispatchEvent=function(e){if(!(e.type in this.listeners))return!0;for(var t=this.listeners[e.type].slice(),r=0,n=t.length;r<n;r++)t[r].call(this,e);return!e.defaultPrevented},e}(),f=function(e,t){void 0===t&&(t=100);var r=Date.now();return function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];r+t-Date.now()<0&&(e.apply(void 0,n),r=Date.now())}},p=function(){var e=function(t,r){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),y=function(e){function t(){var t=e.call(this)||this;return t.scrollTop=0,t.lastY=0,t.width=0,t.height=0,t.showToolbar=!0,t.toolbar=document.querySelector(".tsd-page-toolbar"),t.secondaryNav=document.querySelector(".tsd-navigation.secondary"),window.addEventListener("scroll",f((function(){return t.onScroll()}),10)),window.addEventListener("resize",f((function(){return t.onResize()}),10)),t.onResize(),t.onScroll(),t}return p(t,e),t.prototype.triggerResize=function(){var e=new CustomEvent("resize",{detail:{width:this.width,height:this.height}});this.dispatchEvent(e)},t.prototype.onResize=function(){this.width=window.innerWidth||0,this.height=window.innerHeight||0;var e=new CustomEvent("resize",{detail:{width:this.width,height:this.height}});this.dispatchEvent(e)},t.prototype.onScroll=function(){this.scrollTop=window.scrollY||0;var e=new CustomEvent("scroll",{detail:{scrollTop:this.scrollTop}});this.dispatchEvent(e),this.hideShowToolbar()},t.prototype.hideShowToolbar=function(){var e=this.showToolbar;this.showToolbar=this.lastY>=this.scrollTop||0===this.scrollTop,e!==this.showToolbar&&(this.toolbar.classList.toggle("tsd-page-toolbar--hide"),this.secondaryNav.classList.toggle("tsd-navigation--toolbar-hide")),this.lastY=this.scrollTop},t.instance=new t,t}(d),m=function(){var e=function(t,r){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),v=function(e){function t(t){var r=e.call(this,t)||this;return r.anchors=[],r.index=-1,y.instance.addEventListener("resize",(function(){return r.onResize()})),y.instance.addEventListener("scroll",(function(e){return r.onScroll(e)})),r.createAnchors(),r}return m(t,e),t.prototype.createAnchors=function(){var e=this,t=window.location.href;-1!=t.indexOf("#")&&(t=t.substr(0,t.indexOf("#"))),this.el.querySelectorAll("a").forEach((function(r){var n=r.href;if(-1!=n.indexOf("#")&&n.substr(0,t.length)==t){var i=n.substr(n.indexOf("#")+1),s=document.querySelector("a.tsd-anchor[name="+i+"]"),o=r.parentNode;s&&o&&e.anchors.push({link:o,anchor:s,position:0})}})),this.onResize()},t.prototype.onResize=function(){for(var e,t=0,r=this.anchors.length;t<r;t++){var n=(e=this.anchors[t]).anchor.getBoundingClientRect();e.position=n.top+document.body.scrollTop}this.anchors.sort((function(e,t){return e.position-t.position}));var i=new CustomEvent("scroll",{detail:{scrollTop:y.instance.scrollTop}});this.onScroll(i)},t.prototype.onScroll=function(e){for(var t=e.detail.scrollTop+5,r=this.anchors,n=r.length-1,i=this.index;i>-1&&r[i].position>t;)i-=1;for(;i<n&&r[i+1].position<t;)i+=1;this.index!=i&&(this.index>-1&&this.anchors[this.index].link.classList.remove("focus"),this.index=i,this.index>-1&&this.anchors[this.index].link.classList.add("focus"))},t}(u),g=function(){var e=function(t,r){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),x=function(){function e(e,t){this.signature=e,this.description=t}return e.prototype.addClass=function(e){return this.signature.classList.add(e),this.description.classList.add(e),this},e.prototype.removeClass=function(e){return this.signature.classList.remove(e),this.description.classList.remove(e),this},e}(),w=function(e){function t(t){var r=e.call(this,t)||this;return r.groups=[],r.index=-1,r.createGroups(),r.container&&(r.el.classList.add("active"),Array.from(r.el.children).forEach((function(e){e.addEventListener("touchstart",(function(e){return r.onClick(e)})),e.addEventListener("click",(function(e){return r.onClick(e)}))})),r.container.classList.add("active"),r.setIndex(0)),r}return g(t,e),t.prototype.setIndex=function(e){if(e<0&&(e=0),e>this.groups.length-1&&(e=this.groups.length-1),this.index!=e){var t=this.groups[e];if(this.index>-1){var r=this.groups[this.index];r.removeClass("current").addClass("fade-out"),t.addClass("current"),t.addClass("fade-in"),y.instance.triggerResize(),setTimeout((function(){r.removeClass("fade-out"),t.removeClass("fade-in")}),300)}else t.addClass("current"),y.instance.triggerResize();this.index=e}},t.prototype.createGroups=function(){var e=this.el.children;if(!(e.length<2)){this.container=this.el.nextElementSibling;var t=this.container.children;this.groups=[];for(var r=0;r<e.length;r++)this.groups.push(new x(e[r],t[r]))}},t.prototype.onClick=function(e){var t=this;this.groups.forEach((function(r,n){r.signature===e.currentTarget&&t.setIndex(n)}))},t}(u),L="mousedown",E="mousemove",b="mouseup",S={x:0,y:0},k=!1,Q=!1,O=!1,P=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);document.documentElement.classList.add(P?"is-mobile":"not-mobile"),P&&"ontouchstart"in document.documentElement&&(!0,L="touchstart",E="touchmove",b="touchend"),document.addEventListener(L,(function(e){Q=!0,O=!1;var t="touchstart"==L?e.targetTouches[0]:e;S.y=t.pageY||0,S.x=t.pageX||0})),document.addEventListener(E,(function(e){if(Q&&!O){var t="touchstart"==L?e.targetTouches[0]:e,r=S.x-(t.pageX||0),n=S.y-(t.pageY||0);O=Math.sqrt(r*r+n*n)>10}})),document.addEventListener(b,(function(){Q=!1})),document.addEventListener("click",(function(e){k&&(e.preventDefault(),e.stopImmediatePropagation(),k=!1)}));var T=function(){var e=function(t,r){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),_=function(e){function t(t){var r=e.call(this,t)||this;return r.className=r.el.dataset.toggle||"",r.el.addEventListener(b,(function(e){return r.onPointerUp(e)})),r.el.addEventListener("click",(function(e){return e.preventDefault()})),document.addEventListener(L,(function(e){return r.onDocumentPointerDown(e)})),document.addEventListener(b,(function(e){return r.onDocumentPointerUp(e)})),r}return T(t,e),t.prototype.setActive=function(e){if(this.active!=e){this.active=e,document.documentElement.classList.toggle("has-"+this.className,e),this.el.classList.toggle("active",e);var t=(this.active?"to-has-":"from-has-")+this.className;document.documentElement.classList.add(t),setTimeout((function(){return document.documentElement.classList.remove(t)}),500)}},t.prototype.onPointerUp=function(e){O||(this.setActive(!0),e.preventDefault())},t.prototype.onDocumentPointerDown=function(e){if(this.active){if(e.target.closest(".col-menu, .tsd-filter-group"))return;this.setActive(!1)}},t.prototype.onDocumentPointerUp=function(e){var t=this;if(!O&&this.active&&e.target.closest(".col-menu")){var r=e.target.closest("a");if(r){var n=window.location.href;-1!=n.indexOf("#")&&(n=n.substr(0,n.indexOf("#"))),r.href.substr(0,n.length)==n&&setTimeout((function(){return t.setActive(!1)}),250)}}},t}(u),C=function(){var e=function(t,r){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),R=function(){function e(e,t){this.key=e,this.value=t,this.defaultValue=t,this.initialize(),window.localStorage[this.key]&&this.setValue(this.fromLocalStorage(window.localStorage[this.key]))}return e.prototype.initialize=function(){},e.prototype.setValue=function(e){if(this.value!=e){var t=this.value;this.value=e,window.localStorage[this.key]=this.toLocalStorage(e),this.handleValueChange(t,e)}},e}(),I=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return C(t,e),t.prototype.initialize=function(){var e=this,t=document.querySelector("#tsd-filter-"+this.key);t&&(this.checkbox=t,this.checkbox.addEventListener("change",(function(){e.setValue(e.checkbox.checked)})))},t.prototype.handleValueChange=function(e,t){this.checkbox&&(this.checkbox.checked=this.value,document.documentElement.classList.toggle("toggle-"+this.key,this.value!=this.defaultValue))},t.prototype.fromLocalStorage=function(e){return"true"==e},t.prototype.toLocalStorage=function(e){return e?"true":"false"},t}(R),j=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return C(t,e),t.prototype.initialize=function(){var e=this;document.documentElement.classList.add("toggle-"+this.key+this.value);var t=document.querySelector("#tsd-filter-"+this.key);if(t){this.select=t;var r=function(){e.select.classList.add("active")};this.select.addEventListener(L,r),this.select.addEventListener("mouseover",r),this.select.addEventListener("mouseleave",(function(){e.select.classList.remove("active")})),this.select.querySelectorAll("li").forEach((function(r){r.addEventListener(b,(function(r){t.classList.remove("active"),e.setValue(r.target.dataset.value||"")}))})),document.addEventListener(L,(function(t){e.select.contains(t.target)||e.select.classList.remove("active")}))}},t.prototype.handleValueChange=function(e,t){this.select.querySelectorAll("li.selected").forEach((function(e){e.classList.remove("selected")}));var r=this.select.querySelector('li[data-value="'+t+'"]'),n=this.select.querySelector(".tsd-select-label");r&&n&&(r.classList.add("selected"),n.textContent=r.textContent),document.documentElement.classList.remove("toggle-"+e),document.documentElement.classList.add("toggle-"+t)},t.prototype.fromLocalStorage=function(e){return e},t.prototype.toLocalStorage=function(e){return e},t}(R),F=function(e){function t(t){var r=e.call(this,t)||this;return r.optionVisibility=new j("visibility","private"),r.optionInherited=new I("inherited",!0),r.optionExternals=new I("externals",!0),r.optionOnlyExported=new I("only-exported",!1),r}return C(t,e),t.isSupported=function(){try{return void 0!==window.localStorage}catch(e){return!1}},t}(u);r(1);i(h,"#tsd-search"),i(v,".menu-highlight"),i(w,".tsd-signatures"),i(_,"a[data-toggle]"),F.isSupported()?i(F,"#tsd-filter"):document.documentElement.classList.add("no-filter");var N=new a;Object.defineProperty(window,"app",{value:N})}]);