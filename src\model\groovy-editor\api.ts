import axios from "../../core/axios/index"
import type * as dto from "../../def/index"

export default class GroovyEditorApi {
    constructor(private codeFileId: string) {}
    public query() {
        return axios.get<dto.GroovyEditorTypes.queryApiResult>(
            `general/configs/codeFile?fileId=${this.codeFileId}`
        )
    }

    public async save(code: string) {
        const formdata = new FormData()
        formdata.append("text", code)
        await axios.post<void>(
            `general/configs/codeFile/save?fileId=${this.codeFileId}`,
            formdata
        )
        this.reload()
    }

    private reload() {
        return axios.post<void>(`general/entrances/reload`)
    }
}
