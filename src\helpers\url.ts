const URL_REGEX =
    // eslint-disable-next-line no-useless-escape
    /((?:(?:https?|ftp|file):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[A-Z0-9+&@#\/%=~_|$]))/gim

const LINE_URL_REGEX =
    // eslint-disable-next-line no-useless-escape
    /((?:(?:https?|ftp|file):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[A-Z0-9+&@#\/%=~_|$]))/i

const mobile_reg = /(1[0-9_]{8,14})/gi

export function replaceText2Link(text: string) {
    return text.replace(URL_REGEX, '<a href="$1" target="_blank">$1</a>')
}

export function replaceText2Dom(text: string, className: string) {
    return text.replace(URL_REGEX, `<span class="${className}">$1</span>`)
}

export function replacePhone2Dom(text: string, className: string) {
    return text.replace(
        mobile_reg,
        `<a href="tel:$1" class="${className}">$1</a>`
    )
}

export function isUrl(text: string) {
    return LINE_URL_REGEX.test(text)
}

export function isAccessibleUrl(url: string) {
    return url && (url.startsWith("blob") || url.startsWith("http"))
}
