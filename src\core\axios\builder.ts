import { AxiosRequestConfig } from "axios"

import { global } from "../global"
import { isTokenValid } from "../token-checker"

/**
 * 有效的token缓存，避免每次都解析
 */
const validToken = new Set<string>()

export function rebuildToken(config: AxiosRequestConfig) {
    if (config.headers && config.headers.anonymous) {
        delete config.headers.Authorization
        delete config.headers.anonymous
        return { xid: false }
    }
    if (config.headers && config.headers.Authorization) {
        return { xid: true }
    }
    if (config.headers && config.headers.xid) {
        const xidToken = global.getXidToken(config.headers.xid + "")
        if (xidToken && xidToken.token) {
            if (validToken.has(xidToken.token)) {
                config.headers.Authorization = xidToken.token
                delete config.headers.xid
                return { xid: true }
            }
            if (isTokenValid(xidToken.token)) {
                validToken.add(xidToken.token)
                config.headers.Authorization = xidToken.token
                delete config.headers.xid
                return { xid: true }
            } else {
                global.removeXidToken(config.headers.xid + "")
            }
        }
    }
    if (global.initData && global.initData.xid) {
        const xidToken = global.getXidToken(global.initData.xid + "")
        if (xidToken && xidToken.token) {
            if (validToken.has(xidToken.token)) {
                config.headers.Authorization = xidToken.token
                return { xid: true }
            }
            if (isTokenValid(xidToken.token)) {
                config.headers.Authorization = xidToken.token
                validToken.add(xidToken.token)
                return { xid: true }
            } else {
                global.removeXidToken(global.initData.xid + "")
            }
        }
    }
    if (global.jwtToken && !config.headers.Authorization) {
        if (validToken.has(global.jwtToken)) {
            config.headers.Authorization = global.jwtToken
            return { user: true }
        }
        if (isTokenValid(global.jwtToken)) {
            config.headers.Authorization = global.jwtToken
            validToken.add(global.jwtToken)
            return { user: true }
        }
    }
    return { xid: false, user: false }
}
