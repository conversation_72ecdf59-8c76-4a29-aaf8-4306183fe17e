<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>OperationType | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="operationtype.html">OperationType</a>
				</li>
			</ul>
			<h1>Enumeration OperationType</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Enumeration members</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="operationtype.html#add_remark" class="tsd-kind-icon">ADD_<wbr>REMARK</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="operationtype.html#cancel_task" class="tsd-kind-icon">CANCEL_<wbr>TASK</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="operationtype.html#change_handler" class="tsd-kind-icon">CHANGE_<wbr>HANDLER</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="operationtype.html#change_manager" class="tsd-kind-icon">CHANGE_<wbr>MANAGER</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="operationtype.html#change_state" class="tsd-kind-icon">CHANGE_<wbr>STATE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="operationtype.html#change_task" class="tsd-kind-icon">CHANGE_<wbr>TASK</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="operationtype.html#change_task_date" class="tsd-kind-icon">CHANGE_<wbr>TASK_<wbr>DATE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="operationtype.html#create_task" class="tsd-kind-icon">CREATE_<wbr>TASK</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="operationtype.html#edit_task" class="tsd-kind-icon">EDIT_<wbr>TASK</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="operationtype.html#finish_task" class="tsd-kind-icon">FINISH_<wbr>TASK</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="operationtype.html#restart_process" class="tsd-kind-icon">RESTART_<wbr>PROCESS</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="operationtype.html#rollback_state" class="tsd-kind-icon">ROLLBACK_<wbr>STATE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="operationtype.html#rollback_task" class="tsd-kind-icon">ROLLBACK_<wbr>TASK</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="operationtype.html#start_process" class="tsd-kind-icon">START_<wbr>PROCESS</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Enumeration members</h2>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="add_remark" class="tsd-anchor"></a>
					<h3>ADD_<wbr>REMARK</h3>
					<div class="tsd-signature tsd-kind-icon">ADD_<wbr>REMARK<span class="tsd-signature-symbol">:</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:22</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="cancel_task" class="tsd-anchor"></a>
					<h3>CANCEL_<wbr>TASK</h3>
					<div class="tsd-signature tsd-kind-icon">CANCEL_<wbr>TASK<span class="tsd-signature-symbol">:</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:19</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="change_handler" class="tsd-anchor"></a>
					<h3>CHANGE_<wbr>HANDLER</h3>
					<div class="tsd-signature tsd-kind-icon">CHANGE_<wbr>HANDLER<span class="tsd-signature-symbol">:</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:26</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="change_manager" class="tsd-anchor"></a>
					<h3>CHANGE_<wbr>MANAGER</h3>
					<div class="tsd-signature tsd-kind-icon">CHANGE_<wbr>MANAGER<span class="tsd-signature-symbol">:</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:25</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="change_state" class="tsd-anchor"></a>
					<h3>CHANGE_<wbr>STATE</h3>
					<div class="tsd-signature tsd-kind-icon">CHANGE_<wbr>STATE<span class="tsd-signature-symbol">:</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:16</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="change_task" class="tsd-anchor"></a>
					<h3>CHANGE_<wbr>TASK</h3>
					<div class="tsd-signature tsd-kind-icon">CHANGE_<wbr>TASK<span class="tsd-signature-symbol">:</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:21</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="change_task_date" class="tsd-anchor"></a>
					<h3>CHANGE_<wbr>TASK_<wbr>DATE</h3>
					<div class="tsd-signature tsd-kind-icon">CHANGE_<wbr>TASK_<wbr>DATE<span class="tsd-signature-symbol">:</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:27</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="create_task" class="tsd-anchor"></a>
					<h3>CREATE_<wbr>TASK</h3>
					<div class="tsd-signature tsd-kind-icon">CREATE_<wbr>TASK<span class="tsd-signature-symbol">:</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:17</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="edit_task" class="tsd-anchor"></a>
					<h3>EDIT_<wbr>TASK</h3>
					<div class="tsd-signature tsd-kind-icon">EDIT_<wbr>TASK<span class="tsd-signature-symbol">:</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:20</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="finish_task" class="tsd-anchor"></a>
					<h3>FINISH_<wbr>TASK</h3>
					<div class="tsd-signature tsd-kind-icon">FINISH_<wbr>TASK<span class="tsd-signature-symbol">:</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:18</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="restart_process" class="tsd-anchor"></a>
					<h3>RESTART_<wbr>PROCESS</h3>
					<div class="tsd-signature tsd-kind-icon">RESTART_<wbr>PROCESS<span class="tsd-signature-symbol">:</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:28</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="rollback_state" class="tsd-anchor"></a>
					<h3>ROLLBACK_<wbr>STATE</h3>
					<div class="tsd-signature tsd-kind-icon">ROLLBACK_<wbr>STATE<span class="tsd-signature-symbol">:</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:24</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="rollback_task" class="tsd-anchor"></a>
					<h3>ROLLBACK_<wbr>TASK</h3>
					<div class="tsd-signature tsd-kind-icon">ROLLBACK_<wbr>TASK<span class="tsd-signature-symbol">:</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:23</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="start_process" class="tsd-anchor"></a>
					<h3>START_<wbr>PROCESS</h3>
					<div class="tsd-signature tsd-kind-icon">START_<wbr>PROCESS<span class="tsd-signature-symbol">:</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:15</li>
						</ul>
					</aside>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-enum">
						<a href="operationtype.html" class="tsd-kind-icon">Operation<wbr>Type</a>
						<ul>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="operationtype.html#add_remark" class="tsd-kind-icon">ADD_<wbr>REMARK</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="operationtype.html#cancel_task" class="tsd-kind-icon">CANCEL_<wbr>TASK</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="operationtype.html#change_handler" class="tsd-kind-icon">CHANGE_<wbr>HANDLER</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="operationtype.html#change_manager" class="tsd-kind-icon">CHANGE_<wbr>MANAGER</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="operationtype.html#change_state" class="tsd-kind-icon">CHANGE_<wbr>STATE</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="operationtype.html#change_task" class="tsd-kind-icon">CHANGE_<wbr>TASK</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="operationtype.html#change_task_date" class="tsd-kind-icon">CHANGE_<wbr>TASK_<wbr>DATE</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="operationtype.html#create_task" class="tsd-kind-icon">CREATE_<wbr>TASK</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="operationtype.html#edit_task" class="tsd-kind-icon">EDIT_<wbr>TASK</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="operationtype.html#finish_task" class="tsd-kind-icon">FINISH_<wbr>TASK</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="operationtype.html#restart_process" class="tsd-kind-icon">RESTART_<wbr>PROCESS</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="operationtype.html#rollback_state" class="tsd-kind-icon">ROLLBACK_<wbr>STATE</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="operationtype.html#rollback_task" class="tsd-kind-icon">ROLLBACK_<wbr>TASK</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="operationtype.html#start_process" class="tsd-kind-icon">START_<wbr>PROCESS</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>