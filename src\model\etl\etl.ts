import { sseWorker } from "../../core/tools/sse"
import type * as dto from "../../def/index"

import ETLAPI from "./api"

export class ETL {
    private api: ETLAPI
    constructor(id: dto.ETLTypes.id) {
        this.api = new ETLAPI(id)
    }

    public query() {
        return this.api.query()
    }
    public getData(params: unknown) {
        return this.api.getData(params)
    }
    public createTask(params: unknown, taskDate: string) {
        return this.api.createTask(taskDate, params)
    }

    public execute(params: unknown, onProgress: (msg: string[]) => void) {
        const url = this.api.createExecuteURL(params)
        let serverMessages: string[] = []
        return sseWorker<{ data: string }, string>(url, {
            onOpen: () => {
                serverMessages = []
                onProgress(serverMessages)
            },
            onError: (done) => {
                serverMessages.push("执行完成。")
                onProgress(serverMessages)
                done("执行完成。")
            },
            onMsg: (e) => {
                serverMessages.push(String(e.data))
                onProgress(serverMessages)
            },
        })
    }
}
