import endsWith from "lodash/endsWith"

const loadedPaths = new Set<string>()

export function loadScript(url: string, load?: () => void) {
    if (loadedPaths.has(url)) {
        return load && load()
    }

    const head = document.getElementsByTagName("head")[0]
    if (endsWith(url, "css")) {
        const style = document.createElement("link")
        style.href = url
        style.rel = "stylesheet"
        if (load) {
            style.onload = load
        }
        loadedPaths.add(url)
        return head.appendChild(style)
    }

    const script = document.createElement("script")
    script.type = "text/javascript"
    script.src = url
    if (load) {
        script.onload = load
    }
    head.appendChild(script)
    loadedPaths.add(url)
}
