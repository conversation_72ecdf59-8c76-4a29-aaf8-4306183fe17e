export class AnonymousApi {
    public isAnonymous: boolean
    public anonymous() {
        this.isAnonymous = true
        return this
    }
    public unAnonymous() {
        this.isAnonymous = false
        return this
    }

    public get urlPrefix() {
        return this.isAnonymous ? "public" : "general"
    }
}
export class AnonymousModel {
    protected api?: AnonymousApi
    public isAnonymous: boolean
    public anonymous() {
        this.api.anonymous()
        return this
    }
    public unAnonymous() {
        this.api.unAnonymous()
        return this
    }
}
