import jwt_decode from "jwt-decode"

type jwtJson = {
    exp: number
    sub: string
    user_id: number
}

export function parseToken(jwt: string) {
    return jwt_decode<jwtJson>(jwt)
}

/**
 * 获取JWT令牌的过期时间戳（以毫秒为单位）。
 * 
 * @param jwt - 要解析的JWT字符串。
 * @returns 返回过期时间戳，单位为毫秒。
 */
export function getTokenExp(jwt: string) {
    const d = parseToken(jwt)
    return d.exp * 1e3
}

export function isTokenValid(jwt: string) {
    const t = getTokenExp(jwt)
    return t > new Date().valueOf()
}
