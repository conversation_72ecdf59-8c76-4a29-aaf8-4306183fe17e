import { Anonymous<PERSON><PERSON> } from "../../core/anonymous-api"
import axios from "../../core/axios/index"
import type * as dto from "../../def/index"
export default class TagManagerApi extends AnonymousApi {
    constructor(private model_name: string) {
        super()
    }

    public getTagGroups() {
        return axios.get<dto.TagManagerTypes.TagGroup[]>(
            `${this.urlPrefix}/model/${this.model_name}/tag/groups`
        )
    }

    public query(keyValue: string) {
        return axios.get<dto.TagManagerTypes.queryApiResult>(
            `general/model/${this.model_name}/key/${keyValue}/tag/info`
        )
    }
}
