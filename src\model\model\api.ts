import axios from "../../core/axios/index"
import type { QueryObject } from "../../core/object2query"
import { serialize } from "../../core/object2query"
import { encodeParams4Parameters } from "../../core/tools/form"
import type * as dto from "../../def/index"

export class ModelApi {
    constructor(private model_name: string) {}

    public async toolbarEnhancerValid<T>(behavior: string, jsonStr: string) {
        const param = {
            behavior: behavior,
            jsonStr: jsonStr,
        }
        return axios.post<T>(
            `general/model/${this.model_name}/toolbar/enhancer/valid`,
            param
        )
    }

    public mappingFetch(params: dto.ModelTypes.mappingFetchAPIParams) {
        const extraUrl = params.nodeValue
            ? `/${encodeURIComponent(params.nodeValue)}`
            : ""
        const mappingName = params.actionName
            ? `${params.actionName}/${params.mappingName}`
            : params.mappingName
        return axios.post(
            `general/model/${this.model_name}/mapping2/${mappingName}/fetch${extraUrl}`,
            {
                form_params: params.form_params,
                selected_list: params.selected_list,
            }
        )
    }

    public jointSearch(
        joint_name: string,
        params: dto.ModelTypes.searchJointApiParams
    ) {
        return axios.post<dto.ModelTypes.searchJointApiResult>(
            `general/model/${this.model_name}/search2/${joint_name}/fetch`,
            params
        )
    }

    public intentSearch(
        intentName: string,
        params: { actionId: string; form_params: unknown }
    ) {
        return axios.post<dto.RowIntent>(
            `general/model/${this.model_name}/intentSearch2/${intentName}/fetch`,
            params
        )
    }

    public groupSearch(params: dto.ModelTypes.groupSearchApiParams) {
        return axios.post<dto.ModelTypes.groupSearchApiResult>(
            `general/model/${this.model_name}/group`,
            params
        )
    }

    public getForwardURL(forwards: string, params?: unknown) {
        return axios.post<dto.ModelTypes.getForwardURLApiResult>(
            `general/model/${this.model_name}/request/${forwards[0]}/`,
            params
        )
    }

    public getModelRequest<T>(apiName: string, query: QueryObject | string) {
        const queryString = typeof query === "string" ? query : serialize(query)
        return axios.get<T>(
            `general/model/${this.model_name}/request/${apiName}/?${queryString}`
        )
    }

    public postModelRequest<T>(apiName: string, params: FormData) {
        return axios.post<T>(
            `general/model/${this.model_name}/request/${apiName}/`,
            params
        )
    }

    public getTimeByIdentifier(identifier: string) {
        return axios.get<string>(
            `general/model/${this.model_name}/evaluateOptionalValue/${identifier}`
        )
    }

    public getRemarkList(params: {
        associateId: string
        pageIndex: number
        pageSize: number
    }) {
        return axios.get<{
            rows: Array<unknown>
            total: number
            pageIndex: number
            pageSize: number
        }>(
            `general/model/${
                this.model_name
            }/remark/list?${encodeParams4Parameters(params)}`
        )
    }

    public createChat(params: { detailId: number; autoJoin?: boolean }) {
        return axios.post<{ id: string }>(
            `/general/model/${this.model_name}/${params.detailId}/createChat/${
                params.autoJoin ? "1" : "0"
            }`
        )
    }

    public getHistoryVersion(objectId: string | number, field: string) {
        return axios.get<dto.HistoryVersion[]>(
            `general/model/${
                this.model_name
            }/key/${objectId}/history_version/${encodeURIComponent(field)}`
        )
    }

    public superCascaderFetch(
        params: dto.ModelTypes.SuperCascaderFetchApiParams
    ) {
        const extraUrl = params.parent
            ? `/${encodeURIComponent(params.parent)}`
            : ""
        return axios.post<dto.ModelTypes.SuperCascaderFetchApiResult>(
            `general/model/${this.model_name}/supercascader2/${params.superCascaderName}/fetch${extraUrl}`,
            {
                actionId: params.actionId,
                form_params: params.form_params,
                selected_list: params.selected_list,
                prefilters: params.prefilters,
            }
        )
    }

    public superCascaderSearch(
        params: dto.ModelTypes.SuperCascaderSearchApiParams
    ) {
        return axios.post<dto.ModelTypes.SuperCascaderSearchApiResult>(
            `general/model/${this.model_name}/supercascader2/${params.superCascaderName}/search`,
            {
                actionId: params.actionId,
                form_params: params.form_params,
                selected_list: params.selected_list,
                prefilters: params.prefilters,
                keyword: params.keyword,
                limitBegin: params.limitBegin,
            }
        )
    }

    public getSection(sectionName: string) {
        return axios.post<dto.DetailTypes.section>(
            `general/model/${this.model_name}/section/${sectionName}/meta`
        )
    }
}
