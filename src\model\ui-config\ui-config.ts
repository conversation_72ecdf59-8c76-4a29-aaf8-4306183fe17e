import type * as dto from "../../def/index"

import UIConfigApi from "./api"
export class UIConfig {
    private api: UIConfigApi
    public datamodel?: unknown
    constructor(datamodelName: string) {
        this.api = new UIConfigApi(datamodelName)
    }

    public async query() {
        const data = await this.api.query()
        this.datamodel = JSON.parse(data.text.join("\n"))
        return data
    }

    public save(datamodel: unknown) {
        return this.api.save(datamodel)
    }
    public requestMappingValues(
        enumRequestString: dto.UIConfigTypes.SchemaNode["enumRequest"]
    ) {
        return this.api.requestMappingValues(enumRequestString)
    }
}
