import { defaultAxiosBuilder } from "../../core/axios/default"
import axios from "../../core/axios/index"
import { global } from "../../core/global"
import { isTokenValid } from "../../core/token-checker"
import type * as dto from "../../def/index"

export function getLogList() {
    return axios.get<string[]>(`general/log/list`)
}

export async function login(params: {
    username: string
    password: string
    rootEntrance: string
    seed: number
    codeToVerify: string
}) {
    return await axios.post<dto.Index.loginRequestResult>(
        `login`,
        require("qs").stringify(params)
    )
}

export function logout() {
    return axios.post<void>(`logout`)
}

export function logout2() {
    return axios.get<void>(`/system/user/logout2`)
}

export function createVerifyCodeImageUrl(seed: number) {
    return `${global.baseUrl}general/imageToVerify/${seed}`
}

export function refreshToken() {
    return axios.post<dto.Index.refreshTokenRequestResult>(
        `general/user/token/refresh`
    )
}

/**
 * 将个人token换取为带上组织应用信息的token
 */
export function changeTokenWithXid(xid: string | number, force?: boolean) {
    const cache = global.getXidToken(xid + "")
    if (cache && cache.token && !force) {
        if (isTokenValid(cache.token)) {
            return Promise.resolve(cache.token)
        }
    }
    return defaultAxiosBuilder
        .instance()
        .get<{ data: dto.Index.refreshTokenRequestResult }>(
            `general/project/uniplat_base/service/system.auth/apply_token?xid=${xid}`
        )
        .then((r) => {
            if (r.data && r.data.data) {
                const t = r.data.data.jwt
                global.setXidToken(xid + "", t)
                return t
            }
            return ""
        })
}

export function getUserInfo() {
    return axios.get<dto.Index.getUserInfo>(`general/entrances/userinfo`)
}

export function getUserInfoByJwt(jwt: string) {
    return axios.get<dto.Index.getUserInfo>(
        `general/entrances/userinfo?jwt=${jwt}`
    )
}

export const configurationApi = {
    /**
     * @deprecated(请使用getInitialApplicationData) 获取uniplat登录前的一些信息
     */
    getInitialData: () =>
        axios.get<dto.Index.InitialData>("general/entrances/title"),

    getInitialApplicationData: () =>
        axios.get<dto.Index.ApplicationConfig>("general/application/title"),

    /**
     * @deprecated(请使用getAppConfig)
     */
    getInitConfig: (entrance: string) =>
        axios.get<dto.Index.InitConfig>(
            `general/entrances/${encodeURIComponent(entrance)}/config`
        ),

    getAppConfig: (application: string, entrance: string) =>
        axios.get<dto.Index.InitConfig>(
            `general/application/${encodeURIComponent(
                application
            )}/${encodeURIComponent(entrance)}/config`
        ),

    getRouters: (entrance: string) =>
        axios.get<dto.Index.Entrance>(
            `general/entrances/${encodeURIComponent(entrance)}/meta`
        ),

    getAppRouters: (application: string, entrance: string) =>
        axios.get<dto.Index.Entrance>(
            `general/application/${encodeURIComponent(
                application
            )}/${encodeURIComponent(entrance)}/meta`
        ),

    getApplicationOrg: () =>
        axios.get<{ id: number; name: string; application: string }[]>(
            `general/application/org/list`
        ),
    changeTokenWithXid: (xid: string, force?: boolean) =>
        changeTokenWithXid(xid, force),
}
