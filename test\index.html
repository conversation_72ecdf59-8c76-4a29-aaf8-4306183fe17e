<html>
    <body></body>

    <script src="../release/uniplst-sdk-mjs.0.1.2.js"></script>
    <!-- <script src="../release/mjs.min.js"></script> -->

    <script>
        const uniplat = window.uniplatsdk

        // 密钥混淆，请到 http://uniplat3-vue3-web.jinsehuaqin.com/release/ 中进行

        // 创建sdk实例
        const sdk = uniplat.create()

        // 注入密钥，不注入的话，如果Response是加密内容，则sdk处理不了密文
        uniplat.crypto.set(这里填入混淆后的密钥)
        // 此行表示对请求入参进行加密，可不配置，按需求开启
        uniplat.crypto.enablePayloadEncode()

        // 注入sdk配置信息。不管是匿名还是非匿名访问，这一步都是必须的。
        sdk.connect({
            // 必须，配置api接口地址，可以是绝对地址，可以是相对地址
            baseUrl: "http://jzpes.test-api.qqxb.jinsehuaqin.com:8800/",
            /**
             * [可选]可配置请求超时时间，默认 60s
             */
            axiosTimeout: 60 * 1000,
        })

        const token = ""
        // 如果请求需要授权，需要先注入token
        token &&
            sdk.loginByToken({
                token,
                // 可选。是否为超管
                isSuperUser: false,
                // 可选。用户名
                username: "",
            })

        // 注入场景值，部分接口条件下需要这些信息
        sdk.global.rootEntrance = "荆州市智慧就业服务工作台"
        sdk.setInitData({
            scenes: [{ key: "1", name: "ManagerOrgScene" }],
        })

        // 全局错误拦截（可选配置）
        sdk.events.addUniversalErrorResponseCallback(
            r => {
                // r 是一个AxiosResponse
                // {
                //     config: {
                //         url: string 请求的api地址
                //     },
                //     data: {
                //         rescode: number
                //         msg: string
                //     }
                // }
                if(r.data.rescode === 401){
                    // 登出处理
                }
            }
        )

        const rowPredict = {
            type: "label",
            order_no: "",
            read_time: "",
            title: "",
            second_title: "",
            url_path: "",
            shelves_status: "label",
        }
        // list 调用
        sdk.model("g_policy_advice")
            .list2("manage_for_anony" /*可选listname*/)
            .anonymous() // 如果是匿名接口，需要使用此行。非匿名接口则不需要，但是需要提前注入token
            .query({ item_size: 10, pageIndex: 1 })
            .then((r) => {
                const d = r.pageData
                // 总数
                const total = d.record_count
                const rows = uniplat.helper.buildRows(d.rows, rowPredict)
                console.log(rows)
            })

        // list 调用，添加过滤器(推荐使用)
        sdk.model("g_policy_advice")
            .list2("manage_for_anony" /*可选listname*/)
            .anonymous() // 如果是匿名接口，需要使用此行。非匿名接口则不需要，但是需要提前注入token
            .search({
                item_size: 10,
                pageIndex: 1,
                filters: [{ property: "name1", value: "value1" }], // 可选
                prefilters: [{ property: "name1", value: "value1" }], // 可选
            })
            .then((r) => {
                const d = r.pageData
                // 总数
                const total = d.recordCount
                const rows = uniplat.helper.buildRows(d.rows, rowPredict)
                console.log(rows)
            })

        // list 调用，添加过滤器（请优先使用上面的search方法）
        sdk.model("g_policy_advice")
            .list2("manage_for_anony" /*可选listname*/)
            .anonymous() // 如果是匿名接口，需要使用此行。非匿名接口则不需要，但是需要提前注入token
            .clearPrefilter() // 如果会多次调用，且prefilter value可能会变化，可以先清空掉，再传入新的
            .addPrefilter({
                // 添加 prefilter，多个条件整合成一个对象传入。
                query1: 1,
                query2: 2,
            })
            .clearFilter() // filter 添加和 prefilter 类似，不过addFilter时数据结构略有不同
            .addFilter({ property: "name1", value: "value1" })
            .query({ item_size: 10, pageIndex: 1 })
            .then((r) => {
                const d = r.pageData
                // 总数
                const total = d.recordCount
                const rows = uniplat.helper.buildRows(d.rows, rowPredict)
                console.log(rows)
            })

        // detail 调用
        sdk.model("g_policy_advice")
            // key值现在支持传入密文，密文来自于行数据上的 _accesskey属性
            .detail2(56)
            .anonymous() // 如果是匿名接口，需要使用此行。非匿名接口则不需要，但是需要提前注入token
            .query()
            .then((r) => {
                // 部分信息在行数据上的，可以获取到行数据对象
                const row = uniplat.helper.buildRows(r.row, rowPredict)
                // 其他信息可能在 r.meta.header 或 r.meta.sections 或 r.meta.pages 中的，按需继续寻找

                // 使用此方法可以进行数据结构简化
                const info = uniplat.builder.detailBuilder.build(r)
                // info 中包含 headers（一些summary信息，数据结构为 {label:string; value: string}[]）
                //             sections, 数据结构为 { items: { label: string; value: string}[] }[], 拆分为多个items的集合是因为有时候需要区分卡片以免信息全部堆积在一起
                //                       如果要以一整个卡片显示，则需要调用者手动合并所有items为一个数组
                //             pages, 一般情况下用不到，这里面会附属一些二级列表信息，用此信息可以读取此detail关联的一些列表行数据
                //             keyValue，此detail的键值
                //             row，此detail对应的一些额外信息，大部分情况也用不到
            })

        // 提交表单
        sdk.model("g_policy_advice")
            // 这里是表单名称
            .action("insert")
            // 这里提交的是表单内容
            .addInputs_parameter({
                // 产品名称(必填项)
                name: "直播带岗运营服务",
                // 产品描述
                description:
                    "包含第三方平台，如快手抖音小程序账号注册及建设，以及指导、配合局方人员做直播内容规划、直播人员招募培训、试播等服务",
                // 首次上线时间
                launch_time: "",
                // 重大版本记录
                major_version_record: "",
            })
            // 如果提交的表单是编辑或更新操作，这里需要提供要编辑的数据的id和version信息。没有是新增操作，这里面则不需要调用 updateInitialParams
            .updateInitialParams({
                // id 支持密文，来自于行数据上的_accesskey属性
                selected_list: [{ v, id }],
            })
            .anonymous() // 如果是匿名接口，需要使用此行。非匿名接口则不需要，但是需要提前注入token
            .execute()
            .then((r) => {
                message.success("成功")
            })
            .catch(onError)
            .finally(() => (loading = false))
    </script>
</html>
