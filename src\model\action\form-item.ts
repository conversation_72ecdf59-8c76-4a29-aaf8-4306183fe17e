import type { ActionTypes } from "../../def/index"
import { Tree } from "../tree/tree"
enum InputTypes {
    tip = "tip",
    label = "label",
    text = "text",
    color = "color",
    mapping = "mapping",
    multi_mapping = "multi_mapping",
    checkbox = "checkbox-group",
    boolean = "boolean",
    json = "json",
    date = "date",
    datetime = "datetime",
    month = "month",
    number = "number",
    money = "money",
    search = "search",
    multi_search = "multi_search",
    file = "file",
    multi_file = "multi_file",
    textarea = "textarea",
    grid = "grid",
    cascader = "cascader",
    qart = "qart",
    rich_text = "rich_text",
    tree = "tree",
    hidden = "hidden",
    enum = "enum",
}

type MorePreciseInput = ActionTypes.inputsParameter & {
    type: InputTypes
}
export function getFormItem(data: MorePreciseInput | ActionTypes.inputsParameter) {
    const ConstructorMap = {
        mapping: MappingItem,
        tree: TreeItem,
    }
    const constructor = ConstructorMap[data.type]
    if (constructor == null) {
        return new FormItem(data)
    }
    return new constructor(data)
}

export class ActionForm {
    constructor(private formItems: FormItem[]) {}
    public getObject() {
        const obj = {}
        this.formItems.forEach((item) => {
            const [key, value] = item.getKeyValue()
            obj[key] = value
        })
        return obj
    }
}
export class FormItem {
    protected data: MorePreciseInput
    constructor(data: ActionTypes.inputsParameter) {
        this.data = data as MorePreciseInput
    }

    public getKeyValue(): [string, unknown] {
        return [this.data.property, ""]
    }
}

class MappingItem extends FormItem {
    public get elSelectOptions() {
        return this.data.ext_properties.mapping.mapping_values
    }
}

class TreeItem extends FormItem {
    private tree?: Tree
    constructor(data: ActionTypes.inputsParameter) {
        super(data)
        this.tree = new Tree(this.data.treeModelName)
    }
    public load(nodeId: number) {
        return this.tree.queryTreeLazy(nodeId)
    }
    public getKeyValue(): [string, number] {
        return [this.data.property, 0]
    }
}
