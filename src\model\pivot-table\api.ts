import axios from "../../core/axios/index"
import { encodeParams4Parameters } from "../../core/tools/form"
import type * as dto from "../../def/index"
export class PivotTableApi {
    constructor(private model_name: string, private pivottableName: string) {}

    public query(params: dto.PivotTableTypes.queryParams) {
        return axios.get<dto.PivotTableTypes.queryResult>(
            `general/pivottable/${this.model_name}/${
                this.pivottableName
            }/meta/?${encodeParams4Parameters(params)}`
        )
    }

    public refresh(params: dto.PivotTableTypes.refreshAPIParams) {
        return axios.get<dto.PivotTableTypes.refreshResult>(
            `general/pivottable/${this.model_name}/${
                this.pivottableName
            }/query/?${encodeParams4Parameters(params)}`
        )
    }

    public createExportUrl(params: dto.PivotTableTypes.exportAPIExcelParams) {
        return axios.get<string>(
            `general/pivottable/${this.model_name}/${
                this.pivottableName
            }/export?${encodeParams4Parameters(params, true)}`
        )
    }

    public getDetail(params: dto.PivotTableTypes.getDetailAPIParams) {
        return axios.get<dto.PivotTableTypes.getDetailRequestResult>(
            `general/pivottable/${this.model_name}/${
                this.pivottableName
            }/detail/?${encodeParams4Parameters(params)}`
        )
    }
    public createExportDetailUrl(
        params: dto.PivotTableTypes.getDetailAPIParams
    ) {
        return axios.get<string>(
            `general/pivottable/${this.model_name}/${
                this.pivottableName
            }/detail/export?${encodeParams4Parameters(params, true)}`
        )
    }
}
