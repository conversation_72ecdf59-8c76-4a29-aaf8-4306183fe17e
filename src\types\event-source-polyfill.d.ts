/**
 * If you import a dependency which does not include its own type definitions,
 * TypeScript will try to find a definition for it by following the `typeRoots`
 * compiler option in tsconfig.json. For this project, we've configured it to
 * fall back to this folder if nothing is found in node_modules/@types.
 *
 * Often, you can install the DefinitelyTyped
 * (https://github.com/DefinitelyTyped/DefinitelyTyped) type definition for the
 * dependency in question. However, if no one has yet contributed definitions
 * for the package, you may want to declare your own. (If you're using the
 * `noImplicitAny` compiler options, you'll be required to declare it.)
 *
 * This is an example type definition which allows import from `module-name`,
 * e.g.:
 * ```ts
 * import something from 'module-name';
 * something();
 * ```
 */
// declare module 'module-name' {
//   // eslint-disable-next-line @typescript-eslint/no-explicit-any
//   const whatever: any;
//   export = whatever;
// }

declare module "event-source-polyfill" {
    export class EventSourcePolyfill {
        constructor(url: string, option?: unknown)
        addEventListener(
            eventName: string,
            callback: (...p: readonly unknown[]) => void
        ): void
        removeEventListener(
            eventName: string,
            callback: (...p: readonly unknown[]) => void
        ): void
        readonly dispatchEvent: unknown
        close(): void
        onopen<T>(msg: T): void
        onerror<T>(msg: T): void
        onmessage<T>(msg: T): void
        readonly readyState: number
        readonly url: string
        readonly CLOSED: number
        readonly OPEN: number
        readonly withCredentials: boolean
        readonly CONNECTING: number
    }
    export class NativeEventSource {
        constructor(url: string, option?: unknown)
        addEventListener(
            eventName: string,
            callback: (...p: readonly unknown[]) => void
        ): void
        removeEventListener(
            eventName: string,
            callback: (...p: readonly unknown[]) => void
        ): void
        readonly dispatchEvent: unknown
        close(): void
        onopen<T>(msg: T): void
        onerror<T>(msg: T): void
        onmessage<T>(msg: T): void
        readonly readyState: number
        readonly url: string
        readonly CLOSED: number
        readonly OPEN: number
        readonly withCredentials: boolean
        readonly CONNECTING: number
    }
}
