<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Events | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="events.html">Events</a>
				</li>
			</ul>
			<h1>Class Events</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">Events</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section tsd-is-private tsd-is-private-protected">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="events.html#logedin" class="tsd-kind-icon">logedin</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="events.html#tokenchanged" class="tsd-kind-icon">token<wbr>Changed</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#addbigactionmessagelistener" class="tsd-kind-icon">add<wbr>Big<wbr>Action<wbr>Message<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#addconnectivityobserver" class="tsd-kind-icon">add<wbr>Connectivity<wbr>Observer</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#addlogedincallback" class="tsd-kind-icon">add<wbr>Logedin<wbr>Callback</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#addresponsetoolargeorslowcallback" class="tsd-kind-icon">add<wbr>Response<wbr>Too<wbr>Large<wbr>OrSlow<wbr>Callback</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#addssenotifymessagelistener" class="tsd-kind-icon">add<wbr>Sse<wbr>Notify<wbr>Message<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#addtokenchanged" class="tsd-kind-icon">add<wbr>Token<wbr>Changed</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#addtokenexpiring" class="tsd-kind-icon">add<wbr>Token<wbr>Expiring</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#addtransportmessagelistener" class="tsd-kind-icon">add<wbr>Transport<wbr>Message<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#adduniversalerrorcallback" class="tsd-kind-icon">add<wbr>Universal<wbr>Error<wbr>Callback</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#adduniversalerrorresponsecallback" class="tsd-kind-icon">add<wbr>Universal<wbr>Error<wbr>Response<wbr>Callback</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#adduserinfochangelistener" class="tsd-kind-icon">add<wbr>User<wbr>Info<wbr>Change<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#calllogedin" class="tsd-kind-icon">call<wbr>Logedin</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#callresponsetoolargeorslowcallback" class="tsd-kind-icon">call<wbr>Response<wbr>Too<wbr>Large<wbr>OrSlow<wbr>Callback</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#calltokenchanged" class="tsd-kind-icon">call<wbr>Token<wbr>Changed</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#calltokenexpiring" class="tsd-kind-icon">call<wbr>Token<wbr>Expiring</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#calluniversalerrorcallback" class="tsd-kind-icon">call<wbr>Universal<wbr>Error<wbr>Callback</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#calluniversalerrorresponsecallback" class="tsd-kind-icon">call<wbr>Universal<wbr>Error<wbr>Response<wbr>Callback</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="events.html#calluserinfochangelistener" class="tsd-kind-icon">call<wbr>User<wbr>Info<wbr>Change<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="events.html#getsse" class="tsd-kind-icon">get<wbr>Sse</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="events.html#responsetoolargeorslowcallback" class="tsd-kind-icon">response<wbr>Too<wbr>Large<wbr>OrSlow<wbr>Callback</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="events.html#tokenexpiring" class="tsd-kind-icon">token<wbr>Expiring</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="events.html#universalerrorcallback" class="tsd-kind-icon">universal<wbr>Error<wbr>Callback</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="events.html#universalerrorresponsecallback" class="tsd-kind-icon">universal<wbr>Error<wbr>Response<wbr>Callback</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="events.html#userinfochange" class="tsd-kind-icon">user<wbr>Info<wbr>Change</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="logedin" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> logedin</h3>
					<div class="tsd-signature tsd-kind-icon">logedin<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/events.ts:15</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-property">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="tokenchanged" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> token<wbr>Changed</h3>
					<div class="tsd-signature tsd-kind-icon">token<wbr>Changed<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>token<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/events.ts:16</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-property">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>token<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>token: <span class="tsd-signature-type">string</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addbigactionmessagelistener" class="tsd-anchor"></a>
					<h3>add<wbr>Big<wbr>Action<wbr>Message<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Big<wbr>Action<wbr>Message<wbr>Listener<span class="tsd-signature-symbol">(</span>callback<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.bigActionUpdateListener</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:155</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加SSE的bigAction进度更新的监听函数</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>callback: <span class="tsd-signature-type">dto.SSE.bigActionUpdateListener</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>返回移除监听的方法</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addconnectivityobserver" class="tsd-anchor"></a>
					<h3>add<wbr>Connectivity<wbr>Observer</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Connectivity<wbr>Observer<span class="tsd-signature-symbol">(</span>callback<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.ConnectivityObserver</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:145</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加监听SSE 链接状态状态的回掉函数</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>callback: <span class="tsd-signature-type">dto.SSE.ConnectivityObserver</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>返回移除监听的方法</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addlogedincallback" class="tsd-anchor"></a>
					<h3>add<wbr>Logedin<wbr>Callback</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Logedin<wbr>Callback<span class="tsd-signature-symbol">(</span>cb<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:58</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加回掉函数
									在登陆成功后被调用</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>cb: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addresponsetoolargeorslowcallback" class="tsd-anchor"></a>
					<h3>add<wbr>Response<wbr>Too<wbr>Large<wbr>OrSlow<wbr>Callback</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Response<wbr>Too<wbr>Large<wbr>OrSlow<wbr>Callback<span class="tsd-signature-symbol">(</span>cb<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.UniversalErrorResponseCallback</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:94</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>sdk向外抛出的错误响应</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>cb: <span class="tsd-signature-type">dto.UniversalErrorResponseCallback</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addssenotifymessagelistener" class="tsd-anchor"></a>
					<h3>add<wbr>Sse<wbr>Notify<wbr>Message<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Sse<wbr>Notify<wbr>Message<wbr>Listener<span class="tsd-signature-symbol">(</span>callback<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.notifyListener</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:135</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加SSE的消息通知的监听函数</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>callback: <span class="tsd-signature-type">dto.SSE.notifyListener</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>返回移除监听的方法</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addtokenchanged" class="tsd-anchor"></a>
					<h3>add<wbr>Token<wbr>Changed</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Token<wbr>Changed<span class="tsd-signature-symbol">(</span>cb<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span>token<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:45</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加Token改变时的回掉</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>cb: <span class="tsd-signature-symbol">(</span>token<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>token<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>token: <span class="tsd-signature-type">string</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addtokenexpiring" class="tsd-anchor"></a>
					<h3>add<wbr>Token<wbr>Expiring</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Token<wbr>Expiring<span class="tsd-signature-symbol">(</span>cb<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.callBackFromOutside</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:32</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加Token过期时的回掉</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>cb: <span class="tsd-signature-type">dto.SSE.callBackFromOutside</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addtransportmessagelistener" class="tsd-anchor"></a>
					<h3>add<wbr>Transport<wbr>Message<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Transport<wbr>Message<wbr>Listener<span class="tsd-signature-symbol">(</span>callback<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.transportMessageListener</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:123</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加SSE的模型更新时的监听函数</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>callback: <span class="tsd-signature-type">dto.SSE.transportMessageListener</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>返回移除监听的方法</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="adduniversalerrorcallback" class="tsd-anchor"></a>
					<h3>add<wbr>Universal<wbr>Error<wbr>Callback</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Universal<wbr>Error<wbr>Callback<span class="tsd-signature-symbol">(</span>cb<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.UniversalErrorCallback</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:83</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>sdk向外抛出的错误</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>cb: <span class="tsd-signature-type">dto.UniversalErrorCallback</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="adduniversalerrorresponsecallback" class="tsd-anchor"></a>
					<h3>add<wbr>Universal<wbr>Error<wbr>Response<wbr>Callback</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Universal<wbr>Error<wbr>Response<wbr>Callback<span class="tsd-signature-symbol">(</span>cb<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.UniversalErrorResponseCallback</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:108</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>sdk向外抛出的错误响应</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>cb: <span class="tsd-signature-type">dto.UniversalErrorResponseCallback</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="adduserinfochangelistener" class="tsd-anchor"></a>
					<h3>add<wbr>User<wbr>Info<wbr>Change<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>User<wbr>Info<wbr>Change<wbr>Listener<span class="tsd-signature-symbol">(</span>cb<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.UserInfoChangedEventListener</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:70</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加用户信息变化时的回掉</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>cb: <span class="tsd-signature-type">dto.UserInfoChangedEventListener</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="calllogedin" class="tsd-anchor"></a>
					<h3>call<wbr>Logedin</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">call<wbr>Logedin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:62</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="callresponsetoolargeorslowcallback" class="tsd-anchor"></a>
					<h3>call<wbr>Response<wbr>Too<wbr>Large<wbr>OrSlow<wbr>Callback</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">call<wbr>Response<wbr>Too<wbr>Large<wbr>OrSlow<wbr>Callback<span class="tsd-signature-symbol">(</span>r<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:100</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>r: <span class="tsd-signature-type">unknown</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="calltokenchanged" class="tsd-anchor"></a>
					<h3>call<wbr>Token<wbr>Changed</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">call<wbr>Token<wbr>Changed<span class="tsd-signature-symbol">(</span>token<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:49</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>token: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="calltokenexpiring" class="tsd-anchor"></a>
					<h3>call<wbr>Token<wbr>Expiring</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">call<wbr>Token<wbr>Expiring<span class="tsd-signature-symbol">(</span>msg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:36</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> msg: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="calluniversalerrorcallback" class="tsd-anchor"></a>
					<h3>call<wbr>Universal<wbr>Error<wbr>Callback</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">call<wbr>Universal<wbr>Error<wbr>Callback<span class="tsd-signature-symbol">(</span>msg<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, res<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">AxiosResponse</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:87</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>msg: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>res: <span class="tsd-signature-type">AxiosResponse</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="calluniversalerrorresponsecallback" class="tsd-anchor"></a>
					<h3>call<wbr>Universal<wbr>Error<wbr>Response<wbr>Callback</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">call<wbr>Universal<wbr>Error<wbr>Response<wbr>Callback<span class="tsd-signature-symbol">(</span>r<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:114</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>r: <span class="tsd-signature-type">unknown</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="calluserinfochangelistener" class="tsd-anchor"></a>
					<h3>call<wbr>User<wbr>Info<wbr>Change<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">call<wbr>User<wbr>Info<wbr>Change<wbr>Listener<span class="tsd-signature-symbol">(</span>userInfo<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Parameters</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.UserInfoChangedEventListener</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">0</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:74</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>userInfo: <span class="tsd-signature-type">Parameters</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.UserInfoChangedEventListener</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">0</span><span class="tsd-signature-symbol">]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="getsse" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> get<wbr>Sse</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Sse<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="sse.html" class="tsd-signature-type">Sse</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:25</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="sse.html" class="tsd-signature-type">Sse</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="responsetoolargeorslowcallback" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> response<wbr>Too<wbr>Large<wbr>OrSlow<wbr>Callback</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">response<wbr>Too<wbr>Large<wbr>OrSlow<wbr>Callback<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:22</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="tokenexpiring" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> token<wbr>Expiring</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">token<wbr>Expiring<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:12</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="universalerrorcallback" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> universal<wbr>Error<wbr>Callback</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">universal<wbr>Error<wbr>Callback<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:18</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="universalerrorresponsecallback" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> universal<wbr>Error<wbr>Response<wbr>Callback</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">universal<wbr>Error<wbr>Response<wbr>Callback<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:19</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="userinfochange" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> user<wbr>Info<wbr>Change</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">user<wbr>Info<wbr>Change<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/events.ts:14</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="events.html" class="tsd-kind-icon">Events</a>
						<ul>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="events.html#logedin" class="tsd-kind-icon">logedin</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="events.html#tokenchanged" class="tsd-kind-icon">token<wbr>Changed</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#addbigactionmessagelistener" class="tsd-kind-icon">add<wbr>Big<wbr>Action<wbr>Message<wbr>Listener</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#addconnectivityobserver" class="tsd-kind-icon">add<wbr>Connectivity<wbr>Observer</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#addlogedincallback" class="tsd-kind-icon">add<wbr>Logedin<wbr>Callback</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#addresponsetoolargeorslowcallback" class="tsd-kind-icon">add<wbr>Response<wbr>Too<wbr>Large<wbr>OrSlow<wbr>Callback</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#addssenotifymessagelistener" class="tsd-kind-icon">add<wbr>Sse<wbr>Notify<wbr>Message<wbr>Listener</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#addtokenchanged" class="tsd-kind-icon">add<wbr>Token<wbr>Changed</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#addtokenexpiring" class="tsd-kind-icon">add<wbr>Token<wbr>Expiring</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#addtransportmessagelistener" class="tsd-kind-icon">add<wbr>Transport<wbr>Message<wbr>Listener</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#adduniversalerrorcallback" class="tsd-kind-icon">add<wbr>Universal<wbr>Error<wbr>Callback</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#adduniversalerrorresponsecallback" class="tsd-kind-icon">add<wbr>Universal<wbr>Error<wbr>Response<wbr>Callback</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#adduserinfochangelistener" class="tsd-kind-icon">add<wbr>User<wbr>Info<wbr>Change<wbr>Listener</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#calllogedin" class="tsd-kind-icon">call<wbr>Logedin</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#callresponsetoolargeorslowcallback" class="tsd-kind-icon">call<wbr>Response<wbr>Too<wbr>Large<wbr>OrSlow<wbr>Callback</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#calltokenchanged" class="tsd-kind-icon">call<wbr>Token<wbr>Changed</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#calltokenexpiring" class="tsd-kind-icon">call<wbr>Token<wbr>Expiring</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#calluniversalerrorcallback" class="tsd-kind-icon">call<wbr>Universal<wbr>Error<wbr>Callback</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#calluniversalerrorresponsecallback" class="tsd-kind-icon">call<wbr>Universal<wbr>Error<wbr>Response<wbr>Callback</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="events.html#calluserinfochangelistener" class="tsd-kind-icon">call<wbr>User<wbr>Info<wbr>Change<wbr>Listener</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="events.html#getsse" class="tsd-kind-icon">get<wbr>Sse</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="events.html#responsetoolargeorslowcallback" class="tsd-kind-icon">response<wbr>Too<wbr>Large<wbr>OrSlow<wbr>Callback</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="events.html#tokenexpiring" class="tsd-kind-icon">token<wbr>Expiring</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="events.html#universalerrorcallback" class="tsd-kind-icon">universal<wbr>Error<wbr>Callback</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="events.html#universalerrorresponsecallback" class="tsd-kind-icon">universal<wbr>Error<wbr>Response<wbr>Callback</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="events.html#userinfochange" class="tsd-kind-icon">user<wbr>Info<wbr>Change</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>