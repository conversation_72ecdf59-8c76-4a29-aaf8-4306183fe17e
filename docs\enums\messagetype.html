<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>MessageType | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="messagetype.html">MessageType</a>
				</li>
			</ul>
			<h1>Enumeration MessageType</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Enumeration members</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#action" class="tsd-kind-icon">Action</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#card" class="tsd-kind-icon">Card</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#file" class="tsd-kind-icon">File</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#generalordermsg" class="tsd-kind-icon">General<wbr>Order<wbr>Msg</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#image" class="tsd-kind-icon">Image</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#jobdescription" class="tsd-kind-icon">Job<wbr>Description</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#mpnavigate" class="tsd-kind-icon">Mp<wbr>Navigate</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#mypurchaseplan" class="tsd-kind-icon">My<wbr>Purchase<wbr>Plan</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#mywelfare" class="tsd-kind-icon">My<wbr>Welfare</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#navigatecard" class="tsd-kind-icon">Navigate<wbr>Card</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#notice" class="tsd-kind-icon">Notice</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#notify" class="tsd-kind-icon">Notify</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#pay" class="tsd-kind-icon">Pay</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#payresult" class="tsd-kind-icon">Pay<wbr>Result</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#payv1" class="tsd-kind-icon">Pay<wbr>V1</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#platformbasicnotify" class="tsd-kind-icon">Platform<wbr>Basic<wbr>Notify</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#platformshare" class="tsd-kind-icon">Platform<wbr>Share</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#position" class="tsd-kind-icon">Position</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#questionanswer" class="tsd-kind-icon">Question<wbr>Answer</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#refund" class="tsd-kind-icon">Refund</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#refundv1" class="tsd-kind-icon">Refund<wbr>V1</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#text" class="tsd-kind-icon">Text</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#video" class="tsd-kind-icon">Video</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#voice" class="tsd-kind-icon">Voice</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="messagetype.html#withdraw" class="tsd-kind-icon">Withdraw</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Enumeration members</h2>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="action" class="tsd-anchor"></a>
					<h3>Action</h3>
					<div class="tsd-signature tsd-kind-icon">Action<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;action&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:921</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="card" class="tsd-anchor"></a>
					<h3>Card</h3>
					<div class="tsd-signature tsd-kind-icon">Card<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;card&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:929</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="file" class="tsd-anchor"></a>
					<h3>File</h3>
					<div class="tsd-signature tsd-kind-icon">File<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;file&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:913</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="generalordermsg" class="tsd-anchor"></a>
					<h3>General<wbr>Order<wbr>Msg</h3>
					<div class="tsd-signature tsd-kind-icon">General<wbr>Order<wbr>Msg<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;general_order_msg&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:916</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="image" class="tsd-anchor"></a>
					<h3>Image</h3>
					<div class="tsd-signature tsd-kind-icon">Image<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;image&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:912</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="jobdescription" class="tsd-anchor"></a>
					<h3>Job<wbr>Description</h3>
					<div class="tsd-signature tsd-kind-icon">Job<wbr>Description<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;jd&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:932</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="mpnavigate" class="tsd-anchor"></a>
					<h3>Mp<wbr>Navigate</h3>
					<div class="tsd-signature tsd-kind-icon">Mp<wbr>Navigate<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;mp-navigate&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:923</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="mypurchaseplan" class="tsd-anchor"></a>
					<h3>My<wbr>Purchase<wbr>Plan</h3>
					<div class="tsd-signature tsd-kind-icon">My<wbr>Purchase<wbr>Plan<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;my_purchase_plan&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:918</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="mywelfare" class="tsd-anchor"></a>
					<h3>My<wbr>Welfare</h3>
					<div class="tsd-signature tsd-kind-icon">My<wbr>Welfare<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;my_welfare&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:919</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="navigatecard" class="tsd-anchor"></a>
					<h3>Navigate<wbr>Card</h3>
					<div class="tsd-signature tsd-kind-icon">Navigate<wbr>Card<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;navigate-card&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:931</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="notice" class="tsd-anchor"></a>
					<h3>Notice</h3>
					<div class="tsd-signature tsd-kind-icon">Notice<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;notice&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:935</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="notify" class="tsd-anchor"></a>
					<h3>Notify</h3>
					<div class="tsd-signature tsd-kind-icon">Notify<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;notify&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:922</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="pay" class="tsd-anchor"></a>
					<h3>Pay</h3>
					<div class="tsd-signature tsd-kind-icon">Pay<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;gpay2&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:925</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="payresult" class="tsd-anchor"></a>
					<h3>Pay<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">Pay<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;gresult&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:926</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="payv1" class="tsd-anchor"></a>
					<h3>Pay<wbr>V1</h3>
					<div class="tsd-signature tsd-kind-icon">Pay<wbr>V1<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;gpay&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:924</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="platformbasicnotify" class="tsd-anchor"></a>
					<h3>Platform<wbr>Basic<wbr>Notify</h3>
					<div class="tsd-signature tsd-kind-icon">Platform<wbr>Basic<wbr>Notify<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;basic_notify&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:934</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="platformshare" class="tsd-anchor"></a>
					<h3>Platform<wbr>Share</h3>
					<div class="tsd-signature tsd-kind-icon">Platform<wbr>Share<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;platform-share&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:933</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="position" class="tsd-anchor"></a>
					<h3>Position</h3>
					<div class="tsd-signature tsd-kind-icon">Position<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;position&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:930</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="questionanswer" class="tsd-anchor"></a>
					<h3>Question<wbr>Answer</h3>
					<div class="tsd-signature tsd-kind-icon">Question<wbr>Answer<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;question_answer&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:920</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="refund" class="tsd-anchor"></a>
					<h3>Refund</h3>
					<div class="tsd-signature tsd-kind-icon">Refund<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;grefund2&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:928</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="refundv1" class="tsd-anchor"></a>
					<h3>Refund<wbr>V1</h3>
					<div class="tsd-signature tsd-kind-icon">Refund<wbr>V1<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;grefund&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:927</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="text" class="tsd-anchor"></a>
					<h3>Text</h3>
					<div class="tsd-signature tsd-kind-icon">Text<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;text&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:911</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="video" class="tsd-anchor"></a>
					<h3>Video</h3>
					<div class="tsd-signature tsd-kind-icon">Video<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:914</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="voice" class="tsd-anchor"></a>
					<h3>Voice</h3>
					<div class="tsd-signature tsd-kind-icon">Voice<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;voice&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:915</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="withdraw" class="tsd-anchor"></a>
					<h3>Withdraw</h3>
					<div class="tsd-signature tsd-kind-icon">Withdraw<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;withdraw&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:917</li>
						</ul>
					</aside>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-enum">
						<a href="messagetype.html" class="tsd-kind-icon">Message<wbr>Type</a>
						<ul>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#action" class="tsd-kind-icon">Action</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#card" class="tsd-kind-icon">Card</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#file" class="tsd-kind-icon">File</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#generalordermsg" class="tsd-kind-icon">General<wbr>Order<wbr>Msg</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#image" class="tsd-kind-icon">Image</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#jobdescription" class="tsd-kind-icon">Job<wbr>Description</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#mpnavigate" class="tsd-kind-icon">Mp<wbr>Navigate</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#mypurchaseplan" class="tsd-kind-icon">My<wbr>Purchase<wbr>Plan</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#mywelfare" class="tsd-kind-icon">My<wbr>Welfare</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#navigatecard" class="tsd-kind-icon">Navigate<wbr>Card</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#notice" class="tsd-kind-icon">Notice</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#notify" class="tsd-kind-icon">Notify</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#pay" class="tsd-kind-icon">Pay</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#payresult" class="tsd-kind-icon">Pay<wbr>Result</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#payv1" class="tsd-kind-icon">Pay<wbr>V1</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#platformbasicnotify" class="tsd-kind-icon">Platform<wbr>Basic<wbr>Notify</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#platformshare" class="tsd-kind-icon">Platform<wbr>Share</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#position" class="tsd-kind-icon">Position</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#questionanswer" class="tsd-kind-icon">Question<wbr>Answer</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#refund" class="tsd-kind-icon">Refund</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#refundv1" class="tsd-kind-icon">Refund<wbr>V1</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#text" class="tsd-kind-icon">Text</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#video" class="tsd-kind-icon">Video</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#voice" class="tsd-kind-icon">Voice</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="messagetype.html#withdraw" class="tsd-kind-icon">Withdraw</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>