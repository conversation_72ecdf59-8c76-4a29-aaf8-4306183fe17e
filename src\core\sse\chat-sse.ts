import axios from "../../core/axios/index"
import { global } from "../global"

class Sse {
    private usedUid = ""

    private randomId() {
        return String(Math.floor(Math.random() * 65535 * 65535))
    }

    public getUUID() {
        if (this.usedUid) {
            return this.usedUid
        }
        return (this.usedUid = this.randomId())
    }

    public registerModels<T>(models: string[]) {
        const set = new Set(models)
        const items = []
        set.forEach((v) => items.push(v))
        return global.sse
            ? axios.post<T>(
                  `general/sse/uuid/${this.getUUID()}/subscribe/${items.join(
                      ","
                  )}`
              )
            : Promise.reject(new Error("SSE not ready"))
    }

    public unregisterModels<T>() {
        return axios.post<T>(`general/sse/uuid/${this.getUUID()}/unsubscribe`)
    }

    public registerMenuOnBadgeChanged<T>(
        models: string[],
        project: "entrance" | "communication" | "todo"
    ) {
        const set = new Set(models)
        const items = []
        set.forEach((v) => items.push(v))
        const tail = items.length ? `?dataNames=${items.join(",")}` : ""
        return global.sse
            ? axios.post<T>(
                  `general/sse/uuid/${this.getUUID()}/subscribeMenuData/${project}${tail}`
              )
            : Promise.reject(new Error("SSE not ready"))
    }

    public unregisterMenus<T>() {
        return axios.post<T>(
            `general/sse/uuid/${this.getUUID()}/unsubscribeMenuData`
        )
    }
}

export const chatSse = new Sse()
