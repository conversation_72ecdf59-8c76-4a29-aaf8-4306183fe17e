import axios from "../../core/axios/index"
import type * as dto from "../../def/index"

export default class OrgApi {
    public getOrg(orgId: number) {
        return axios.get<dto.Org.Org>(`general/entrances/org/${orgId}`)
    }

    private buildQueryString(q: string[]) {
        if (q.length) {
            return `?${q.join("&")}`
        }
        return ""
    }

    public loadList(params: dto.Org.LoadListApiParams) {
        const q = []
        params.name && q.push(`name=${encodeURIComponent(params.name)}`)
        q.push(`limitBegin=${params.itemIndex}`)
        params.itemSize && q.push(`limitSize=${params.itemSize}`)
        if (params.application) {
            params.oid && q.push(`oid=${params.oid}`)
            return axios.get<dto.Org.OrgList<dto.Org.ApplicationOrg>>(
                `general/application/${
                    params.application
                }/instance/list${this.buildQueryString(q)}`
            )
        }
        return axios.get<dto.Org.OrgList<dto.Org.Org>>(
            `general/entrances/org/list${this.buildQueryString(q)}`
        )
    }
}
