import cloneDeep from "lodash/cloneDeep"
import uniqBy from "lodash/uniqBy"

import { AnonymousModel } from "../../core/anonymous-api"
import { events } from "../../core/events"
import { getAllFilters } from "../../core/tools/filter"
import { sseWorker } from "../../core/tools/sse"
import type * as dto from "../../def/index"

import ActionApi from "./api"
import { ActionForm, getFormItem } from "./form-item"

type SseMessageResp = {
    id: string
    type: string
    max: number
    current: number
    msg: string
    forward: string
}

type DetailItem =
    dto.ActionTypes.DetailParameterManager.result["values"][number]

class DetailParameterManager {
    private result: dto.ActionTypes.DetailParameterManager.result
    /**
     * detail parameter页签名
     */
    public name: string
    constructor(
        private detail: dto.ActionTypes.details_parameters,
        private updator: (
            result: dto.ActionTypes.DetailParameterManager.result
        ) => void
    ) {
        this.name = detail.name
        const values = detail.datas.map((k) => {
            return {
                keyValue: k.keyValue,
                rowData: Object.keys(k.rowData).map((property) => {
                    return {
                        property,
                        value: k.rowData[property].value as string,
                        display: k.rowData[property].display,
                        emptyValue: k.rowData[property].emptyValue,
                    }
                }),
            }
        })
        this.result = {
            name: this.detail.name,
            values,
            deleted: [],
        }
        if (detail.datas.length > 0) {
            this.done()
        }
    }

    private handleRowDataParam(
        rowData: dto.ActionTypes.DetailParameterManager.addParam
    ): dto.ActionTypes.DetailParameterManager.rowData[] {
        return Object.keys(rowData).map((property) => {
            const value = rowData[property] as string
            return {
                property,
                value,
            }
        })
    }

    private update() {
        const copyResult = cloneDeep(
            this.result
        ) as dto.ActionTypes.DetailParameterManager.result
        copyResult.values.forEach((k) => {
            k.rowData = k.rowData.map((j) => {
                const { property, value } = j
                return {
                    property,
                    value,
                }
            })
        })
        this.updator(copyResult)
    }
    /**
     *
     * 获取默认数据以供编辑
     */
    public getInputedData() {
        return this.detail.datas.map((k) => {
            const { keyValue, rowData } = k
            return {
                keyValue,
                rowData,
                del: () => {
                    this.delete(keyValue)
                },
                edit: (
                    rawRowData: dto.ActionTypes.DetailParameterManager.addParam
                ) => {
                    this.edit(keyValue, rawRowData)
                },
            }
        })
    }
    /**
     * 删除指定数据
     * @param keyValue
     */
    public delete(keyValue: string) {
        this.result.values = this.result.values.filter(
            (k) => k.keyValue !== keyValue
        )
        this.result.deleted.push(keyValue)
        return this
    }
    /**
     * 删除指定数据
     * @param keyValue
     */
    public deleteByPropertyValue(proerty: string, value: unknown) {
        this.result.values
            .filter((_value) => {
                const wantedInput = _value.rowData.find(
                    (k) => k.property === proerty
                )
                if (wantedInput == null) return false
                if (wantedInput.value === value) {
                    return true
                }
                return false
            })
            .map((k) => k.keyValue)
            .forEach((keyValue) => this.delete(keyValue))

        return this
    }
    /**
     * 更新指定数据
     * @param keyValue
     * @param rawRowData
     */
    public edit(
        keyValue: string,
        rawRowData: dto.ActionTypes.DetailParameterManager.addParam
    ) {
        const idx = this.result.values.findIndex((k) => {
            return k.keyValue === keyValue
        })
        this.result.values.splice(idx, 1, {
            keyValue,
            rowData: this.handleRowDataParam(rawRowData),
        })
        return this
    }
    /**
     * 添加表单数据
     * @param rawRowData
     */
    public add(rawRowData: dto.ActionTypes.DetailParameterManager.addParam) {
        this.result.values.push({
            keyValue: "",
            rowData: this.handleRowDataParam(rawRowData),
        })
        return this
    }
    public uniqByKeyValue() {
        const hasKeyValueValues = this.result.values.filter(
            (k) => k.keyValue != null && k.keyValue !== ""
        )
        const nohasKeyValueValues = this.result.values.filter(
            (k) => k.keyValue == null || k.keyValue === ""
        )
        const hasKeyValueValuesSet = uniqBy(hasKeyValueValues, "keyValue")
        this.result.values = [...hasKeyValueValuesSet, ...nohasKeyValueValues]
        return this
    }

    public uniqBy(proerty: string) {
        this.uniqByKeyValue()
        this.result.values = uniqBy(this.result.values, (item: DetailItem) => {
            return item.rowData.find((row) => row.property === proerty).value
        })
        return this
    }
    /**
     * 编辑完成
     */
    public done() {
        this.update()
        return this.result
    }
}

export class Action extends AnonymousModel {
    private meta?: dto.ActionTypes.info
    protected api: ActionApi
    private selected_list: dto.ActionTypes.selectedList[] = []
    private prefilters: dto.prefilter[] = []
    private hiddenInputParameters: dto.ActionTypes.inputs_parameters[] = []
    private dataDetails: dto.ActionTypes.detaDetail[] = []
    private inputs_parameters: dto.ActionTypes.inputs_parameters[] = []
    private detailParametersManagers: Array<DetailParameterManager> = []
    private list_parameters: dto.ActionTypes.list_parameter[] = []
    private notQueried = true
    private actionId?: string
    private listName?: string //某些action需要知道当前列表名字
    private modelName?: string

    constructor(props: dto.ActionTypes.constructorType) {
        super()
        this.api = new ActionApi(props.model_name, props.action_name)
        this.modelName = props.model_name
    }

    private bigActionCallbackOnChange: dto.SSE.callBackOfBigActionExecute =
        () => null

    private onBigActionMessageNotify = (msg: dto.SSE.msg) => {
        const type = msg.type
        if (type != "bigAction") {
            return
        }
        const key = msg.key as string
        const arr = key.split(",")
        const modelName = arr[0]
        const taskId = parseInt(arr[1])
        if (this.modelName != modelName) {
            return
        }
        this.bigActionCallbackOnChange &&
            this.bigActionCallbackOnChange(modelName, taskId)
    }

    /**
     * 注册当前模型的数据变化时的回掉
     * @param CallBackOfModelUpdated
     */
    public registerOnBigActionExecute(cb: dto.SSE.callBackOfBigActionExecute) {
        this.bigActionCallbackOnChange = cb
        return events.addBigActionMessageListener(this.onBigActionMessageNotify)
    }

    public async bigActionSingal(taskId: number, signal: string) {
        return this.api.bigActionSignal(taskId, signal)
    }

    public async bigActionDetail(query: dto.ActionTypes.BigActionDetailQuery) {
        return this.api.bigActionDetail(query)
    }

    public async bigActionCheck() {
        return this.api.bigActionCheck()
    }

    public bigActionFileUrl() {
        return this.api.bigActionFileUrl()
    }

    public async bigActionImport(param) {
        return this.api.bigActionImport(param)
    }

    public setListName(name: string) {
        this.listName = name
        return this
    }

    public clearListName() {
        this.listName = null
        return this
    }

    /**
     * 如方法名所示，会在当前类实例中保存一个参数。
     * @returns 返回当前实例，供链式调用
     */
    public addInputs_parameter(
        values: dto.ActionTypes.addInputs_parameterParams
    ) {
        this.inputs_parameters = Object.keys(values).map((k) => {
            return {
                property: k,
                value: values[k],
            }
        })
        return this
    }

    /**
     * 清空方法名所示的参数的数组
     * @returns 返回当前实例，供链式调用
     */
    public clearInputs_parameter() {
        this.inputs_parameters = []
        return this
    }

    private async queryForYou() {
        if (this.notQueried) {
            await this.query(this.selected_list, this.prefilters)
        }
    }

    /**
     * 获取所有detailParameters manager
     */
    public async getDetailParametersManager() {
        await this.queryForYou()
        return this.detailParametersManagers
    }

    /**
     * 获取指定detailParameters manager
     */
    public async getDetailParametersManagerByName(name: string) {
        await this.queryForYou()
        return this.detailParametersManagers.find((k) => k.name === name)
    }

    /**
     * 添加excel中的数据，以键值对的形式
     * 键是excel的表头
     * @returns 返回当前实例，供链式调用
     */
    public addExcel(excel: dto.ActionTypes.list_parameter[]) {
        this.list_parameters = excel
        return this
    }

    /**
     * 清空方法名所示的参数的数组
     * @returns 返回当前实例，供链式调用
     */
    public clearExcel() {
        this.list_parameters = []
        return this
    }

    /**
     * 设置actionId
     * @param actionId
     * @returns 返回当前实例，供链式调用
     */
    public setActionId(actionId: string) {
        this.actionId = actionId
        return this
    }

    /**
     * 手动更新缓存的
     * @param selected_list
     * @param prefilters
     */
    public updateInitialParams(params: dto.ActionTypes.updateInitialParams) {
        if (params.selected_list) {
            this.selected_list = params.selected_list
        }
        if (params.prefilters) {
            this.prefilters = params.prefilters
        }
        return this
    }

    private handleHiddenTypeParameters() {
        if (this.meta == null) return
        const hiddenTypeInputParams =
            this.meta.parameters.inputs_parameters.filter(
                (k) => k.type === "hidden"
            )
        //有些default_value为空，需要调用updateControlsPropertiesd后才会得到default_value，需要处理这个逻辑
        //这里先过滤掉没有default_value的
        this.hiddenInputParameters = hiddenTypeInputParams
            .filter((k) => !!k.default_value)
            .map((k) => {
                return {
                    property: k.property,
                    value: k.default_value,
                }
            })
    }

    private getInputsParameters() {
        const items: dto.ActionTypes.inputs_parameters[] = []
        for (const item of [
            ...this.hiddenInputParameters,
            ...this.inputs_parameters,
        ]) {
            const t = items.find((i) => i.property === item.property)
            if (t) {
                t.value = item.value
            } else {
                items.push(item)
            }
        }
        return items
    }

    /**
     * 得到action详情
     */
    public async query(
        selected_list?: dto.ActionTypes.selectedList[],
        prefilters?: dto.prefilter[],
        others?: {
            workflowExecuteContext?: dto.workflow2.WorkflowExecuteContext
            /**
             * @deprecated 即将废弃，请使用 intentContext
             */
            intent?: dto.ActionIntent
            intentContext?: dto.ActionIntent
        }
    ) {
        if (selected_list) {
            this.selected_list = selected_list
        }
        if (prefilters) {
            this.prefilters = prefilters
        }

        const p = {
            selected_list: this.selected_list,
            prefilters: this.prefilters,
        }

        if (others) {
            if (others.workflowExecuteContext) {
                Object.assign(p, {
                    workflowExecuteContext: others.workflowExecuteContext,
                })
            }
            if (others.intent) {
                Object.assign(p, { intent: others.intent })
            }
            if (others.intentContext) {
                Object.assign(p, { intentContext: others.intentContext })
            }
        }

        const data = (this.meta = await this.api.getActionDetail(p))
        this.actionId = data.actionId
        this.detailParametersManagers =
            this.meta.parameters.details_parameters.map(
                (k) =>
                    new DetailParameterManager(k, (result) => {
                        const name = result.name
                        this.dataDetails = this.dataDetails.filter(
                            (detail) => detail.name !== name
                        )
                        this.dataDetails.push(result)
                    })
            )
        this.handleHiddenTypeParameters()

        this.notQueried = false
        return data
    }

    /**
     * 获取当前有权操作当前action的人的列表
     */
    public getAuthorsList() {
        return this.api.getAuthorsList()
    }

    /**
     * 执行action
     * @param ExecuteParams
     */
    public async execute(params: dto.ActionTypes.executeParams = {}) {
        await this.queryForYou()
        return this.dryExecute(params)
    }

    public async dryExecute(params: dto.ActionTypes.executeParams = {}) {
        const dataDetails = params.dataDetails ?? this.dataDetails
        const inputs_parameters =
            params.inputs_parameters ?? this.getInputsParameters()
        if (!params.filters) {
            params.filters = getAllFilters([])
        }
        return this.api.executeAction({
            ...params,
            dataDetails,
            inputs_parameters,
            selected_list: this.selected_list,
            prefilters: this.prefilters,
            actionId: this.actionId,
        })
    }

    /**
     * 立即执行action
     * @param ExecuteParams
     */
    public executeNow(params: dto.ActionTypes.executeNowParams) {
        return this.api.executeAction({
            dataDetails: [],
            inputs_parameters: [],
            selected_list: [],
            prefilters: [],
            ...params,
            actionId: this.actionId,
        })
    }

    /**
     * 验证batch action
     * @param ValidateBatchParams
     * @returns Promise<validateRequestResult>;
     */
    public validateBatch(params: dto.ActionTypes.validateBatchParams) {
        return this.api.validateBatchAction({
            ...params,
            prefilters: this.prefilters,
            list_parameters: params.list_parameters ?? this.list_parameters,
            tagInfosForList: params.tagInfosForList ?? [],
            actionId: this.actionId,
            selected_list: this.selected_list || [],
            inputs_parameters:
                params.inputs_parameters ?? (this.inputs_parameters || []),
        })
    }
    /**
     * 执行batch action
     * @param ExecuteBatchParams
     *
     */
    public async executeBatch(params: dto.ActionTypes.executeBatchParams) {
        await this.queryForYou()
        return this.api.executeBatchAction({
            ...params,
            selected_list: this.selected_list,
            prefilters: this.prefilters,
            list_parameters: params.list_parameters ?? this.list_parameters,
            tagInfosForList: params.tagInfosForList ?? [],
            actionId: this.actionId,
            inputs_parameters:
                params.inputs_parameters ?? (this.inputs_parameters || []),
        })
    }

    /**
     * 执行each类型action
     * 返回开始执行方法，开始执行方法可以传入@param onProgress @param onError
     * 返回一个将被完成的Promise实例@param awaiting 和主动关闭链接的方法 @param close;
     * @param params
     */
    public executeEach(
        params: dto.ActionTypes.executeEachParams,
        headers?: { CurrentOrg?: string; Entrance?: string }
    ) {
        let successCount = 0
        const errorRow: dto.ActionTypes.eachErrorRow[] = []
        return (
            onProgress: dto.ActionTypes.eachOnProgress,
            onError: dto.ActionTypes.eachOnError
        ) => {
            const url = this.api.createEachActionSSEURL({
                ...params,
                prefilters: this.prefilters,
                actionId: this.actionId,
                name: this.listName,
            })

            const onOpen = () => {
                onProgress(0, 0)
            }

            type PromiseFunc = dto.PromiseFunc<string>
            const onSSEError = (_done: PromiseFunc, failed: PromiseFunc) => {
                failed("失败，连接已关闭")
            }
            type Msg = { data: string }
            const onMsg = (e: Msg, done: PromiseFunc) => {
                const result: SseMessageResp = JSON.parse(e.data)
                if (result.type === "error") {
                    console.error("testing each action:", e)
                    errorRow.push({ id: result.id, error: result.msg })
                    onError(errorRow)
                } else if (result.type === "percent") {
                    if (result.current) {
                        const percentage = Math.floor(
                            (result.current * 100) / result.max
                        )
                        successCount++
                        onProgress(percentage, successCount)
                    }
                } else if (result.type === "finish") {
                    onProgress(100, successCount)
                    done(result.forward)
                }
            }
            return sseWorker<Msg, string>(
                url,
                {
                    onOpen,
                    onMsg,
                    onError: onSSEError,
                },
                headers
            )
        }
    }

    /**
     *  用于上传excel的地方的加载远程数据的action
     * @param params
     */
    public getDataSource(params: dto.ActionTypes.getDataSourceRequestParams) {
        return this.api.getDataSource(params)
    }

    /**
     * 获取指定excel模版的下载链接
     * @param schemaName
     */
    public getBatchExcelTemplate(schemaName: string) {
        return this.api.getBatchExcelTemplate(schemaName)
    }

    /**
     * 处理某些inputParamster，其类型为hidden，且值需要调用updateControlsProperties来获得
     *
     */
    private handleNoValueInputParamsters(
        data: dto.ActionTypes.updateControlsPropertiesRequestResult["masters"]
    ) {
        if (!this.meta) return
        const unfullfilledHiddenTypeInputParams =
            this.meta.parameters.inputs_parameters
                .filter((k) => k.type === "hidden")
                .filter((k) => !k.default_value)
                .map((k) => {
                    const d = data.find((item) => item.property === k.property)
                    return {
                        property: k.property,
                        value: d ? d.default_value : "",
                    }
                })
        this.hiddenInputParameters.push(...unfullfilledHiddenTypeInputParams)
        this.hiddenInputParameters = uniqBy(
            this.hiddenInputParameters.reverse(),
            "property"
        )
    }

    /**
     * @param property 需要触发此方法的表单项的property
     * @param params  同execute的参数
     */
    public async updateControlsProperties(
        property: string,
        params: dto.ActionTypes.updateControlsPropertiesRequestParams = {}
    ) {
        const result = await this.api.updateControlsProperties(property, {
            dataDetails: params.dataDetails ?? this.dataDetails ?? [],
            inputs_parameters:
                params.inputs_parameters ?? this.getInputsParameters() ?? [],
            selected_list: this.selected_list || [],
            prefilters: this.prefilters || [],
            actionId: this.actionId,
        })
        this.handleNoValueInputParamsters(result.masters)
        return result
    }

    public async validateInput(property: string) {
        await this.queryForYou()
        return this.api.validateInput(property, {
            dataDetails: this.dataDetails,
            inputs_parameters: this.getInputsParameters(),
            selected_list: this.selected_list,
            prefilters: this.prefilters,
            actionId: this.actionId,
        })
    }

    public async getForm() {
        const noSubmitType = [
            "hidden",
            "tip",
            "cancel_btn",
            "sure_btn",
            "updator_btn",
            "img",
        ]
        await this.queryForYou()
        const inputs = this.meta.parameters.inputs_parameters
            .filter((k) => !noSubmitType.includes(k.type))
            .map((k) => getFormItem(cloneDeep(k)))
        this.meta.parameters.details_parameters.map((k) => {
            return {
                label: k.label,
                name: k.name,
                inputs: k.controls
                    .filter((k) => !noSubmitType.includes(k.type))
                    .map((k) => getFormItem(cloneDeep(k))),
            }
        })
        return new ActionForm(inputs)
    }

    /**
     * 根据actionId清理服务器端action的缓存
     */
    public async clearAction() {
        if (this.actionId) {
            return await this.api.clearAction(this.actionId)
        }
    }

    public executeInnerAction<T>(actionName: string) {
        return this.api.executeInnerAction<T>(actionName, {
            inputs_parameters: this.inputs_parameters,
            selected_list: this.selected_list,
        })
    }
}
