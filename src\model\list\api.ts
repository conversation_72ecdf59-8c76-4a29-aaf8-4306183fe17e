import { Anony<PERSON><PERSON><PERSON> } from "../../core/anonymous-api"
import axios from "../../core/axios/index"
import { global } from "../../core/global"
import { encodeParams4Parameters } from "../../core/tools/form"
import type * as dto from "../../def/index"

class BaseList<PERSON>pi extends AnonymousApi {
    protected model_name = ""
    public updateFilterParam(
        property: string,
        parameters: {
            name?: string
            filters: dto.Tools.FiltersType
        } & Omit<dto.ListTypes.queryProps, "filters">
    ) {
        return axios.get<dto.ListTypes.updateFilterParamRequestResult>(
            `general/model/${
                this.model_name
            }/filter/update/${property}?${encodeParams4Parameters(parameters)}`
        )
    }

    // 查询列表recordCount
    public getPageRecordCount(params: dto.ListTypes.getListDataByTabParams) {
        return axios.post<{
            page_datas: {
                [key: string]: {
                    name: string
                    record_count: number
                    item_index: number
                    item_size: number
                }
            }
            record_count: number
        }>(`general/model/${this.model_name}/list2/page_datas`, params)
    }

    public getPagesMeta(
        params: dto.ListTypes.setColumnsForPagesParams & {
            list_name?: string
            prefilters: dto.prefilters
        }
    ) {
        return axios.post<dto.ListTypes.setColumnsForPagesApiResult>(
            `general/model/${this.model_name}/page_meta`,
            params
        )
    }

    public updateAction(
        params: dto.ListTypes.updateAction & {
            prefilters: dto.prefilters
            name?: string
        }
    ) {
        return axios.post<dto.ListTypes.updateActionRequestResult>(
            `general/model/${this.model_name}/list2/property/${
                params.pageName ?? ""
            }`,
            params
        )
    }

    public async getRowDetail(keyFieldValue: string) {
        return await axios.get<unknown>(
            `general/model/${this.model_name}/key/${keyFieldValue}/minidetail`
        )
    }

    public createExportUrl(parameters: dto.ListTypes.exportToExcel) {
        return `${global.baseUrl}general/model/sse/${
            this.model_name
        }/export?${encodeParams4Parameters(parameters, true)}`
    }

    public createExportUrlV2(eid: string) {
        return `${global.baseUrl}general/model/sse/${this.model_name}/export/v2?eid=${eid}`
    }

    public createExportUrl2Word(eid: string) {
        return `${global.baseUrl}general/model/sse/${this.model_name}/export/word?eid=${eid}`
    }

    public createExportUrlV2ForCsv(eid: string) {
        return `${global.baseUrl}general/model/sse/${this.model_name}/exportCsv?eid=${eid}`
    }

    public getDefaultTemplateUrl(
        list_name: string | undefined,
        page_name: string
    ) {
        const parameters: { name?: string; page_name: string } = {
            page_name,
        }
        if (list_name) {
            parameters.name = list_name
        }
        return axios.get<string>(
            `general/model/${
                this.model_name
            }/exportListTemplate?${encodeParams4Parameters(parameters)}`
        )
    }

    public getAllDefaultTemplateUrl(list_name?: string) {
        return this.getDefaultTemplateUrl(list_name, "")
    }

    public getfilterGroupDetail(parameters: unknown) {
        return axios.post<dto.ListTypes.filterGroup>(
            `general/model/${this.model_name}/group`,
            parameters
        )
    }

    //先把参数通过post闯过去
    //返回一个eid:string
    public postParam4Excel(parameters: dto.ListTypes.exportToExcel) {
        return axios.post<string>(`general/model/postExportParameters`, {
            parameters: JSON.stringify(parameters),
        })
    }

    public getListMeta(parameters: dto.ListTypes.initProps) {
        return axios.post<dto.ListTypes.BaseListDataRequestResult>(
            `${this.urlPrefix}/model/${this.model_name}/list2/meta`,
            parameters
        )
    }
}

export class listApi extends BaseListApi {
    constructor(model_name: string) {
        super()
        this.model_name = model_name
    }

    public getListData<RowType extends dto.ListRow = dto.ListRow>(
        parameters: dto.ListTypes.initProps
    ) {
        return axios.post<dto.ListTypes.getListDataRequestResult<RowType>>(
            `${this.urlPrefix}/model/${this.model_name}/list2`,
            parameters
        )
    }

    public getListDataByTab<RowType extends dto.ListRow = dto.ListRow>(
        tabName: string,
        parameters: dto.ListTypes.getListDataByTabParams
    ) {
        return axios.post<dto.ListTypes.getListDataByTabRequestResult<RowType>>(
            `${this.urlPrefix}/model/${this.model_name}/list_by_page2/${tabName}`,
            parameters
        )
    }

    public getListDataOfSinglePage<RowType extends dto.ListRow = dto.ListRow>(
        parameters: dto.ListTypes.getListDataOfSinglePageParams
    ) {
        return axios.post<
            dto.ListTypes.getListDataOfSinglePageRequestResult<RowType>
        >(`${this.urlPrefix}/model/${this.model_name}/list2/data`, parameters)
    }
}

export class listApi2 extends BaseListApi {
    constructor(model_name: string) {
        super()
        this.model_name = model_name
    }

    public getListData(parameters: dto.ListTypes.initProps) {
        return axios.post<dto.ListTypes.getListDataRequestResult2>(
            `${this.urlPrefix}/model/${this.model_name}/list3`,
            parameters
        )
    }

    public getListDataByTab(
        tabName: string,
        parameters: dto.ListTypes.getListDataByTabParams
    ) {
        return axios.post<dto.ListTypes.getListDataByTabRequestResult2>(
            `${this.urlPrefix}/model/${this.model_name}/list_by_page3/${tabName}`,
            parameters
        )
    }

    public getListDataOfSinglePage(
        parameters: dto.ListTypes.getListDataOfSinglePageParams
    ) {
        return axios.post<dto.ListTypes.getListDataOfSinglePageRequestResult2>(
            `${this.urlPrefix}/model/${this.model_name}/list3/data`,
            parameters
        )
    }
}
