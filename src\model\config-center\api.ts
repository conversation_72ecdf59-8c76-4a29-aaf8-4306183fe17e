import axios from "../../core/axios/index"
import type * as dto from "../../def/index"

export default class ConfigCenterAPI {
    public query() {
        return axios.get<dto.ConfigCenterTypes.QueryAPIResult>(
            `general/configs/allmeta`
        )
    }
    public revalidateModels(dataId: string) {
        return axios.post<void>(`general/configs/revalidateModels/${dataId}`)
    }
    public rebuildFromTemplate(dataId: string) {
        return axios.post<void>(`general/configs/rebuildDataModel/${dataId}`)
    }
    public reload() {
        return axios.post<void>(`general/entrances/reload`)
    }
    public createTable(params: dto.ConfigCenterTypes.CreateTableAPIParams) {
        const formdata = new FormData()
        formdata.append("text", params.sqlString)
        return axios.post<void>(
            `general/configs/createTable/${params.dataSourceName}`,
            formdata
        )
    }
    public createTableSQLScript(
        params: dto.ConfigCenterTypes.createTableSQLScriptAPIParams
    ) {
        return axios.get<dto.ConfigCenterTypes.createTableSQLScriptRequestResult>(
            `general/configs/createSqlScript/${params.dataSourceName}/${params.tableName}/${params.dbtype}`
        )
    }

    public createSchemaSQLScript(
        params: dto.ConfigCenterTypes.createSchemaSQLScript
    ) {
        return axios.get<dto.ConfigCenterTypes.createSchemaSQLScriptRequestResult>(
            `general/configs/createSchemaSqlScript/${params.dataSourceName}/${params.dbtype}`
        )
    }
    public createDataModel(params: dto.ConfigCenterTypes.createDataModel) {
        return axios.post<void>(
            `general/configs/createDataModel/${params.subProjectName}/${params.dataSourceName}/${params.tableName}/${params.modelName}`
        )
    }

    public jsonParseETL(dataId: string) {
        return axios.post<void>(`}general/configs/transETLToJson/${dataId}`)
    }

    public deploy(dataId: string) {
        return axios.post<dto.ConfigCenterTypes.depolyApiResult>(
            `general/configs/deploy/${dataId}`
        )
    }
}
