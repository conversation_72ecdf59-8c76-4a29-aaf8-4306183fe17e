<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>ActionApi | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="actionapi.html">ActionApi</a>
				</li>
			</ul>
			<h1>Class ActionApi</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<a href="anonymousapi.html" class="tsd-signature-type">AnonymousApi</a>
						<ul class="tsd-hierarchy">
							<li>
								<span class="target">ActionApi</span>
							</li>
						</ul>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="actionapi.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="actionapi.html#action_name" class="tsd-kind-icon">action_<wbr>name</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="actionapi.html#isanonymous" class="tsd-kind-icon">is<wbr>Anonymous</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="actionapi.html#model_name" class="tsd-kind-icon">model_<wbr>name</a></li>
							</ul>
						</section>
						<section class="tsd-index-section tsd-is-inherited">
							<h3>Accessors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-get-signature tsd-parent-kind-class tsd-is-inherited"><a href="actionapi.html#urlprefix" class="tsd-kind-icon">url<wbr>Prefix</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="actionapi.html#anonymous" class="tsd-kind-icon">anonymous</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="actionapi.html#bigactioncheck" class="tsd-kind-icon">big<wbr>Action<wbr>Check</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="actionapi.html#bigactiondetail" class="tsd-kind-icon">big<wbr>Action<wbr>Detail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="actionapi.html#bigactionfileurl" class="tsd-kind-icon">big<wbr>Action<wbr>File<wbr>Url</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="actionapi.html#bigactionimport" class="tsd-kind-icon">big<wbr>Action<wbr>Import</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="actionapi.html#bigactionsignal" class="tsd-kind-icon">big<wbr>Action<wbr>Signal</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="actionapi.html#clearaction" class="tsd-kind-icon">clear<wbr>Action</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="actionapi.html#createeachactionsseurl" class="tsd-kind-icon">create<wbr>Each<wbr>ActionSSEURL</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="actionapi.html#executeaction" class="tsd-kind-icon">execute<wbr>Action</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="actionapi.html#executebatchaction" class="tsd-kind-icon">execute<wbr>Batch<wbr>Action</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="actionapi.html#executeinneraction" class="tsd-kind-icon">execute<wbr>Inner<wbr>Action</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="actionapi.html#getactiondetail" class="tsd-kind-icon">get<wbr>Action<wbr>Detail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="actionapi.html#getauthorslist" class="tsd-kind-icon">get<wbr>Authors<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="actionapi.html#getbatchexceltemplate" class="tsd-kind-icon">get<wbr>Batch<wbr>Excel<wbr>Template</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="actionapi.html#getdatasource" class="tsd-kind-icon">get<wbr>Data<wbr>Source</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="actionapi.html#unanonymous" class="tsd-kind-icon">un<wbr>Anonymous</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="actionapi.html#updatecontrolsproperties" class="tsd-kind-icon">update<wbr>Controls<wbr>Properties</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="actionapi.html#validatebatchaction" class="tsd-kind-icon">validate<wbr>Batch<wbr>Action</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="actionapi.html#validateinput" class="tsd-kind-icon">validate<wbr>Input</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new <wbr>Action<wbr>Api<span class="tsd-signature-symbol">(</span>model_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, action_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="actionapi.html" class="tsd-signature-type">ActionApi</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:8</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>model_name: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>action_name: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="actionapi.html" class="tsd-signature-type">ActionApi</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="action_name" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> action_<wbr>name</h3>
					<div class="tsd-signature tsd-kind-icon">action_<wbr>name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/api.ts:9</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited">
					<a name="isanonymous" class="tsd-anchor"></a>
					<h3>is<wbr>Anonymous</h3>
					<div class="tsd-signature tsd-kind-icon">is<wbr>Anonymous<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
						<p>Inherited from <a href="anonymousapi.html">AnonymousApi</a>.<a href="anonymousapi.html#isanonymous">isAnonymous</a></p>
						<ul>
							<li>Defined in src/core/anonymous-api.ts:2</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="model_name" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> model_<wbr>name</h3>
					<div class="tsd-signature tsd-kind-icon">model_<wbr>name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/api.ts:9</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group tsd-is-inherited">
				<h2>Accessors</h2>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class tsd-is-inherited">
					<a name="urlprefix" class="tsd-anchor"></a>
					<h3>url<wbr>Prefix</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> urlPrefix<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"public"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"general"</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="anonymousapi.html">AnonymousApi</a>.<a href="anonymousapi.html#urlprefix">urlPrefix</a></p>
								<ul>
									<li>Defined in src/core/anonymous-api.ts:12</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">"public"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"general"</span></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="anonymous" class="tsd-anchor"></a>
					<h3>anonymous</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">anonymous<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="anonymousapi.html">AnonymousApi</a>.<a href="anonymousapi.html#anonymous">anonymous</a></p>
								<ul>
									<li>Defined in src/core/anonymous-api.ts:3</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="bigactioncheck" class="tsd-anchor"></a>
					<h3>big<wbr>Action<wbr>Check</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">big<wbr>Action<wbr>Check<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#bigactioncheckresp" class="tsd-signature-type">BigActionCheckResp</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:26</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#bigactioncheckresp" class="tsd-signature-type">BigActionCheckResp</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="bigactiondetail" class="tsd-anchor"></a>
					<h3>big<wbr>Action<wbr>Detail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">big<wbr>Action<wbr>Detail<span class="tsd-signature-symbol">(</span>query<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.BigActionDetailQuery</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#bigactiondetailresult" class="tsd-signature-type">BigActionDetailResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:19</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>query: <span class="tsd-signature-type">dto.ActionTypes.BigActionDetailQuery</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#bigactiondetailresult" class="tsd-signature-type">BigActionDetailResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="bigactionfileurl" class="tsd-anchor"></a>
					<h3>big<wbr>Action<wbr>File<wbr>Url</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">big<wbr>Action<wbr>File<wbr>Url<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:32</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="bigactionimport" class="tsd-anchor"></a>
					<h3>big<wbr>Action<wbr>Import</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">big<wbr>Action<wbr>Import<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:36</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">any</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="bigactionsignal" class="tsd-anchor"></a>
					<h3>big<wbr>Action<wbr>Signal</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">big<wbr>Action<wbr>Signal<span class="tsd-signature-symbol">(</span>taskId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, signal<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#bigactiondetailresult" class="tsd-signature-type">BigActionDetailResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:13</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>taskId: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>signal: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#bigactiondetailresult" class="tsd-signature-type">BigActionDetailResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearaction" class="tsd-anchor"></a>
					<h3>clear<wbr>Action</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Action<span class="tsd-signature-symbol">(</span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:176</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>actionId: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="createeachactionsseurl" class="tsd-anchor"></a>
					<h3>create<wbr>Each<wbr>ActionSSEURL</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">create<wbr>Each<wbr>ActionSSEURL<span class="tsd-signature-symbol">(</span>parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-type">dto.ActionTypes.executeEachParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:74</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>parameters: <span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-type">dto.ActionTypes.executeEachParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="executeaction" class="tsd-anchor"></a>
					<h3>execute<wbr>Action</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">execute<wbr>Action<span class="tsd-signature-symbol">(</span>parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-type">dto.ActionTypes.executeParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:57</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>parameters: <span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-type">dto.ActionTypes.executeParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="executebatchaction" class="tsd-anchor"></a>
					<h3>execute<wbr>Batch<wbr>Action</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">execute<wbr>Batch<wbr>Action<span class="tsd-signature-symbol">(</span>parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>list_parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.list_parameter</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tagInfosForList<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.TagInfos</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-type">dto.ActionTypes.executeParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:102</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>parameters: <span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>list_parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.list_parameter</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tagInfosForList<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.TagInfos</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-type">dto.ActionTypes.executeParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="executeinneraction" class="tsd-anchor"></a>
					<h3>execute<wbr>Inner<wbr>Action</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">execute<wbr>Inner<wbr>Action&lt;T&gt;<span class="tsd-signature-symbol">(</span>actionName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>inputs_parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.inputs_parameters</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:180</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>actionName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>parameters: <span class="tsd-signature-symbol">{ </span>inputs_parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.inputs_parameters</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5>inputs_<wbr>parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.inputs_parameters</span><span class="tsd-signature-symbol">[]</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5>selected_<wbr>list<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getactiondetail" class="tsd-anchor"></a>
					<h3>get<wbr>Action<wbr>Detail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Action<wbr>Detail<span class="tsd-signature-symbol">(</span>parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>intent<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ActionIntent</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilter</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>workflowExecuteContext<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.workflow2.WorkflowExecuteContext</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#info" class="tsd-signature-type">info</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:43</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>parameters: <span class="tsd-signature-symbol">{ </span>intent<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ActionIntent</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilter</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>workflowExecuteContext<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.workflow2.WorkflowExecuteContext</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> intent<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ActionIntent</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilter</span><span class="tsd-signature-symbol">[]</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5>selected_<wbr>list<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> workflow<wbr>Execute<wbr>Context<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.workflow2.WorkflowExecuteContext</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#info" class="tsd-signature-type">info</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getauthorslist" class="tsd-anchor"></a>
					<h3>get<wbr>Authors<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Authors<wbr>List<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#getauthorslistrequestresult" class="tsd-signature-type">getAuthorsListRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:119</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#getauthorslistrequestresult" class="tsd-signature-type">getAuthorsListRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getbatchexceltemplate" class="tsd-anchor"></a>
					<h3>get<wbr>Batch<wbr>Excel<wbr>Template</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Batch<wbr>Excel<wbr>Template<span class="tsd-signature-symbol">(</span>schemaName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:136</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>schemaName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getdatasource" class="tsd-anchor"></a>
					<h3>get<wbr>Data<wbr>Source</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Data<wbr>Source<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.getDataSourceRequestParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#getdatasourcerequestresult" class="tsd-signature-type">getDataSourceRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:125</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ActionTypes.getDataSourceRequestParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#getdatasourcerequestresult" class="tsd-signature-type">getDataSourceRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="unanonymous" class="tsd-anchor"></a>
					<h3>un<wbr>Anonymous</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">un<wbr>Anonymous<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="anonymousapi.html">AnonymousApi</a>.<a href="anonymousapi.html#unanonymous">unAnonymous</a></p>
								<ul>
									<li>Defined in src/core/anonymous-api.ts:7</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updatecontrolsproperties" class="tsd-anchor"></a>
					<h3>update<wbr>Controls<wbr>Properties</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Controls<wbr>Properties<span class="tsd-signature-symbol">(</span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-type">dto.ActionTypes.executeParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#updatecontrolspropertiesrequestresult" class="tsd-signature-type">updateControlsPropertiesRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:142</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>property: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>params: <span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-type">dto.ActionTypes.executeParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#updatecontrolspropertiesrequestresult" class="tsd-signature-type">updateControlsPropertiesRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="validatebatchaction" class="tsd-anchor"></a>
					<h3>validate<wbr>Batch<wbr>Action</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">validate<wbr>Batch<wbr>Action<span class="tsd-signature-symbol">(</span>parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.validateBatchParams</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#validaterequestresult" class="tsd-signature-type">validateRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:88</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>parameters: <span class="tsd-signature-type">dto.ActionTypes.validateBatchParams</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol"> }</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#validaterequestresult" class="tsd-signature-type">validateRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="validateinput" class="tsd-anchor"></a>
					<h3>validate<wbr>Input</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">validate<wbr>Input<span class="tsd-signature-symbol">(</span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-type">dto.ActionTypes.executeParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#validatorinputerresult" class="tsd-signature-type">ValidatorInputerResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/api.ts:160</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>property: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>parameters: <span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-type">dto.ActionTypes.executeParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#validatorinputerresult" class="tsd-signature-type">ValidatorInputerResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="actionapi.html" class="tsd-kind-icon">Action<wbr>Api</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class">
								<a href="actionapi.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="actionapi.html#action_name" class="tsd-kind-icon">action_<wbr>name</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-inherited">
								<a href="actionapi.html#isanonymous" class="tsd-kind-icon">is<wbr>Anonymous</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="actionapi.html#model_name" class="tsd-kind-icon">model_<wbr>name</a>
							</li>
							<li class=" tsd-kind-get-signature tsd-parent-kind-class tsd-is-inherited">
								<a href="actionapi.html#urlprefix" class="tsd-kind-icon">url<wbr>Prefix</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="actionapi.html#anonymous" class="tsd-kind-icon">anonymous</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="actionapi.html#bigactioncheck" class="tsd-kind-icon">big<wbr>Action<wbr>Check</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="actionapi.html#bigactiondetail" class="tsd-kind-icon">big<wbr>Action<wbr>Detail</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="actionapi.html#bigactionfileurl" class="tsd-kind-icon">big<wbr>Action<wbr>File<wbr>Url</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="actionapi.html#bigactionimport" class="tsd-kind-icon">big<wbr>Action<wbr>Import</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="actionapi.html#bigactionsignal" class="tsd-kind-icon">big<wbr>Action<wbr>Signal</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="actionapi.html#clearaction" class="tsd-kind-icon">clear<wbr>Action</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="actionapi.html#createeachactionsseurl" class="tsd-kind-icon">create<wbr>Each<wbr>ActionSSEURL</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="actionapi.html#executeaction" class="tsd-kind-icon">execute<wbr>Action</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="actionapi.html#executebatchaction" class="tsd-kind-icon">execute<wbr>Batch<wbr>Action</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="actionapi.html#executeinneraction" class="tsd-kind-icon">execute<wbr>Inner<wbr>Action</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="actionapi.html#getactiondetail" class="tsd-kind-icon">get<wbr>Action<wbr>Detail</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="actionapi.html#getauthorslist" class="tsd-kind-icon">get<wbr>Authors<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="actionapi.html#getbatchexceltemplate" class="tsd-kind-icon">get<wbr>Batch<wbr>Excel<wbr>Template</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="actionapi.html#getdatasource" class="tsd-kind-icon">get<wbr>Data<wbr>Source</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="actionapi.html#unanonymous" class="tsd-kind-icon">un<wbr>Anonymous</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="actionapi.html#updatecontrolsproperties" class="tsd-kind-icon">update<wbr>Controls<wbr>Properties</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="actionapi.html#validatebatchaction" class="tsd-kind-icon">validate<wbr>Batch<wbr>Action</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="actionapi.html#validateinput" class="tsd-kind-icon">validate<wbr>Input</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>