import axios from "../../core/axios/index"
import { encodeParams4Parameters } from "../../core/tools/form"
import type * as dto from "../../def/index"
type omittedProps = {
    ids: number[]
    tabName: string
}

export type AllFilters = {
    filters: dto.Tools.FiltersType
    tagFilters: dto.TagManagerTypes.TagFilter[]
    filters4Workflow: unknown
    prefilters: dto.prefilters
    order_obj: dto.orderObj
    page_index: number
    columns: string[]
}
export class WorkflowApi {
    constructor(private model_name: string) {}

    public getProcessInfo(params: dto.WorkflowTypes.getProcessInfoParams) {
        return axios.get<unknown>(
            `general/model/${
                this.model_name
            }/getProcessInfoById?${encodeParams4Parameters(params)}`
        )
    }

    public createWorkflow(
        params: dto.WorkflowTypes.createWorkflow &
            omittedProps & {
                allFilters: AllFilters
            }
    ) {
        return axios.get<string>(
            `general/model/${
                this.model_name
            }/createWorkflow?${encodeParams4Parameters(params)}`
        )
    }
    public updateWorkflow(
        params: dto.WorkflowTypes.updateWorkflow & omittedProps
    ) {
        return axios.get<string>(
            `general/model/${
                this.model_name
            }/updateWorkflow?${encodeParams4Parameters(params)}`
        )
    }
}
