import type * as dto from "../../def/index"

import { TreeApi, TreeApi2 } from "./api"

/**
 * @deprecated 请使用 Tree2
 */
export class Tree {
    private api: TreeApi

    constructor(modelName: string) {
        this.api = new TreeApi(modelName)
    }

    public getNodeAllAncestor<T>(node: string | number) {
        return this.api.getNodeAllAncestor<T>(node)
    }

    /**
     * 查询树，懒加载的方式
     * @param parent 父节点
     */
    public queryTreeLazyWithMetaV2(
        parent: string | number,
        listName: string,
        prefilters?: dto.prefilters
    ) {
        return this.api.queryTreeLazyWithMeta(parent, listName, prefilters)
    }

    /**
     * 查询树，懒加载的方式
     * @param parent 父节点
     */
    public queryTreeLazyWithMeta(parent: string | number) {
        return this.api.queryTreeLazyWithMeta(parent, null)
    }

    /**
     * 查询树，懒加载的方式
     * @param parent 父节点
     */
    public queryTreeLazy(parent: string | number, prefilters?: dto.prefilters) {
        return this.api.queryTreeLazy(parent, prefilters)
    }

    /**
     * 查询树，并携带业务数据
     * @param params 定义
     */
    public async queryTreeWithData(
        listTreeWithDataDef: dto.TreeTypes.ListTreeWithDataDef
    ) {
        return this.api.queryTreeWithData(listTreeWithDataDef)
    }

    /**
     * 查询所有树，并携带了树的元信息
     */
    public queryTreeWithMeta() {
        return this.api.queryTreeWithMeta()
    }

    /**
     * 查询所有的树
     */
    public queryAllTreeList(q: {
        searchKeyword: string
        parent?: string | number
        prefilters?: dto.prefilters
        listName?: string
        searchProperties: string[]
        depth?: number
    }) {
        return this.api.queryAllTreeList(q)
    }

    public queryTreeByRoot(
        node: string | number,
        summary: dto.TreeTypes.TreeSummary
    ) {
        return this.api.queryTreeByRoot(node, summary)
    }

    /**
     * 查询指定节点的信息
     * @param node 节点ID
     * @param parent true 查询当前节点的父节点信息 false 查询当前节点信息
     */
    public getNode(node: string | number, parent: boolean) {
        return this.api.getNode(node, parent)
    }
}

export class Tree2 {
    private api: TreeApi2

    constructor(modelName: string) {
        this.api = new TreeApi2(modelName)
    }

    public getNodeAllAncestor<T>(node: string | number) {
        return this.api.getNodeAllAncestor<T>(node)
    }

    /**
     * 查询树，懒加载的方式
     * @param parent 父节点
     */
    public queryTreeLazyWithMetaV2(
        parent: string | number,
        listName: string,
        prefilters?: dto.prefilters
    ) {
        return this.api.queryTreeLazyWithMeta(parent, listName, prefilters)
    }

    /**
     * 查询树，懒加载的方式
     * @param parent 父节点
     */
    public queryTreeLazyWithMeta(parent: string | number) {
        return this.api.queryTreeLazyWithMeta(parent, null)
    }

    /**
     * 查询树，懒加载的方式
     * @param parent 父节点
     */
    public queryTreeLazy(parent: string | number, prefilters?: dto.prefilters) {
        return this.api.queryTreeLazy(parent, prefilters)
    }

    /**
     * 查询树，并携带业务数据
     * @param params 定义
     */
    public async queryTreeWithData(
        listTreeWithDataDef: dto.TreeTypes.ListTreeWithDataDef
    ) {
        return this.api.queryTreeWithData(listTreeWithDataDef)
    }

    /**
     * 查询所有树，并携带了树的元信息
     */
    public queryTreeWithMeta() {
        return this.api.queryTreeWithMeta()
    }

    /**
     * 查询所有的树
     */
    public queryAllTreeList(q: {
        searchKeyword: string
        parent?: string | number
        prefilters?: dto.prefilters
        listName?: string
        searchProperties: string[]
        depth?: number
    }) {
        return this.api.queryAllTreeList(q)
    }

    public queryTreeByRoot(
        node: string | number,
        summary: dto.TreeTypes.TreeSummary
    ) {
        return this.api.queryTreeByRoot(node, summary)
    }

    /**
     * 查询指定节点的信息
     * @param node 节点ID
     * @param parent true 查询当前节点的父节点信息 false 查询当前节点信息
     */
    public getNode(node: string | number, parent: boolean) {
        return this.api.getNode(node, parent)
    }
}
