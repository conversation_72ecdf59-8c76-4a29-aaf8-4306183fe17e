import { Meta, prefilter, Tools, workflow2 } from "../../def/index"

export type WorkflowBatchParam = {
    type: string
    before: string
    after: string
}

export type WorkflowBatchRequest = {
    /**
     * 过滤器
     */
    filters?: Tools.FiltersType
    /**
     * 前置过滤器
     */
    prefilters?: prefilter[]
    workflowFilter: workflow2.WorkflowFilter
    top: number
    selectedList: Array<any>
    batchParam: WorkflowBatchParam
    listName: string
}

export type TaskDefaultDealerRequest = {
    processId: number
    /**
     * 任务
     */
    task: string
}

export type ChangeMasterRequest = {
    /**
     * 流程ID
     */
    processId: number
    /**
     * 负责人ID
     */
    master: number
}

export type WorkflowUser = {
    id: number
    name: string
}

export type WorkflowHandle = {
    label: string
    name: string
    handle: string
    type: string
}

export type ProcessConfigInfo = {
    processDef: workflow2.ProcessDef
    handles: Array<WorkflowHandle>
    groups: Array<ProcessGroup>
}

export type ProcessGroup = {
    name: string
    label: string
}

export type CreateProcessRequest = {
    modelName: string
    processName: string
    preHandle?: string
    description?: string
    cancellable: boolean
    editable: boolean
    changeable: boolean
    defaultMasterHandle?: string
    group?: string
}

export type EditProcessRequest = {
    modelName: string
    processName: string
    description?: string
    cancellable: boolean
    editable: boolean
    changeable: boolean
    groups?: Array<workflow2.StateGroup>
    states?: Array<workflow2.StateDef>
    tasks?: Array<workflow2.TaskDef>
    stateChangeCallback?: string
    defaultMasterHandle?: string
    preHandle?: string
    // 流程的分组
    group?: string
}

/**
 * 详情页相关的控制信息
 */
export type ControlInfo = {
    /**
     * true 处理人可以取消任务
     */
    cancellable: boolean
    /**
     * true 处理人可以编辑任务
     */
    editable: boolean
    /**
     * true 处理人可以更换任务
     */
    changeable: boolean
    /**
     * 详情是否为编辑状态
     */
    detailEditable: boolean
    /**
     * 流程终结时可以更换任务
     */
    stateChangeable: boolean
}

/**
 * 详情页相关的流程定义信息
 */
export type DefInfo = {
    /**
     * 所有的状态的 map
     */
    stateMap: Map<string, workflow2.StateDef>
    /**
     * 所有任务的 map
     */
    taskMap: Map<string, workflow2.TaskDef>
    /**
     * 所有的终结状态
     */
    endStates: Set<string>
    /**
     * 状态分组
     */
    groups: Array<workflow2.StateGroup>
}

/**
 * 流程详情的基本信息
 */
export type BasicInfo = {
    id: number
    /**
     * 关联对象ID
     */
    associateId: number
    /**
     * 模型名称
     */
    modelName: string
    /**
     * 流程名称
     */
    processName: string
    /**
     * 当前状态
     */
    state: string
    /**
     * 当前任务，可能无
     */
    task: string
    /**
     * 摘要字段
     */
    description: string
    /**
     * 当前流程负责人，可能没有
     */
    master?: WorkflowUser
    /**
     * 当前流程创建人
     */
    creator?: WorkflowUser
    /**
     * 当前流程处理人，可能没有
     */
    dealer?: WorkflowUser
    /**
     * 创建时间
     */
    createTime: Date
    /**
     * true 说明流程已终结，否则就是运行中
     */
    finish: boolean
    /**
     * 任务开始时间
     */
    taskBeginTime: Date
    /**
     * 任务结束时间
     */
    taskEndTime: Date
    summary: string
    [key: string]: unknown
}

/**
 * 流程关联的数据模型的数据
 */
export type AssociateInfo = Meta & {
    /**
     * 数据模型的具体数据
     */
    objects: Array<WorkObject>
    /**
     * 标题
     */
    title: string
    /**
     * 数据模型的行为
     */
    actions: any[]
    /**
     * 数据模型的版本字段
     */
    uniplatVersion: any
    [key: string]: unknown
}

export type AssociateInfo2 = Meta & {
    /**
     * 数据模型的具体数据
     */
    objects: Array<WorkObject>
    /**
     * 标题
     */
    title: string
    /**
     * 数据模型的行为
     */
    actions: any[]
    row: {
        keyValue: string | number
        objectData: { [key: string]: unknown }
        uniplatVersion: number
    }
    [key: string]: unknown
}

/**
 * 数据模型的数据
 */
export type WorkObject = {
    /**
     * label
     */
    label: string
    /**
     * 显示字段
     */
    display: string
    /**
     * 其他信息
     */
    fieldGroup?: unknown
}

/**
 * 详情中流程任务变更的信息
 */
export type TaskChangeInfo = {
    /**
     * 任务完成前状态
     */
    state: string
    /**
     * 任务完成后状态，未完成的任务没有
     */
    finishState: string
    /**
     * 任务
     */
    task: string
    /**
     * true 已取消
     */
    cancel: boolean
    /**
     * 任务的创建日期
     */
    createTimeStr: string
    /**
     * 剩余时间的毫秒
     * < 0 已超时
     * = 0 未设置
     * > 0 计算时间，例如剩余 1 天
     */
    remainMillisecond: number
    /**
     * true 为当前任务
     */
    current: boolean
}

/**
 * 详情页左右滑动的任务 banner 数据
 */
export type TaskChangeBanner = {
    /**
     * true 当前存在任务
     */
    hasTaskNow: boolean
    /**
     * 任务全记录
     */
    tasks: Array<TaskChangeInfo>
}

type BaseProcessDetail = {
    /**
     * 流程的基本信息
     */
    ProcessBasicInfo: BasicInfo
    basicInfo: BasicInfo
    /**
     * 流程定义相关信息
     */
    defInfo: DefInfo
    /**
     * 控制相关的信息
     */
    control: ControlInfo
    /**
     * 详情页 banner 数据
     */
    taskChangeBanner: TaskChangeBanner
    /**
     * 备注相关信息
     */
    remarks: Array<workflow2.Remark>
    /**
     * 任务记录相关信息
     */
    workRecords: Array<workflow2.WorkRecord>
    /**
     * 操作日志相关信息
     */
    operations: Array<workflow2.ProcessOperation>
}

/**
 * 流程的基本信息
 */
export type ProcessDetail2 = BaseProcessDetail & {
    /**
     * 关联的数据信息
     */
    associateInfo: AssociateInfo
}

export type ProcessDetail3 = BaseProcessDetail & {
    /**
     * 关联的数据信息
     */
    associateInfo: AssociateInfo2
}
