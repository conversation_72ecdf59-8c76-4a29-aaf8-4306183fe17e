import axios from "../../core/axios/index"
import type * as dto from "../../def/index"

export default class TemplateManagerApi {
    constructor(private config: dto.TemplateManagerTypes.apiConstructor) {}

    public query() {
        const params = {
            filters: {
                text_filters: [
                    {
                        property: "project_name",
                        status: this.config.subProjectName,
                        match: "exact",
                    },
                    {
                        property: "model_name",
                        status: this.config.model_name,
                        match: "exact",
                    },
                    {
                        property: "target_name",
                        status: this.config.targetName,
                        match: "exact",
                    },
                ],
                enum_filters: [
                    { property: "type", status: [this.config.templateType] },
                ],
            },
            item_index: 0,
            item_size: 100,
            columns: ["*"],
        }
        return axios.post<dto.TemplateManagerTypes.queryApiResult>(
            `general/model/export_template/list2`,
            params
        )
    }
    public add(params: dto.TemplateManagerTypes.addApiParams) {
        const postData = {
            inputs_parameters: [
                { property: "name", value: params.name },
                { property: "url", value: params.url },
                { property: "page_name", value: params.pageName },
                { property: "file_name_template", value: params.fileName },
                { property: "project_name", value: this.config.subProjectName },
                { property: "type", value: this.config.templateType },
                { property: "model_name", value: this.config.model_name },
                { property: "target_name", value: this.config.targetName },
            ],
            dataDetails: [],
            version_control: true,
            selected_list: [],
        }
        return axios.post<void>(
            `general/model/export_template/action2/add_template/execute`,
            postData
        )
    }
    public edit(params: dto.TemplateManagerTypes.editApiParams) {
        const postData = {
            inputs_parameters: [
                { property: "name", value: params.name },
                { property: "url", value: params.url },
                { property: "page_name", value: params.pageName },
                { property: "file_name_template", value: params.fileName },
            ],
            dataDetails: [],
            version_control: true,
            selected_list: [{ v: params.uniplat_version, id: params.id }],
        }
        return axios.post<void>(
            `general/model/export_template/action2/edit_template/execute`,
            postData
        )
    }

    public del(params: dto.TemplateManagerTypes.delApiParams) {
        const postData = {
            inputs_parameters: [{ property: "a", value: "" }],
            dataDetails: [],
            version_control: true,
            selected_list: [{ v: params.uniplat_version, id: params.id }],
        }
        return axios.post<void>(
            `general/model/export_template/action2/delete/execute`,
            postData
        )
    }
}
