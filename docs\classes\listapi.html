<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>listApi | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="listapi.html">listApi</a>
				</li>
			</ul>
			<h1>Class listApi</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<a href="anonymousapi.html" class="tsd-signature-type">AnonymousApi</a>
						<ul class="tsd-hierarchy">
							<li>
								<span class="target">listApi</span>
							</li>
						</ul>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="listapi.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="listapi.html#isanonymous" class="tsd-kind-icon">is<wbr>Anonymous</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="listapi.html#model_name" class="tsd-kind-icon">model_<wbr>name</a></li>
							</ul>
						</section>
						<section class="tsd-index-section tsd-is-inherited">
							<h3>Accessors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-get-signature tsd-parent-kind-class tsd-is-inherited"><a href="listapi.html#urlprefix" class="tsd-kind-icon">url<wbr>Prefix</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listapi.html#anonymous" class="tsd-kind-icon">anonymous</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listapi.html#createexporturl" class="tsd-kind-icon">create<wbr>Export<wbr>Url</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listapi.html#createexporturl2word" class="tsd-kind-icon">create<wbr>Export<wbr>Url2<wbr>Word</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listapi.html#createexporturlv2" class="tsd-kind-icon">create<wbr>Export<wbr>Url<wbr>V2</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listapi.html#createexporturlv2forcsv" class="tsd-kind-icon">create<wbr>Export<wbr>Url<wbr>V2For<wbr>Csv</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listapi.html#getalldefaulttemplateurl" class="tsd-kind-icon">get<wbr>All<wbr>Default<wbr>Template<wbr>Url</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listapi.html#getdefaulttemplateurl" class="tsd-kind-icon">get<wbr>Default<wbr>Template<wbr>Url</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="listapi.html#getlistdata" class="tsd-kind-icon">get<wbr>List<wbr>Data</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="listapi.html#getlistdatabytab" class="tsd-kind-icon">get<wbr>List<wbr>Data<wbr>ByTab</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="listapi.html#getlistdataofsinglepage" class="tsd-kind-icon">get<wbr>List<wbr>Data<wbr>OfSingle<wbr>Page</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="listapi.html#getlistmeta" class="tsd-kind-icon">get<wbr>List<wbr>Meta</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listapi.html#getpagerecordcount" class="tsd-kind-icon">get<wbr>Page<wbr>Record<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listapi.html#getpagesmeta" class="tsd-kind-icon">get<wbr>Pages<wbr>Meta</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listapi.html#getrowdetail" class="tsd-kind-icon">get<wbr>Row<wbr>Detail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listapi.html#getfiltergroupdetail" class="tsd-kind-icon">getfilter<wbr>Group<wbr>Detail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listapi.html#postparam4excel" class="tsd-kind-icon">post<wbr>Param4<wbr>Excel</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listapi.html#unanonymous" class="tsd-kind-icon">un<wbr>Anonymous</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listapi.html#updateaction" class="tsd-kind-icon">update<wbr>Action</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listapi.html#updatefilterparam" class="tsd-kind-icon">update<wbr>Filter<wbr>Param</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new list<wbr>Api<span class="tsd-signature-symbol">(</span>model_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="listapi.html" class="tsd-signature-type">listApi</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:6</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>model_name: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="listapi.html" class="tsd-signature-type">listApi</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited">
					<a name="isanonymous" class="tsd-anchor"></a>
					<h3>is<wbr>Anonymous</h3>
					<div class="tsd-signature tsd-kind-icon">is<wbr>Anonymous<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
						<p>Inherited from <a href="anonymousapi.html">AnonymousApi</a>.<a href="anonymousapi.html#isanonymous">isAnonymous</a></p>
						<ul>
							<li>Defined in src/core/anonymous-api.ts:2</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="model_name" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> model_<wbr>name</h3>
					<div class="tsd-signature tsd-kind-icon">model_<wbr>name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/api.ts:7</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group tsd-is-inherited">
				<h2>Accessors</h2>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class tsd-is-inherited">
					<a name="urlprefix" class="tsd-anchor"></a>
					<h3>url<wbr>Prefix</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> urlPrefix<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"public"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"general"</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="anonymousapi.html">AnonymousApi</a>.<a href="anonymousapi.html#urlprefix">urlPrefix</a></p>
								<ul>
									<li>Defined in src/core/anonymous-api.ts:12</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">"public"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"general"</span></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="anonymous" class="tsd-anchor"></a>
					<h3>anonymous</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">anonymous<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="anonymousapi.html">AnonymousApi</a>.<a href="anonymousapi.html#anonymous">anonymous</a></p>
								<ul>
									<li>Defined in src/core/anonymous-api.ts:3</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="createexporturl" class="tsd-anchor"></a>
					<h3>create<wbr>Export<wbr>Url</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">create<wbr>Export<wbr>Url<span class="tsd-signature-symbol">(</span>parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.exportToExcel</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:84</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>parameters: <span class="tsd-signature-type">dto.ListTypes.exportToExcel</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="createexporturl2word" class="tsd-anchor"></a>
					<h3>create<wbr>Export<wbr>Url2<wbr>Word</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">create<wbr>Export<wbr>Url2<wbr>Word<span class="tsd-signature-symbol">(</span>eid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:94</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eid: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="createexporturlv2" class="tsd-anchor"></a>
					<h3>create<wbr>Export<wbr>Url<wbr>V2</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">create<wbr>Export<wbr>Url<wbr>V2<span class="tsd-signature-symbol">(</span>eid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:90</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eid: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="createexporturlv2forcsv" class="tsd-anchor"></a>
					<h3>create<wbr>Export<wbr>Url<wbr>V2For<wbr>Csv</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">create<wbr>Export<wbr>Url<wbr>V2For<wbr>Csv<span class="tsd-signature-symbol">(</span>eid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:98</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eid: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getalldefaulttemplateurl" class="tsd-anchor"></a>
					<h3>get<wbr>All<wbr>Default<wbr>Template<wbr>Url</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>All<wbr>Default<wbr>Template<wbr>Url<span class="tsd-signature-symbol">(</span>list_name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:119</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> list_name: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getdefaulttemplateurl" class="tsd-anchor"></a>
					<h3>get<wbr>Default<wbr>Template<wbr>Url</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Default<wbr>Template<wbr>Url<span class="tsd-signature-symbol">(</span>list_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">undefined</span>, page_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:102</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>list_name: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">undefined</span></h5>
								</li>
								<li>
									<h5>page_name: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="getlistdata" class="tsd-anchor"></a>
					<h3>get<wbr>List<wbr>Data</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">get<wbr>List<wbr>Data&lt;RowType&gt;<span class="tsd-signature-symbol">(</span>parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.initProps</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdatarequestresult" class="tsd-signature-type">getListDataRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:11</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>RowType<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListRow</span> = <span class="tsd-signature-type">dto.ListRow</span></h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>parameters: <span class="tsd-signature-type">dto.ListTypes.initProps</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdatarequestresult" class="tsd-signature-type">getListDataRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="getlistdatabytab" class="tsd-anchor"></a>
					<h3>get<wbr>List<wbr>Data<wbr>ByTab</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">get<wbr>List<wbr>Data<wbr>ByTab&lt;RowType&gt;<span class="tsd-signature-symbol">(</span>tabName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.getListDataByTabParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdatabytabrequestresult" class="tsd-signature-type">getListDataByTabRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:37</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>RowType<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListRow</span> = <span class="tsd-signature-type">dto.ListRow</span></h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>tabName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>parameters: <span class="tsd-signature-type">dto.ListTypes.getListDataByTabParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdatabytabrequestresult" class="tsd-signature-type">getListDataByTabRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="getlistdataofsinglepage" class="tsd-anchor"></a>
					<h3>get<wbr>List<wbr>Data<wbr>OfSingle<wbr>Page</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">get<wbr>List<wbr>Data<wbr>OfSingle<wbr>Page&lt;RowType&gt;<span class="tsd-signature-symbol">(</span>parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.getListDataOfSinglePageParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdataofsinglepagerequestresult" class="tsd-signature-type">getListDataOfSinglePageRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:51</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>RowType<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListRow</span> = <span class="tsd-signature-type">dto.ListRow</span></h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>parameters: <span class="tsd-signature-type">dto.ListTypes.getListDataOfSinglePageParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdataofsinglepagerequestresult" class="tsd-signature-type">getListDataOfSinglePageRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="getlistmeta" class="tsd-anchor"></a>
					<h3>get<wbr>List<wbr>Meta</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">get<wbr>List<wbr>Meta&lt;RowType&gt;<span class="tsd-signature-symbol">(</span>parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.initProps</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdatarequestresult" class="tsd-signature-type">getListDataRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:24</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>RowType<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListRow</span> = <span class="tsd-signature-type">dto.ListRow</span></h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>parameters: <span class="tsd-signature-type">dto.ListTypes.initProps</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdatarequestresult" class="tsd-signature-type">getListDataRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getpagerecordcount" class="tsd-anchor"></a>
					<h3>get<wbr>Page<wbr>Record<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Page<wbr>Record<wbr>Count<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.getListDataByTabParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>page_datas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:167</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.getListDataByTabParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>page_datas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getpagesmeta" class="tsd-anchor"></a>
					<h3>get<wbr>Pages<wbr>Meta</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Pages<wbr>Meta<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.setColumnsForPagesParams</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>list_name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#setcolumnsforpagesapiresult" class="tsd-signature-type">setColumnsForPagesApiResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:147</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.setColumnsForPagesParams</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>list_name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol"> }</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#setcolumnsforpagesapiresult" class="tsd-signature-type">setColumnsForPagesApiResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getrowdetail" class="tsd-anchor"></a>
					<h3>get<wbr>Row<wbr>Detail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Row<wbr>Detail<span class="tsd-signature-symbol">(</span>keyFieldValue<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:78</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>keyFieldValue: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getfiltergroupdetail" class="tsd-anchor"></a>
					<h3>getfilter<wbr>Group<wbr>Detail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">getfilter<wbr>Group<wbr>Detail<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>group<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#filtergroup" class="tsd-signature-type">filterGroup</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:123</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-symbol">{ </span>group<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5>group<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#filtergroup" class="tsd-signature-type">filterGroup</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="postparam4excel" class="tsd-anchor"></a>
					<h3>post<wbr>Param4<wbr>Excel</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">post<wbr>Param4<wbr>Excel<span class="tsd-signature-symbol">(</span>parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.exportToExcel</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:161</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>parameters: <span class="tsd-signature-type">dto.ListTypes.exportToExcel</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="unanonymous" class="tsd-anchor"></a>
					<h3>un<wbr>Anonymous</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">un<wbr>Anonymous<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="anonymousapi.html">AnonymousApi</a>.<a href="anonymousapi.html#unanonymous">unAnonymous</a></p>
								<ul>
									<li>Defined in src/core/anonymous-api.ts:7</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updateaction" class="tsd-anchor"></a>
					<h3>update<wbr>Action</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Action<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.updateAction</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#updateactionrequestresult" class="tsd-signature-type">updateActionRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:131</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.updateAction</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol"> }</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#updateactionrequestresult" class="tsd-signature-type">updateActionRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updatefilterparam" class="tsd-anchor"></a>
					<h3>update<wbr>Filter<wbr>Param</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Filter<wbr>Param<span class="tsd-signature-symbol">(</span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>filters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.Tools.FiltersType</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.ListTypes.queryProps</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">"filters"</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#updatefilterparamrequestresult" class="tsd-signature-type">updateFilterParamRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/api.ts:64</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>property: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>parameters: <span class="tsd-signature-symbol">{ </span>filters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.Tools.FiltersType</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-type">Omit</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.ListTypes.queryProps</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">"filters"</span><span class="tsd-signature-symbol">&gt;</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#updatefilterparamrequestresult" class="tsd-signature-type">updateFilterParamRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="listapi.html" class="tsd-kind-icon">list<wbr>Api</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class">
								<a href="listapi.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-inherited">
								<a href="listapi.html#isanonymous" class="tsd-kind-icon">is<wbr>Anonymous</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="listapi.html#model_name" class="tsd-kind-icon">model_<wbr>name</a>
							</li>
							<li class=" tsd-kind-get-signature tsd-parent-kind-class tsd-is-inherited">
								<a href="listapi.html#urlprefix" class="tsd-kind-icon">url<wbr>Prefix</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listapi.html#anonymous" class="tsd-kind-icon">anonymous</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listapi.html#createexporturl" class="tsd-kind-icon">create<wbr>Export<wbr>Url</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listapi.html#createexporturl2word" class="tsd-kind-icon">create<wbr>Export<wbr>Url2<wbr>Word</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listapi.html#createexporturlv2" class="tsd-kind-icon">create<wbr>Export<wbr>Url<wbr>V2</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listapi.html#createexporturlv2forcsv" class="tsd-kind-icon">create<wbr>Export<wbr>Url<wbr>V2For<wbr>Csv</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listapi.html#getalldefaulttemplateurl" class="tsd-kind-icon">get<wbr>All<wbr>Default<wbr>Template<wbr>Url</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listapi.html#getdefaulttemplateurl" class="tsd-kind-icon">get<wbr>Default<wbr>Template<wbr>Url</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="listapi.html#getlistdata" class="tsd-kind-icon">get<wbr>List<wbr>Data</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="listapi.html#getlistdatabytab" class="tsd-kind-icon">get<wbr>List<wbr>Data<wbr>ByTab</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="listapi.html#getlistdataofsinglepage" class="tsd-kind-icon">get<wbr>List<wbr>Data<wbr>OfSingle<wbr>Page</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="listapi.html#getlistmeta" class="tsd-kind-icon">get<wbr>List<wbr>Meta</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listapi.html#getpagerecordcount" class="tsd-kind-icon">get<wbr>Page<wbr>Record<wbr>Count</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listapi.html#getpagesmeta" class="tsd-kind-icon">get<wbr>Pages<wbr>Meta</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listapi.html#getrowdetail" class="tsd-kind-icon">get<wbr>Row<wbr>Detail</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listapi.html#getfiltergroupdetail" class="tsd-kind-icon">getfilter<wbr>Group<wbr>Detail</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listapi.html#postparam4excel" class="tsd-kind-icon">post<wbr>Param4<wbr>Excel</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listapi.html#unanonymous" class="tsd-kind-icon">un<wbr>Anonymous</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listapi.html#updateaction" class="tsd-kind-icon">update<wbr>Action</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listapi.html#updatefilterparam" class="tsd-kind-icon">update<wbr>Filter<wbr>Param</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>