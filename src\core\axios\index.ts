import { AxiosInstance } from "axios"

import { axiosFactory } from "./axios"

const axios = {
    get<T>(...params: Parameters<AxiosInstance["get"]>): Promise<T> {
        const instance = axiosFactory.getAxios()
        return instance.get.apply(instance, params)
    },
    post<T>(...params: Parameters<AxiosInstance["post"]>): Promise<T> {
        const instance = axiosFactory.getAxios()
        return instance.post.apply(instance, params)
    },
    request<T>(...params: Parameters<AxiosInstance["request"]>): Promise<T> {
        const instance = axiosFactory.getAxios()
        return instance.request.apply(instance, params)
    },
}

export default axios
