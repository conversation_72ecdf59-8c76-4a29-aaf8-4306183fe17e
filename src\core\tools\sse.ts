/* eslint-disable @typescript-eslint/no-explicit-any */
import { EventSourcePolyfill } from "event-source-polyfill"

import { global } from "../../core/global"
import type * as dto from "../../def/index"

export function sseWorker<Msg, Result>(
    url: string,
    listeners: {
        onOpen?: (
            res: dto.PromiseFunc<Result>,
            rej: dto.PromiseFunc<string>
        ) => void
        onError?: (
            res: dto.PromiseFunc<Result>,
            rej: dto.PromiseFunc<string>
        ) => void
        onMsg?: (
            e: Msg,
            res: dto.PromiseFunc<Result>,
            rej: dto.PromiseFunc<string>
        ) => void
    },
    headers?: { CurrentOrg?: string; Entrance?: string }
) {
    let done: dto.PromiseFunc<Result> = () => {
        throw new Error("executeEach出错")
    }
    let failed: dto.PromiseFunc<string> = () => {
        throw new Error("executeEach出错")
    }
    const awaiting = new Promise<Result>((resolve, reject) => {
        done = resolve
        failed = reject
    })
    const sse = new EventSourcePolyfill(url, {
        headers: {
            Authorization: global.getCurrentToken(),
            Entrance: encodeURIComponent(global.rootEntrance),
            CurrentOrg: global.initData.orgId ?? 0,
            Scenes: JSON.stringify(global.initData.scenes ?? []),
            ...(headers || {}),
        },
    })

    sse.addEventListener("error", () => {
        sse.close()
        listeners.onError && listeners.onError(done, failed)
    })
    sse.addEventListener(
        "open",
        () => listeners.onOpen && listeners.onOpen(done, failed)
    )
    sse.addEventListener(
        "message",
        (e: Msg) => listeners.onMsg && listeners.onMsg(e, done, failed)
    )
    return {
        awaiting,
        close: () => sse.close(),
    }
}

function exportToExcel(
    url: string,
    headers?: { CurrentOrg?: string; Entrance?: string }
) {
    return (
        onProgress: (percent: number, status: string) => void = () => null
    ) => {
        let percent = 0
        type PromiseFunc = dto.PromiseFunc<string>
        const onError = (_done: PromiseFunc, failed: PromiseFunc) => {
            if (percent < 100) {
                failed("导出失败，连接已关闭")
            }
        }
        type Msg = { data: string }
        const onMsg = (e: Msg, done: PromiseFunc, failed: PromiseFunc) => {
            const data = JSON.parse(e.data)
            if (data.type === "heartbeat") {
                return
            }
            if (data.err) {
                failed(data.err)
            } else if (data.url) {
                onProgress(100, "完成")
                done(data.url)
            } else {
                const percentage = (percent = Number(data.percentage))
                const status = data.status
                onProgress(percentage, status)
            }
        }
        return sseWorker<Msg, string>(
            url,
            {
                onError,
                onMsg,
            },
            headers
        )
    }
}

function exportToReportExcel<T>(
    url: string,
    headers?: { CurrentOrg?: string; Entrance?: string }
) {
    return (
        onProgress: (percent: number, status: string) => void = () => null
    ) => {
        let percent = 0
        type PromiseFunc = dto.PromiseFunc<T>
        const onError = (
            _done: PromiseFunc,
            failed: dto.PromiseFunc<string>
        ) => {
            if (percent < 100) {
                failed("导出失败，连接已关闭")
            }
        }
        type Msg = { data: string }
        const onMsg = (
            e: Msg,
            done: PromiseFunc,
            failed: dto.PromiseFunc<string>
        ) => {
            const data = JSON.parse(e.data)
            if (data.type === "heartbeat") {
                return
            }
            if (data.err) {
                failed(data.err)
            } else if (data.url) {
                onProgress(100, "完成")
                done(data)
            } else {
                const percentage = (percent = Number(data.percentage))
                const status = data.status
                onProgress(percentage, status)
            }
        }
        return sseWorker<Msg, T>(
            url,
            {
                onError,
                onMsg,
            },
            headers
        )
    }
}

//for readability
export const exportToExcelForList = exportToExcel
export const exportToExcelForDetail = exportToExcel
export const exportToExcelForReport = exportToReportExcel

function uppperFirstChar(name: string) {
    return `${name[0].toUpperCase()}${name.substring(1)}`
}
export function paramHandler(target: any, name: string) {
    target[name] = []
    const funcName = uppperFirstChar(name).replace(/s$/, "")
    target[`add${funcName}`] = function (item: any) {
        this[name].push(item)
        return this
    }
    target[`clear${funcName}`] = function () {
        this[name] = []
    }
    return target
}
