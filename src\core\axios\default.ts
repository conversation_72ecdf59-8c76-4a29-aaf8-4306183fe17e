import axios, { AxiosAdapter, <PERSON>xiosInstance, AxiosResponse } from "axios"

import { parse, rebuildAxiosConfig } from "../../helpers/crypto"
import { global } from "../global"
import { isInUniApp } from "../in-uniapp"

import { rebuildToken } from "./builder"

let inner: AxiosInstance | null = null

const builder = {
    init: (config: { adapter?: AxiosAdapter; timeout?: number } = {}) => {
        const baseURL = global.baseUrl
        const option = { baseURL }
        !isInUniApp() &&
            Object.assign(option, { timeout: config.timeout || 60e3 })
        inner = axios.create(option)
        if (config.adapter) {
            inner.defaults.adapter = config.adapter as AxiosAdapter
        }
        inner.interceptors.request.use(
            (config) => {
                rebuildToken(config)
                return rebuildAxiosConfig(config)
            },
            (error) => Promise.reject(error)
        ),
            inner.interceptors.response.use((r) => {
                const p = parse(r.data) as {
                    rescode?: number
                    data: unknown
                    msg?: string
                    message?: string
                }
                return Promise.resolve({ data: p } as AxiosResponse)
            })
    },
    instance: () => inner,
}

export { builder as defaultAxiosBuilder }
