import type * as dto from "../../def/index"

import TemplateManagerApi from "./api"
export class TemplateManager {
    private api: TemplateManagerApi
    constructor(config: dto.TemplateManagerTypes.apiConstructor) {
        this.api = new TemplateManagerApi(config)
    }

    public query() {
        return this.api.query()
    }

    public add(params: dto.TemplateManagerTypes.addApiParams) {
        return this.api.add(params)
    }

    public edit(params: dto.TemplateManagerTypes.editApiParams) {
        return this.api.edit(params)
    }

    public del(params: dto.TemplateManagerTypes.delApiParams) {
        return this.api.del(params)
    }
}
