import { encodePayload, isParameterEncode } from "../../helpers/crypto"

export function encodeParams4Parameters<T>(data: T, encodeUrl = false) {
    if (isParameterEncode() && !encodeUrl) {
        // 如果开启了参数加解密，则不进行URL encode(queryName为parameters)，否则加密会导致出现解析问题
        return `parameters=${JSON.stringify(data)}`
    }
    return `parameters=${encodeURIComponent(JSON.stringify(data))}`
}

export function encodeParams<T>(data: T) {
    return encodeURIComponent(JSON.stringify(data))
}

export function encodeParamsWithCrypto<T>(data: T) {
    if (isParameterEncode()) {
        const s = JSON.stringify(data)
        return encodePayload(s) as string
    }
    return encodeParams(data)
}
