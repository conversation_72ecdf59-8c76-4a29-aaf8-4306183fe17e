import { events } from "../../core/events"
import type * as dto from "../../def/index"
import { Action } from "../action/action"
import { List } from "../list/list"

import { RoleApi } from "./role-api"

export class Role {
    private callbackOnChange?: dto.SSE.callBackOfModelUpdated
    private api: RoleApi
    constructor() {
        this.api = new RoleApi()
    }
    /**
     * 查询角色列表
     * */
    public async query() {
        const data = await this.api.query()
        return data
    }

    /**
     * 查询拥有指定角色的用户
     * @param roleId
     *
     */
    public getAuthUsers(roleId: number) {
        return this.api.getAuthUsers(roleId)
    }
    /**
     * 剥夺指定用户的所有权限
     * @param userId
     */
    public depriveUserRights(userId: number) {
        return this.api.depriveUserRights(userId)
    }
    /**
     * 获取选中角色版本
     * @param userId
     */
    public getRoleVersion(roleId: number) {
        return this.api.getRoleVersion(roleId)
    }
    /**
     * 获取选中角色用户版本
     * @param userId
     */
    public getRoleUserVersion(userId: number) {
        return this.api.getRoleUserVersion(userId)
    }
    /**
     * 获取xid对应角色列表
     * @param xid
     */
    public getRolesByXid(xid: number) {
        return this.api.getRoleTreeByXid(xid)
    }
    /**
     * 更新一个用户
     * @param userId
     * @param roleId
     * @returns 返回一个Action和用在Action中的selected_list， prefilters
     */
    public async updateRoleUser(userId: number, roleId: number) {
        const data = await this.api.getRoleUserVersion(userId)
        const uniplatVerson = data.row.uniplat_version.value
        const action = new Action({
            model_name: "roleuser",
            action_name: "update",
        })
        const selected_list = [{ v: uniplatVerson, id: userId }]
        const prefilters = [{ property: "role_id", value: roleId }]
        return {
            action,
            selected_list,
            prefilters,
        }
    }
    /**
     * 给指定角色添加一个用户
     * @param roleId
     * @returns 返回一个Action和用在Action中的prefilters
     */
    public addNewUser(roleId: number) {
        const action = new Action({
            model_name: "roleuser",
            action_name: "insert",
        })
        const prefilters = [{ property: "role_id", value: roleId }]
        return {
            action,
            prefilters,
        }
    }

    /**
     * 查询权限全量数据，结果是一个树，可以直接用
     * @deprecated 权限改造
     */
    public getGlobalRightsTree() {
        return this.api.getGlobalRightsTree()
    }

    /**
     * 新查询权限全量数据，结果是一个树，可以直接用
     *  权限改造
     */
    public getNewGlobalRightsTree(xid: number) {
        return this.api.getNewGlobalRightsTree(xid)
    }

    /**
     * 修改指定角色的权限
     * @deprecated 权限改造
     * @param params
     */
    public async saveRightsChange(
        params: dto.RoleTypes.SaveRightsChangeParams
    ) {
        const data = await this.api.getRoleVersion(params.role_id)
        const uniplatVerson = data.row.uniplat_version.value
        const selected_list = [{ v: uniplatVerson, id: params.role_id }]
        return await new Action({
            model_name: "role",
            action_name: "update_role_entry",
        })
            .updateInitialParams({ selected_list })
            .addInputs_parameter({
                add_entrys: JSON.stringify(params.add_entrys || []),
                remove_entrys: JSON.stringify(params.remove_entrys || []),
            })
            .execute()
    }
    /**
     * 获取角色已有权限
     * @deprecated 权限改造
     * @param roleId
     */
    public getRoleRights(roleId: number) {
        return this.api.getRoleRights(roleId)
    }

    /**
     * 获取角色已有权限
     * @param roleId
     */
    public getNewRoleRights(roleId: number) {
        return this.api.getNewRoleRights(roleId)
    }

    /**
     * 操作数据权限列表
     * @return List类
     */
    public getRightsTable() {
        return new List({
            model_name: "role_dataright",
        })
    }

    public getNewRightsTable() {
        return new List({
            model_name: "uniplat_roledataright",
        })
    }

    /** @deprecated 权限改造 */
    public getModelEntries(model_name: string) {
        return this.api.getModelEntries(model_name)
    }

    public getDataSourceList() {
        return this.api.getDataSourceList()
    }

    public getNewDataSourceList() {
        return this.api.getNewDataSourceList()
    }

    public getEntryList(model_name?: string) {
        return this.api.getEntryList(model_name)
    }

    public getNewEntryList(xid: number, model_name?: string) {
        return this.api.getNewEntryList(xid, model_name)
    }

    public getRoleEntryRights(roleId: number) {
        return this.api.getRoleEntryRights(roleId)
    }

    public getNewRoleEntryRights(roleId: number) {
        return this.api.getNewRoleEntryRights(roleId)
    }

    public async updateRoleEntryRight(
        params: dto.RoleTypes.UpdateRoleEntryRightParams
    ) {
        return await new Action({
            model_name: "role_entry_right",
            action_name: "update_role_entry_right",
        })
            .addInputs_parameter({
                role_id: params.role_id,
                add_entries: JSON.stringify(params.add_entries || []),
                remove_entries: JSON.stringify(params.remove_entries || []),
            })
            .execute()
    }

    public async updateNewRoleEntryRight(
        params: dto.RoleTypes.UpdateRoleEntryRightParams
    ) {
        return await new Action({
            model_name: "uniplat_roleentryright",
            action_name: "update_role_entry_right",
        })
            .addInputs_parameter({
                role_id: params.role_id,
                add_entries: JSON.stringify(params.add_entries || []),
                remove_entries: JSON.stringify(params.remove_entries || []),
                entry_type: params.entry_type,
            })
            .execute()
    }

    private onTransportMessage = (msg: dto.SSE.msg) => {
        const models = msg.dataUpdates.filter((x) =>
            ["role", "roleuser"].includes(x.model)
        )
        if (models.length === 0) return
        this.callbackOnChange && this.callbackOnChange(msg.createByMyself)
    }
    /**
     * 注册当前模型的数据变化时的回掉
     * @param CallbackOfModelUpdatedOfDashboad
     */
    public registerOnChange(cb: dto.SSE.callBackOfModelUpdated) {
        this.callbackOnChange = cb
        return events.addTransportMessageListener(this.onTransportMessage)
    }
}
