import { global } from "../../core/global"
import type * as dto from "../../def/index"

import * as api from "./api"
export class OAuthLogin {
    public async teammixOauthLogin() {
        const data = await api.teammixOauthLogin()
        global.username = data.username
        global.jwtToken = data.jwt
        global.rootEntrance = "小站管理"
        return data
    }
    public async hrsOauthLogin(
        params: dto.OAuthLoginTypes.hrsOauthLoginApiParams
    ) {
        const data = await api.hrsOauthLogin(params)
        global.username = data.username
        global.jwtToken = data.jwt
        global.rootEntrance = "薪酬企业平台"
        return data
    }
    public initCompanyAuthLoginData(orgId: string) {
        return api.initCompanyAuthLoginData(orgId)
    }
}
