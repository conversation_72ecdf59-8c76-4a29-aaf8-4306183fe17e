/**
 * 这些Url请求时不需要带上场景值信息，否则反而会有问题
 */
const defaultNoSceneHeaderUrl = [
    /entrances\/userinfo/,
    /system\.user\/info/,
    /application\/instance\/list/,
    /entrances\/(.*)\/config/,
    /system.auth\/apply_token/,
    /waitjoin\/list/,
    /entrances\/scene\/datas/,
]

export function isNoSceneHeaderUrl(url: string) {
    return defaultNoSceneHeaderUrl.some((item) => item.test(url))
}

export function appendNoSceneHeaderUrl(url: RegExp) {
    defaultNoSceneHeaderUrl.push(url)
}
