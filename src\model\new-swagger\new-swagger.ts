import NewSwaggerApi from "./api"

export class NewSwagger {
    private api: NewSwaggerApi
    constructor() {
        this.api = new NewSwaggerApi()
    }

    public domainList() {
        return this.api.domainList()
    }
    public gatewayList() {
        return this.api.gatewayList()
    }
    public getGateway(gwName: string) {
        return this.api.getGateway(gwName)
    }
    public getDomainModelList(domainName: string) {
        return this.api.getDomainModelList(domainName)
    }
    public getDomainServiceList(domainName: string) {
        return this.api.getDomainServiceList(domainName)
    }
    public getModel(domainName: string, modelName: string) {
        return this.api.getModel(domainName, modelName)
    }
}
