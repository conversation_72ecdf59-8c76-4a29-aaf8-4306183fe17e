<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Tools | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="tools.html">Tools</a>
				</li>
			</ul>
			<h1>Namespace Tools</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Type aliases</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#filterstype" class="tsd-kind-icon">Filters<wbr>Type</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#boolean_filter" class="tsd-kind-icon">boolean_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#cascader_filter" class="tsd-kind-icon">cascader_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#combine_full_text_filter" class="tsd-kind-icon">combine_<wbr>full_<wbr>text_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#combo_text_filter" class="tsd-kind-icon">combo_<wbr>text_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#date_between_filter" class="tsd-kind-icon">date_<wbr>between_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#date_filter" class="tsd-kind-icon">date_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#enum_filter" class="tsd-kind-icon">enum_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#full_text_filter" class="tsd-kind-icon">full_<wbr>text_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#number_filter" class="tsd-kind-icon">number_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#search_filter" class="tsd-kind-icon">search_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#simple_relationship_filters-1" class="tsd-kind-icon">simple_<wbr>relationship_<wbr>filters</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#text_filter" class="tsd-kind-icon">text_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#tree_filter" class="tsd-kind-icon">tree_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#workflow_instance_state_filter" class="tsd-kind-icon">workflow_<wbr>instance_<wbr>state_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#workflow_process_name_filter" class="tsd-kind-icon">workflow_<wbr>process_<wbr>name_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#workflow_process_name_multi_filter" class="tsd-kind-icon">workflow_<wbr>process_<wbr>name_<wbr>multi_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#workflow_process_state_filter" class="tsd-kind-icon">workflow_<wbr>process_<wbr>state_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#workflow_process_state_multi_filter" class="tsd-kind-icon">workflow_<wbr>process_<wbr>state_<wbr>multi_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#workflow_process_task_filter" class="tsd-kind-icon">workflow_<wbr>process_<wbr>task_<wbr>filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="tools.html#workflow_process_task_multi_filter" class="tsd-kind-icon">workflow_<wbr>process_<wbr>task_<wbr>multi_<wbr>filter</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Type aliases</h2>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="filterstype" class="tsd-anchor"></a>
					<h3>Filters<wbr>Type</h3>
					<div class="tsd-signature tsd-kind-icon">Filters<wbr>Type<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>boolean_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#boolean_filter" class="tsd-signature-type">boolean_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>cascader_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#cascader_filter" class="tsd-signature-type">cascader_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>combine_full_text_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#combine_full_text_filter" class="tsd-signature-type">combine_full_text_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>combo_text_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#combo_text_filter" class="tsd-signature-type">combo_text_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>date_between_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#date_between_filter" class="tsd-signature-type">date_between_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>date_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#date_filter" class="tsd-signature-type">date_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>datetime_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#date_filter" class="tsd-signature-type">date_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>enum_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#enum_filter" class="tsd-signature-type">enum_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>full_text_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#full_text_filter" class="tsd-signature-type">full_text_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>number_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#number_filter" class="tsd-signature-type">number_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>search_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#search_filter" class="tsd-signature-type">search_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>simple_relationship_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#filterstype.__type.simple_relationship_filters" class="tsd-signature-type">simple_relationship_filters</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>text_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#text_filter" class="tsd-signature-type">text_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tree_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#tree_filter" class="tsd-signature-type">tree_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>workflow_instance_state_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#workflow_instance_state_filter" class="tsd-signature-type">workflow_instance_state_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>workflow_process_name_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#workflow_process_name_filter" class="tsd-signature-type">workflow_process_name_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>workflow_process_name_multi_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#workflow_process_name_multi_filter" class="tsd-signature-type">workflow_process_name_multi_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>workflow_process_state_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#workflow_process_state_filter" class="tsd-signature-type">workflow_process_state_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>workflow_process_state_multi_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#workflow_process_name_multi_filter" class="tsd-signature-type">workflow_process_name_multi_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>workflow_process_task_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#workflow_process_task_filter" class="tsd-signature-type">workflow_process_task_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>workflow_process_task_multi_filters<span class="tsd-signature-symbol">: </span><a href="tools.html#workflow_process_task_multi_filter" class="tsd-signature-type">workflow_process_task_multi_filter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:257</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>boolean_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#boolean_filter" class="tsd-signature-type">boolean_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>cascader_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#cascader_filter" class="tsd-signature-type">cascader_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>combine_<wbr>full_<wbr>text_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#combine_full_text_filter" class="tsd-signature-type">combine_full_text_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>combo_<wbr>text_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#combo_text_filter" class="tsd-signature-type">combo_text_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>date_<wbr>between_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#date_between_filter" class="tsd-signature-type">date_between_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>date_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#date_filter" class="tsd-signature-type">date_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>datetime_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#date_filter" class="tsd-signature-type">date_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>enum_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#enum_filter" class="tsd-signature-type">enum_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>full_<wbr>text_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#full_text_filter" class="tsd-signature-type">full_text_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>number_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#number_filter" class="tsd-signature-type">number_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>search_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#search_filter" class="tsd-signature-type">search_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>simple_<wbr>relationship_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#filterstype.__type.simple_relationship_filters" class="tsd-signature-type">simple_relationship_filters</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>text_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#text_filter" class="tsd-signature-type">text_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tree_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#tree_filter" class="tsd-signature-type">tree_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>workflow_<wbr>instance_<wbr>state_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#workflow_instance_state_filter" class="tsd-signature-type">workflow_instance_state_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>workflow_<wbr>process_<wbr>name_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#workflow_process_name_filter" class="tsd-signature-type">workflow_process_name_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>workflow_<wbr>process_<wbr>name_<wbr>multi_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#workflow_process_name_multi_filter" class="tsd-signature-type">workflow_process_name_multi_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>workflow_<wbr>process_<wbr>state_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#workflow_process_state_filter" class="tsd-signature-type">workflow_process_state_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>workflow_<wbr>process_<wbr>state_<wbr>multi_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#workflow_process_name_multi_filter" class="tsd-signature-type">workflow_process_name_multi_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>workflow_<wbr>process_<wbr>task_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#workflow_process_task_filter" class="tsd-signature-type">workflow_process_task_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>workflow_<wbr>process_<wbr>task_<wbr>multi_<wbr>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#workflow_process_task_multi_filter" class="tsd-signature-type">workflow_process_task_multi_filter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="boolean_filter" class="tsd-anchor"></a>
					<h3>boolean_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">boolean_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:204</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="cascader_filter" class="tsd-anchor"></a>
					<h3>cascader_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">cascader_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>filed_values<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:206</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>filed_<wbr>values<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter-index-signature">
										<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-type">string</span></h5>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="combine_full_text_filter" class="tsd-anchor"></a>
					<h3>combine_<wbr>full_<wbr>text_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">combine_<wbr>full_<wbr>text_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>properties<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:212</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>properties<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="combo_text_filter" class="tsd-anchor"></a>
					<h3>combo_<wbr>text_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">combo_<wbr>text_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>fields<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:192</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>fields<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="date_between_filter" class="tsd-anchor"></a>
					<h3>date_<wbr>between_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">date_<wbr>between_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>date<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>endDate<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>startDate<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:193</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>date<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>end<wbr>Date<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>start<wbr>Date<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="date_filter" class="tsd-anchor"></a>
					<h3>date_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">date_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>end<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>start<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:190</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>end<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>start<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="enum_filter" class="tsd-anchor"></a>
					<h3>enum_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">enum_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:205</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="full_text_filter" class="tsd-anchor"></a>
					<h3>full_<wbr>text_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">full_<wbr>text_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:211</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="number_filter" class="tsd-anchor"></a>
					<h3>number_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">number_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>end<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>start<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:198</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>end<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>start<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="search_filter" class="tsd-anchor"></a>
					<h3>search_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">search_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>include<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>range<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:199</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>include<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>range<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="simple_relationship_filters-1" class="tsd-anchor"></a>
					<h3>simple_<wbr>relationship_<wbr>filters</h3>
					<div class="tsd-signature tsd-kind-icon">simple_<wbr>relationship_<wbr>filters<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>entityIds<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>model<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>predicate<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:251</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>entity<wbr>Ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>model<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>predicate<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="text_filter" class="tsd-anchor"></a>
					<h3>text_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">text_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:191</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="tree_filter" class="tsd-anchor"></a>
					<h3>tree_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">tree_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>direct<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>is_param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">; </span>nodeId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">; </span>treeModelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:216</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>direct<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>is_<wbr>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>node<wbr>Id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tree<wbr>Model<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="workflow_instance_state_filter" class="tsd-anchor"></a>
					<h3>workflow_<wbr>instance_<wbr>state_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">workflow_<wbr>instance_<wbr>state_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:248</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="workflow_process_name_filter" class="tsd-anchor"></a>
					<h3>workflow_<wbr>process_<wbr>name_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">workflow_<wbr>process_<wbr>name_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:224</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="workflow_process_name_multi_filter" class="tsd-anchor"></a>
					<h3>workflow_<wbr>process_<wbr>name_<wbr>multi_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">workflow_<wbr>process_<wbr>name_<wbr>multi_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:236</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="workflow_process_state_filter" class="tsd-anchor"></a>
					<h3>workflow_<wbr>process_<wbr>state_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">workflow_<wbr>process_<wbr>state_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:228</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="workflow_process_state_multi_filter" class="tsd-anchor"></a>
					<h3>workflow_<wbr>process_<wbr>state_<wbr>multi_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">workflow_<wbr>process_<wbr>state_<wbr>multi_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:240</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="workflow_process_task_filter" class="tsd-anchor"></a>
					<h3>workflow_<wbr>process_<wbr>task_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">workflow_<wbr>process_<wbr>task_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:232</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="workflow_process_task_multi_filter" class="tsd-anchor"></a>
					<h3>workflow_<wbr>process_<wbr>task_<wbr>multi_<wbr>filter</h3>
					<div class="tsd-signature tsd-kind-icon">workflow_<wbr>process_<wbr>task_<wbr>multi_<wbr>filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:244</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
					<li class="current tsd-kind-namespace">
						<a href="tools.html">Tools</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#filterstype" class="tsd-kind-icon">Filters<wbr>Type</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#boolean_filter" class="tsd-kind-icon">boolean_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#cascader_filter" class="tsd-kind-icon">cascader_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#combine_full_text_filter" class="tsd-kind-icon">combine_<wbr>full_<wbr>text_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#combo_text_filter" class="tsd-kind-icon">combo_<wbr>text_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#date_between_filter" class="tsd-kind-icon">date_<wbr>between_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#date_filter" class="tsd-kind-icon">date_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#enum_filter" class="tsd-kind-icon">enum_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#full_text_filter" class="tsd-kind-icon">full_<wbr>text_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#number_filter" class="tsd-kind-icon">number_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#search_filter" class="tsd-kind-icon">search_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#simple_relationship_filters-1" class="tsd-kind-icon">simple_<wbr>relationship_<wbr>filters</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#text_filter" class="tsd-kind-icon">text_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#tree_filter" class="tsd-kind-icon">tree_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#workflow_instance_state_filter" class="tsd-kind-icon">workflow_<wbr>instance_<wbr>state_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#workflow_process_name_filter" class="tsd-kind-icon">workflow_<wbr>process_<wbr>name_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#workflow_process_name_multi_filter" class="tsd-kind-icon">workflow_<wbr>process_<wbr>name_<wbr>multi_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#workflow_process_state_filter" class="tsd-kind-icon">workflow_<wbr>process_<wbr>state_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#workflow_process_state_multi_filter" class="tsd-kind-icon">workflow_<wbr>process_<wbr>state_<wbr>multi_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#workflow_process_task_filter" class="tsd-kind-icon">workflow_<wbr>process_<wbr>task_<wbr>filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="tools.html#workflow_process_task_multi_filter" class="tsd-kind-icon">workflow_<wbr>process_<wbr>task_<wbr>multi_<wbr>filter</a>
					</li>
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>