import axios from "../../core/axios/index"
import { encodeParams4Parameters } from "../../core/tools/form"
import type * as dto from "../../def/index"

export default class ModelSchemaManagerApi {
    constructor(
        private config: dto.ModelSchemaManagerTypes.ConstructorParams
    ) {}

    public queryV1() {
        return axios.get<{
            rows: Array<{
                [key: string]: {
                    value: unknown
                }
            }>
        }>(
            `general/model/scheme/${this.config.model_name}/${
                this.config.type
            }/${
                this.config.targetName || "default"
            }/list?${encodeParams4Parameters({
                name: "",
                item_index: 0,
                item_size: 50,
                prefilters: [],
                columns: ["*"],
                order_obj: {},
                filters: {
                    date_filters: [],
                    datetime_filters: [],
                    text_filters: [],
                    combo_text_filters: [],
                    date_between_filters: [],
                    number_filters: [],
                    search_filters: [],
                    boolean_filters: [],
                    enum_filters: [],
                    cascader_filters: [],
                    full_text_filters: [],
                    combine_full_text_filters: [],
                    tree_filters: [],
                    workflow_process_name_filters: [],
                    workflow_process_state_filters: [],
                    workflow_process_task_filters: [],
                    workflow_instance_state_filters: [],
                    simple_relationship_filters: [],
                },
                tagFilters: [],
                sorts: [],
            })}`
        )
    }

    public query() {
        return axios.get<dto.ModelSchemaManagerTypes.SchemaItem[]>(
            `general/model/scheme/${this.config.model_name}/${
                this.config.type
            }/${this.config.targetName || "default"}/query`
        )
    }

    public rename({ old, val }) {
        const oldName = decodeURI(old)
        const newName = decodeURI(val)
        return axios.post<dto.ModelSchemaManagerTypes.SchemaItem[]>(
            `general/model/scheme/${this.config.model_name}/${this.config.type}/${this.config.targetName}/${oldName}/${newName}/rename`
        )
    }
    public setDefaultV2(schemeType, schemeId) {
        return axios.post<dto.ModelSchemaManagerTypes.SchemaItem[]>(
            `general/model/scheme/${this.config.model_name}/${this.config.type}/${this.config.targetName}/${schemeType}/${schemeId}/set_default`
        )
    }
}
