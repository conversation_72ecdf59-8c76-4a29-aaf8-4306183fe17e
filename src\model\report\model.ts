import { global } from "../../core/global"
import { encodeParams4Parameters } from "../../core/tools/form"
import { exportToExcelForReport } from "../../core/tools/sse"

import { ReportApi } from "./api"

export class Report {
    private readonly api: ReportApi

    constructor(
        private domain: string,
        private reportName: string,
        private secondaryApi?: string
    ) {
        this.api = new ReportApi(domain, reportName)
    }

    public getMeta() {
        return this.api.meta()
    }

    private createExportUrl(parameters: unknown) {
        return `${this.secondaryApi || global.baseUrl}general/report/${
            this.domain
        }/${this.reportName}/sse/export?${encodeParams4Parameters(parameters, true)}`
    }

    private createExport2HtmlUrl(parameters: unknown) {
        return `${this.secondaryApi || global.baseUrl}general/report/${
            this.domain
        }/${this.reportName}/sse/exportHtml?${encodeParams4Parameters(
            parameters
        )}`
    }

    public query<T>(parameters: unknown) {
        return exportToExcelForReport<T>(this.createExportUrl(parameters))
    }

    public query4Html<T>(parameters: unknown) {
        return exportToExcelForReport<T>(this.createExport2HtmlUrl(parameters))
    }
}
