import axios from "../../core/axios/index"
import type * as dto from "../../def/index"

export default class DashboardApi {
    constructor(private projName: string, private dashboardName: string) {}

    public getDashboard() {
        return axios.get<dto.DashboardTypes.getDashboard>(
            `general/dashboard/${this.projName}/${this.dashboardName}`
        )
    }

    public refreshSpecifyChat(index: number) {
        return axios.get<dto.DashboardTypes.refreshSpecifyChat>(
            `general/dashboard/${this.projName}/${this.dashboardName}/${index}`
        )
    }
}
