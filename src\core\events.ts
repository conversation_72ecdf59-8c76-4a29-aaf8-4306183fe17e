import { AxiosResponse } from "axios"

import type * as dto from "../def/index"
import { sseInvoker } from "../model/index/sse-invoker"

const noop = () => {
    // throw new Error("初始化sdk时未打开sse开关")
    console.error("初始化sdk时未打开sse开关")
}

export class Events {
    private tokenExpiring: dto.SSE.callBackFromOutside = () =>
        console.error("登录失效，请重新登录")
    private userInfoChange: dto.UserInfoChangedEventListener = () => null
    private logedin: () => void
    private tokenChanged: (token: string) => void

    private universalErrorCallback: dto.UniversalErrorCallback = () => null
    private universalErrorResponseCallback: dto.UniversalErrorResponseCallback =
        () => null

    private responseTooLargeOrSlowCallback: dto.UniversalErrorResponseCallback =
        () => null

    private async getSse() {
        return await sseInvoker.getSSEInstance()
    }

    /**
     * 添加Token过期时的回掉
     */
    public addTokenExpiring(cb: dto.SSE.callBackFromOutside) {
        this.tokenExpiring = cb
    }

    public async callTokenExpiring(msg?: string | number) {
        const sse = await this.getSse()
        sse?.close()
        this.tokenExpiring(msg)
    }

    /**
     * 添加Token改变时的回掉
     */
    public addTokenChanged(cb: (token: string) => void) {
        this.tokenChanged = cb
    }

    public async callTokenChanged(token: string) {
        if (this.tokenChanged == null) return
        this.tokenChanged(token)
    }

    /**
     * 添加回掉函数
     * 在登陆成功后被调用
     */
    public addLogedinCallback(cb: () => void) {
        this.logedin = cb
    }

    public callLogedin() {
        if (this.logedin == null) return
        this.logedin()
    }

    /**
     * 添加用户信息变化时的回掉
     */
    public addUserInfoChangeListener(cb: dto.UserInfoChangedEventListener) {
        this.userInfoChange = cb
    }

    public callUserInfoChangeListener(
        userInfo: Parameters<dto.UserInfoChangedEventListener>[0]
    ) {
        this.userInfoChange(userInfo)
    }

    /**
     * sdk向外抛出的错误
     */
    public addUniversalErrorCallback(cb: dto.UniversalErrorCallback) {
        this.universalErrorCallback = cb
    }

    public callUniversalErrorCallback(msg: string, res: AxiosResponse) {
        this.universalErrorCallback(msg, res)
    }

    /**
     * sdk向外抛出的错误响应
     */
    public addResponseTooLargeOrSlowCallback(
        cb: dto.UniversalErrorResponseCallback
    ) {
        this.responseTooLargeOrSlowCallback = cb
    }

    public callResponseTooLargeOrSlowCallback(r: unknown) {
        this.responseTooLargeOrSlowCallback &&
            this.responseTooLargeOrSlowCallback(r)
    }

    /**
     * sdk向外抛出的错误响应
     */
    public addUniversalErrorResponseCallback(
        cb: dto.UniversalErrorResponseCallback
    ) {
        this.universalErrorResponseCallback = cb
    }

    public callUniversalErrorResponseCallback(r: unknown) {
        this.universalErrorResponseCallback &&
            this.universalErrorResponseCallback(r)
    }

    /**
     * 添加SSE的模型更新时的监听函数
     * @returns 返回移除监听的方法
     */
    public addTransportMessageListener = (
        callback: dto.SSE.transportMessageListener
    ) =>
        new Promise<dto.SSE.removeSSEListener>(async (done) => {
            const sse = await this.getSse()
            done(sse?.addTransportMessageListener.call(sse, callback) ?? noop)
        })

    /**
     * 添加SSE的消息通知的监听函数
     * @returns 返回移除监听的方法
     */
    public addSseNotifyMessageListener = (callback: dto.SSE.notifyListener) =>
        new Promise<dto.SSE.removeSSEListener>(async (done) => {
            const sse = await this.getSse()
            done(sse?.addSseNotifyMessageListener.call(sse, callback) ?? noop)
        })

    /**
     * 添加监听SSE 链接状态状态的回掉函数
     * @returns 返回移除监听的方法
     */
    public addConnectivityObserver = (callback: dto.SSE.ConnectivityObserver) =>
        new Promise<dto.SSE.removeSSEListener>(async (done) => {
            const sse = await this.getSse()
            done(sse?.addConnectivityObserver.call(sse, callback) ?? noop)
        })

    /**
     * 添加SSE的bigAction进度更新的监听函数
     * @returns 返回移除监听的方法
     */
    public addBigActionMessageListener = (
        callback: dto.SSE.bigActionUpdateListener
    ) =>
        new Promise<dto.SSE.removeSSEListener>(async (done) => {
            const sse = await this.getSse()
            done(sse?.addBigActionMessageListener.call(sse, callback) ?? noop)
        })
}

export const events = new Events()
