import type * as dto from "../../def/index"

import IndexSearchApi from "./api"

const createError = () => new Error("尚未搜索")
export class IndexSearch {
    private api: IndexSearchApi
    private searchParams?: dto.IndexSearchTypes.SearchApiParams
    constructor() {
        this.api = new IndexSearchApi()
    }
    public query() {
        return this.api.query()
    }
    public search(params: dto.IndexSearchTypes.SearchApiParams) {
        this.searchParams = params
        return this.api.search(params)
    }
    public exportData() {
        if (this.searchParams == null) {
            throw createError()
        }
        return this.api.exportData(this.searchParams)
    }
    public exportZip() {
        if (this.searchParams == null) {
            throw createError()
        }
        return this.api.exportZip(this.searchParams)
    }
}
