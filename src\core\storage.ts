import { isInNode, isInUniApp } from "./in-uniapp"

class FakeStorage implements Storage {
    private storage: { [key: string]: string } = {}
    public setItem(key: string, value: string) {
        this.storage[key] = value
    }
    public getItem(key: string) {
        return this.storage[key]
    }

    public removeItem(key: string) {
        return delete this.storage[key]
    }

    public clear() {
        this.storage = {}
    }

    public key(index: number) {
        return this.storage[Object.keys(this.storage)[index]]
    }

    public get length() {
        return Object.keys(this.storage).length
    }
}

class UniappStorage implements Storage {
    private getStorageInfo() {
        return uni.getStorageInfoSync()
    }
    public key(index: number): string {
        return this.getItem(this.getStorageInfo().keys[index])
    }
    public setItem(key: string, value: string) {
        uni.setStorageSync(key, value)
    }
    public getItem(key: string) {
        if (!this.getStorageInfo().keys.includes(key)) return null
        return uni.getStorageSync(key)
    }
    public removeItem(key: string) {
        uni.removeStorageSync(key)
    }
    public clear() {
        uni.clearStorageSync()
    }

    public get length() {
        return uni.getStorageInfoSync().currentSize
    }
}
const inUniApp = isInUniApp()
const inNode = isInNode()

let _fakeLocalStorage: Storage
let _fakeSessionStorage: Storage

if (inUniApp) {
    _fakeLocalStorage = new UniappStorage()
    _fakeSessionStorage = new UniappStorage()
} else if (inNode) {
    _fakeLocalStorage = new FakeStorage()
    _fakeSessionStorage = new FakeStorage()
} else {
    _fakeLocalStorage = localStorage
    _fakeSessionStorage = sessionStorage
}
export const fakeLocalStorage = _fakeLocalStorage
export const fakeSessionStorage = _fakeSessionStorage
