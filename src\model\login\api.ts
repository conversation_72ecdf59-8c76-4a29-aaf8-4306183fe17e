import axios from "../../core/axios/index"
import type * as dto from "../../def/index"

export function getJWTTokenByPassportToken(token: string) {
    return axios.post<dto.Index.loginByPassportRequestResult>(
        `general/auth/login/teammix`,
        { token }
    )
}

export function bindPassportTokenToJwt(params: dto.Index.BindPassportParams) {
    return axios.post<dto.Index.BindPassportRequestResult>(
        `general/auth/bind/teammix`,
        params
    )
}

export function authLogin(authcode: string) {
    return axios.post<dto.Index.authLogin>(`general/auth/login/back`, {
        authcode,
    })
}

export function authLoginBind(params: dto.Index.authLoginBindParams) {
    return axios.post<dto.Index.authLoginBind>(`general/auth/bind/back`, params)
}

export function bindTeammixAccount(token: string) {
    return axios.post<void>(`general/bind/teammix`, { token })
}

export function bindXiaoBaoAccount(authcode: string) {
    return axios.post<void>(`general/bind/back`, { authcode })
}

export function getAccountBindStatus() {
    return axios.get<dto.Index.bindStatusRequestResult>(`general/bind/list`)
}

export function teammixOauthLogin() {
    return axios.post<dto.OAuthLoginTypes.teammixOauthLoginApiResult>(
        `general/auth/teammix`
    )
}

export function hrsOauthLogin(
    params: dto.OAuthLoginTypes.hrsOauthLoginApiParams
) {
    return axios.post<dto.OAuthLoginTypes.hrsOauthLoginApiResult>(
        `general/auth2/hrs100`,
        params
    )
}

export function initCompanyAuthLoginData(orgId: string) {
    return axios.get<void>(
        `general/model/firstpage/request/company_auth_login_data_init/?orgId=${orgId}`
    )
}
