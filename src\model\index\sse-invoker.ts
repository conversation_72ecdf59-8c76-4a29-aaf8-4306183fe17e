import type { Sse } from "../../core/sse/sse"
import type * as dto from "../../def/index"

class SSEInvoker {
    private sseInstance?: Sse
    private awaiting?: Promise<void>
    public init(init: boolean) {
        if (!init) return
        this.getSse()
    }
    private getSse() {
        this.awaiting = new Promise<void>(async (done) => {
            this.sseInstance = (await import("../../core/sse/sse")).default
            done()
        })
    }
    public async getSSEInstance() {
        await this.awaiting
        return this.sseInstance
    }
    public async initEventSource(open = true) {
        await this.awaiting
        if (this.sseInstance == null) return
        this.sseInstance.initEventSource(open)
    }
    public getSSEConnectivity() {
        if (this.sseInstance == null) return true
        return this.sseInstance.connectivity
    }
    public close() {
        if (this.sseInstance == null) return null
        return this.sseInstance.close()
    }

    public registerOnMenuDataChanged(action: dto.SSE.MenuDataChangedListener) {
        if (this.sseInstance) {
            return this.sseInstance.addSseMenuDataChangedMessageListener(action)
        }
        return () => 0
    }

    public registerModels<T>(models: string[]) {
        if (this.sseInstance) {
            return this.sseInstance.registerModels<T>(models)
        }
        return Promise.reject(new Error("SSE 未完成初始化"))
    }

    public registerMenuOnBadgeChanged<T>(
        models: string[],
        project: "entrance" | "communication" | "todo"
    ) {
        if (this.sseInstance) {
            return this.sseInstance.registerMenuOnBadgeChanged<T>(
                models,
                project
            )
        }
        return Promise.reject(new Error("SSE 未完成初始化"))
    }

    public getIds() {
        if (this.sseInstance) {
            return {
                uid: this.sseInstance.getUUID(),
                sid: this.sseInstance.getSessionId(),
            }
        }
        return { uid: "", sid: "" }
    }
}

export const sseInvoker = new SSEInvoker()
