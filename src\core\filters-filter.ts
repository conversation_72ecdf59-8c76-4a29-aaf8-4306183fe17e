import * as dto from "../def/index"
export function isSimpleRelationshipFilter(
    filter: dto.metaFilter
): filter is dto.simpleRelationshipFilter {
    return !!(filter as dto.simpleRelationshipFilter).predicates
}

export function filterHumbleFilter(filters: dto.metaFilter[]) {
    return filters.filter(
        (filter) => !isSimpleRelationshipFilter(filter)
    ) as dto.HumbleFilter[]
}
export function filterSimpleRelationshipFilter(filters: dto.metaFilter[]) {
    return filters.filter(
        (filter) => !isSimpleRelationshipFilter(filter)
    ) as dto.simpleRelationshipFilter[]
}
