<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Action | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="action.html">Action</a>
				</li>
			</ul>
			<h1>Class Action</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<a href="anonymousmodel.html" class="tsd-signature-type">AnonymousModel</a>
						<ul class="tsd-hierarchy">
							<li>
								<span class="target">Action</span>
							</li>
						</ul>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="action.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="action.html#actionid" class="tsd-kind-icon">action<wbr>Id</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-overwrite tsd-is-protected"><a href="action.html#api" class="tsd-kind-icon">api</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="action.html#datadetails" class="tsd-kind-icon">data<wbr>Details</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="action.html#detailparametersmanagers" class="tsd-kind-icon">detail<wbr>Parameters<wbr>Managers</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="action.html#hiddeninputparameters" class="tsd-kind-icon">hidden<wbr>Input<wbr>Parameters</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="action.html#inputs_parameters" class="tsd-kind-icon">inputs_<wbr>parameters</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="action.html#isanonymous" class="tsd-kind-icon">is<wbr>Anonymous</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="action.html#listname" class="tsd-kind-icon">list<wbr>Name</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="action.html#list_parameters" class="tsd-kind-icon">list_<wbr>parameters</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="action.html#meta" class="tsd-kind-icon">meta</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="action.html#modelname" class="tsd-kind-icon">model<wbr>Name</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="action.html#notqueried" class="tsd-kind-icon">not<wbr>Queried</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="action.html#prefilters" class="tsd-kind-icon">prefilters</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="action.html#selected_list" class="tsd-kind-icon">selected_<wbr>list</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#addexcel" class="tsd-kind-icon">add<wbr>Excel</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#addinputs_parameter" class="tsd-kind-icon">add<wbr>Inputs_<wbr>parameter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="action.html#anonymous" class="tsd-kind-icon">anonymous</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="action.html#bigactioncallbackonchange" class="tsd-kind-icon">big<wbr>Action<wbr>Callback<wbr>OnChange</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#bigactioncheck" class="tsd-kind-icon">big<wbr>Action<wbr>Check</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#bigactiondetail" class="tsd-kind-icon">big<wbr>Action<wbr>Detail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#bigactionfileurl" class="tsd-kind-icon">big<wbr>Action<wbr>File<wbr>Url</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#bigactionimport" class="tsd-kind-icon">big<wbr>Action<wbr>Import</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#bigactionsingal" class="tsd-kind-icon">big<wbr>Action<wbr>Singal</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#clearaction" class="tsd-kind-icon">clear<wbr>Action</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#clearexcel" class="tsd-kind-icon">clear<wbr>Excel</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#clearinputs_parameter" class="tsd-kind-icon">clear<wbr>Inputs_<wbr>parameter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#clearlistname" class="tsd-kind-icon">clear<wbr>List<wbr>Name</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#dryexecute" class="tsd-kind-icon">dry<wbr>Execute</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#execute" class="tsd-kind-icon">execute</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#executebatch" class="tsd-kind-icon">execute<wbr>Batch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#executeeach" class="tsd-kind-icon">execute<wbr>Each</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="action.html#executeinneraction" class="tsd-kind-icon">execute<wbr>Inner<wbr>Action</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#executenow" class="tsd-kind-icon">execute<wbr>Now</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#getauthorslist" class="tsd-kind-icon">get<wbr>Authors<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#getbatchexceltemplate" class="tsd-kind-icon">get<wbr>Batch<wbr>Excel<wbr>Template</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#getdatasource" class="tsd-kind-icon">get<wbr>Data<wbr>Source</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#getdetailparametersmanager" class="tsd-kind-icon">get<wbr>Detail<wbr>Parameters<wbr>Manager</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#getdetailparametersmanagerbyname" class="tsd-kind-icon">get<wbr>Detail<wbr>Parameters<wbr>Manager<wbr>ByName</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#getform" class="tsd-kind-icon">get<wbr>Form</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="action.html#getinputsparameters" class="tsd-kind-icon">get<wbr>Inputs<wbr>Parameters</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="action.html#handlehiddentypeparameters" class="tsd-kind-icon">handle<wbr>Hidden<wbr>Type<wbr>Parameters</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="action.html#handlenovalueinputparamsters" class="tsd-kind-icon">handle<wbr>NoValue<wbr>Input<wbr>Paramsters</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="action.html#onbigactionmessagenotify" class="tsd-kind-icon">on<wbr>Big<wbr>Action<wbr>Message<wbr>Notify</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#query" class="tsd-kind-icon">query</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="action.html#queryforyou" class="tsd-kind-icon">query<wbr>For<wbr>You</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#registeronbigactionexecute" class="tsd-kind-icon">register<wbr>OnBig<wbr>Action<wbr>Execute</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#setactionid" class="tsd-kind-icon">set<wbr>Action<wbr>Id</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#setlistname" class="tsd-kind-icon">set<wbr>List<wbr>Name</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="action.html#unanonymous" class="tsd-kind-icon">un<wbr>Anonymous</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#updatecontrolsproperties" class="tsd-kind-icon">update<wbr>Controls<wbr>Properties</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#updateinitialparams" class="tsd-kind-icon">update<wbr>Initial<wbr>Params</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#validatebatch" class="tsd-kind-icon">validate<wbr>Batch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="action.html#validateinput" class="tsd-kind-icon">validate<wbr>Input</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new <wbr>Action<span class="tsd-signature-symbol">(</span>props<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.constructorType</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="action.html" class="tsd-signature-type">Action</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:211</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>props: <span class="tsd-signature-type">dto.ActionTypes.constructorType</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="action.html" class="tsd-signature-type">Action</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="actionid" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> action<wbr>Id</h3>
					<div class="tsd-signature tsd-kind-icon">action<wbr>Id<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/action.ts:209</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-overwrite tsd-is-protected">
					<a name="api" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagProtected">Protected</span> api</h3>
					<div class="tsd-signature tsd-kind-icon">api<span class="tsd-signature-symbol">:</span> <a href="actionapi.html" class="tsd-signature-type">ActionApi</a></div>
					<aside class="tsd-sources">
						<p>Overrides <a href="anonymousmodel.html">AnonymousModel</a>.<a href="anonymousmodel.html#api">api</a></p>
						<ul>
							<li>Defined in src/model/action/action.ts:200</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="datadetails" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> data<wbr>Details</h3>
					<div class="tsd-signature tsd-kind-icon">data<wbr>Details<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.ActionTypes.detaDetail</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/action.ts:204</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="detailparametersmanagers" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> detail<wbr>Parameters<wbr>Managers</h3>
					<div class="tsd-signature tsd-kind-icon">detail<wbr>Parameters<wbr>Managers<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="detailparametermanager.html" class="tsd-signature-type">DetailParameterManager</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/action.ts:206</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="hiddeninputparameters" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> hidden<wbr>Input<wbr>Parameters</h3>
					<div class="tsd-signature tsd-kind-icon">hidden<wbr>Input<wbr>Parameters<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.ActionTypes.inputs_parameters</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/action.ts:203</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="inputs_parameters" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> inputs_<wbr>parameters</h3>
					<div class="tsd-signature tsd-kind-icon">inputs_<wbr>parameters<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.ActionTypes.inputs_parameters</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/action.ts:205</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited">
					<a name="isanonymous" class="tsd-anchor"></a>
					<h3>is<wbr>Anonymous</h3>
					<div class="tsd-signature tsd-kind-icon">is<wbr>Anonymous<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
						<p>Inherited from <a href="anonymousmodel.html">AnonymousModel</a>.<a href="anonymousmodel.html#isanonymous">isAnonymous</a></p>
						<ul>
							<li>Defined in src/core/anonymous-api.ts:18</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="listname" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> list<wbr>Name</h3>
					<div class="tsd-signature tsd-kind-icon">list<wbr>Name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/action.ts:210</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="list_parameters" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> list_<wbr>parameters</h3>
					<div class="tsd-signature tsd-kind-icon">list_<wbr>parameters<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.ActionTypes.list_parameter</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/action.ts:207</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="meta" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> meta</h3>
					<div class="tsd-signature tsd-kind-icon">meta<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.ActionTypes.info</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/action.ts:199</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="modelname" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> model<wbr>Name</h3>
					<div class="tsd-signature tsd-kind-icon">model<wbr>Name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/action.ts:211</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="notqueried" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> not<wbr>Queried</h3>
					<div class="tsd-signature tsd-kind-icon">not<wbr>Queried<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/action.ts:208</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="prefilters" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> prefilters</h3>
					<div class="tsd-signature tsd-kind-icon">prefilters<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.prefilter</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/action.ts:202</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="selected_list" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> selected_<wbr>list</h3>
					<div class="tsd-signature tsd-kind-icon">selected_<wbr>list<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/action.ts:201</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addexcel" class="tsd-anchor"></a>
					<h3>add<wbr>Excel</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Excel<span class="tsd-signature-symbol">(</span>excel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.list_parameter</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:329</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加excel中的数据，以键值对的形式
									键是excel的表头</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>excel: <span class="tsd-signature-type">dto.ActionTypes.list_parameter</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
							<p>返回当前实例，供链式调用</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addinputs_parameter" class="tsd-anchor"></a>
					<h3>add<wbr>Inputs_<wbr>parameter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Inputs_<wbr>parameter<span class="tsd-signature-symbol">(</span>values<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.addInputs_parameterParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:281</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>如方法名所示，会在当前类实例中保存一个参数。</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>values: <span class="tsd-signature-type">dto.ActionTypes.addInputs_parameterParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
							<p>返回当前实例，供链式调用</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="anonymous" class="tsd-anchor"></a>
					<h3>anonymous</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">anonymous<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="anonymousmodel.html">AnonymousModel</a>.<a href="anonymousmodel.html#anonymous">anonymous</a></p>
								<ul>
									<li>Defined in src/core/anonymous-api.ts:19</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="bigactioncallbackonchange" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> big<wbr>Action<wbr>Callback<wbr>OnChange</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">big<wbr>Action<wbr>Callback<wbr>OnChange<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:219</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="bigactioncheck" class="tsd-anchor"></a>
					<h3>big<wbr>Action<wbr>Check</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">big<wbr>Action<wbr>Check<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#bigactioncheckresp" class="tsd-signature-type">BigActionCheckResp</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:255</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#bigactioncheckresp" class="tsd-signature-type">BigActionCheckResp</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="bigactiondetail" class="tsd-anchor"></a>
					<h3>big<wbr>Action<wbr>Detail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">big<wbr>Action<wbr>Detail<span class="tsd-signature-symbol">(</span>query<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.BigActionDetailQuery</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#bigactiondetailresult" class="tsd-signature-type">BigActionDetailResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:251</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>query: <span class="tsd-signature-type">dto.ActionTypes.BigActionDetailQuery</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#bigactiondetailresult" class="tsd-signature-type">BigActionDetailResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="bigactionfileurl" class="tsd-anchor"></a>
					<h3>big<wbr>Action<wbr>File<wbr>Url</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">big<wbr>Action<wbr>File<wbr>Url<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:259</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="bigactionimport" class="tsd-anchor"></a>
					<h3>big<wbr>Action<wbr>Import</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">big<wbr>Action<wbr>Import<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:263</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">any</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="bigactionsingal" class="tsd-anchor"></a>
					<h3>big<wbr>Action<wbr>Singal</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">big<wbr>Action<wbr>Singal<span class="tsd-signature-symbol">(</span>taskId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, signal<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#bigactiondetailresult" class="tsd-signature-type">BigActionDetailResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:247</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>taskId: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>signal: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#bigactiondetailresult" class="tsd-signature-type">BigActionDetailResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearaction" class="tsd-anchor"></a>
					<h3>clear<wbr>Action</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Action<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:715</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>根据actionId清理服务器端action的缓存</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearexcel" class="tsd-anchor"></a>
					<h3>clear<wbr>Excel</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Excel<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:338</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>清空方法名所示的参数的数组</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
							<p>返回当前实例，供链式调用</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearinputs_parameter" class="tsd-anchor"></a>
					<h3>clear<wbr>Inputs_<wbr>parameter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Inputs_<wbr>parameter<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:297</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>清空方法名所示的参数的数组</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
							<p>返回当前实例，供链式调用</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearlistname" class="tsd-anchor"></a>
					<h3>clear<wbr>List<wbr>Name</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>List<wbr>Name<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:272</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="dryexecute" class="tsd-anchor"></a>
					<h3>dry<wbr>Execute</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">dry<wbr>Execute<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ActionTypes.executeParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:478</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagDefault value">Default value</span> params: <span class="tsd-signature-type">dto.ActionTypes.executeParams</span><span class="tsd-signature-symbol"> = {}</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="execute" class="tsd-anchor"></a>
					<h3>execute</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">execute<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ActionTypes.executeParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:473</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>执行action</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagDefault value">Default value</span> params: <span class="tsd-signature-type">dto.ActionTypes.executeParams</span><span class="tsd-signature-symbol"> = {}</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="executebatch" class="tsd-anchor"></a>
					<h3>execute<wbr>Batch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">execute<wbr>Batch<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.executeBatchParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:532</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>执行batch action</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ActionTypes.executeBatchParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="executeeach" class="tsd-anchor"></a>
					<h3>execute<wbr>Each</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">execute<wbr>Each<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.executeEachParams</span>, headers<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>CurrentOrg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">(Anonymous function)</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:552</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>执行each类型action
										返回开始执行方法，开始执行方法可以传入@param onProgress @param onError
									返回一个将被完成的Promise实例@param awaiting 和主动关闭链接的方法 @param close;</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ActionTypes.executeEachParams</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> headers: <span class="tsd-signature-symbol">{ </span>CurrentOrg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> Current<wbr>Org<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">(Anonymous function)</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="executeinneraction" class="tsd-anchor"></a>
					<h3>execute<wbr>Inner<wbr>Action</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">execute<wbr>Inner<wbr>Action&lt;T&gt;<span class="tsd-signature-symbol">(</span>actionName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:721</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>actionName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="executenow" class="tsd-anchor"></a>
					<h3>execute<wbr>Now</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">execute<wbr>Now<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.executeNowParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:499</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>立即执行action</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ActionTypes.executeNowParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getauthorslist" class="tsd-anchor"></a>
					<h3>get<wbr>Authors<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Authors<wbr>List<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#getauthorslistrequestresult" class="tsd-signature-type">getAuthorsListRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:465</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取当前有权操作当前action的人的列表</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#getauthorslistrequestresult" class="tsd-signature-type">getAuthorsListRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getbatchexceltemplate" class="tsd-anchor"></a>
					<h3>get<wbr>Batch<wbr>Excel<wbr>Template</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Batch<wbr>Excel<wbr>Template<span class="tsd-signature-symbol">(</span>schemaName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:621</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取指定excel模版的下载链接</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>schemaName: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getdatasource" class="tsd-anchor"></a>
					<h3>get<wbr>Data<wbr>Source</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Data<wbr>Source<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.getDataSourceRequestParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#getdatasourcerequestresult" class="tsd-signature-type">getDataSourceRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:613</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p> 用于上传excel的地方的加载远程数据的action</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ActionTypes.getDataSourceRequestParams</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#getdatasourcerequestresult" class="tsd-signature-type">getDataSourceRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getdetailparametersmanager" class="tsd-anchor"></a>
					<h3>get<wbr>Detail<wbr>Parameters<wbr>Manager</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Detail<wbr>Parameters<wbr>Manager<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="detailparametermanager.html" class="tsd-signature-type">DetailParameterManager</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:311</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取所有detailParameters manager</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="detailparametermanager.html" class="tsd-signature-type">DetailParameterManager</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getdetailparametersmanagerbyname" class="tsd-anchor"></a>
					<h3>get<wbr>Detail<wbr>Parameters<wbr>Manager<wbr>ByName</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Detail<wbr>Parameters<wbr>Manager<wbr>ByName<span class="tsd-signature-symbol">(</span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="detailparametermanager.html" class="tsd-signature-type">DetailParameterManager</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:319</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取指定detailParameters manager</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>name: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="detailparametermanager.html" class="tsd-signature-type">DetailParameterManager</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getform" class="tsd-anchor"></a>
					<h3>get<wbr>Form</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Form<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="actionform.html" class="tsd-signature-type">ActionForm</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:682</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="actionform.html" class="tsd-signature-type">ActionForm</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="getinputsparameters" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> get<wbr>Inputs<wbr>Parameters</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Inputs<wbr>Parameters<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="action.html#inputs_parameters" class="tsd-signature-type">inputs_parameters</a><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:386</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <a href="action.html#inputs_parameters" class="tsd-signature-type">inputs_parameters</a><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="handlehiddentypeparameters" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> handle<wbr>Hidden<wbr>Type<wbr>Parameters</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">handle<wbr>Hidden<wbr>Type<wbr>Parameters<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:368</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="handlenovalueinputparamsters" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> handle<wbr>NoValue<wbr>Input<wbr>Paramsters</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">handle<wbr>NoValue<wbr>Input<wbr>Paramsters<span class="tsd-signature-symbol">(</span>data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.updateControlsPropertiesRequestResult</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">"masters"</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:629</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>处理某些inputParamster，其类型为hidden，且值需要调用updateControlsProperties来获得</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>data: <span class="tsd-signature-type">dto.ActionTypes.updateControlsPropertiesRequestResult</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">"masters"</span><span class="tsd-signature-symbol">]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="onbigactionmessagenotify" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> on<wbr>Big<wbr>Action<wbr>Message<wbr>Notify</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">on<wbr>Big<wbr>Action<wbr>Message<wbr>Notify<span class="tsd-signature-symbol">(</span>msg<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.msg</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:222</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>msg: <span class="tsd-signature-type">dto.SSE.msg</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="query" class="tsd-anchor"></a>
					<h3>query</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<span class="tsd-signature-symbol">(</span>selected_list<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span>, prefilters<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.prefilter</span><span class="tsd-signature-symbol">[]</span>, others<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>intent<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ActionIntent</span><span class="tsd-signature-symbol">; </span>intentContext<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ActionIntent</span><span class="tsd-signature-symbol">; </span>workflowExecuteContext<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.workflow2.WorkflowExecuteContext</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#info" class="tsd-signature-type">info</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:405</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>得到action详情</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> selected_list: <span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> prefilters: <span class="tsd-signature-type">dto.prefilter</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> others: <span class="tsd-signature-symbol">{ </span>intent<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ActionIntent</span><span class="tsd-signature-symbol">; </span>intentContext<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ActionIntent</span><span class="tsd-signature-symbol">; </span>workflowExecuteContext<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.workflow2.WorkflowExecuteContext</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> intent<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ActionIntent</span></h5>
											<div class="tsd-comment tsd-typography">
												<dl class="tsd-comment-tags">
													<dt>deprecated</dt>
													<dd><p>即将废弃，请使用 intentContext</p>
													</dd>
												</dl>
											</div>
										</li>
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> intent<wbr>Context<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ActionIntent</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> workflow<wbr>Execute<wbr>Context<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.workflow2.WorkflowExecuteContext</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#info" class="tsd-signature-type">info</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="queryforyou" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> query<wbr>For<wbr>You</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">query<wbr>For<wbr>You<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:302</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="registeronbigactionexecute" class="tsd-anchor"></a>
					<h3>register<wbr>OnBig<wbr>Action<wbr>Execute</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">register<wbr>OnBig<wbr>Action<wbr>Execute<span class="tsd-signature-symbol">(</span>cb<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.callBackOfBigActionExecute</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:242</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>注册当前模型的数据变化时的回掉</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>cb: <span class="tsd-signature-type">dto.SSE.callBackOfBigActionExecute</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="setactionid" class="tsd-anchor"></a>
					<h3>set<wbr>Action<wbr>Id</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Action<wbr>Id<span class="tsd-signature-symbol">(</span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:348</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>设置actionId</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>actionId: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
							<p>返回当前实例，供链式调用</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="setlistname" class="tsd-anchor"></a>
					<h3>set<wbr>List<wbr>Name</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">set<wbr>List<wbr>Name<span class="tsd-signature-symbol">(</span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:267</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>name: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="unanonymous" class="tsd-anchor"></a>
					<h3>un<wbr>Anonymous</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">un<wbr>Anonymous<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="anonymousmodel.html">AnonymousModel</a>.<a href="anonymousmodel.html#unanonymous">unAnonymous</a></p>
								<ul>
									<li>Defined in src/core/anonymous-api.ts:23</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updatecontrolsproperties" class="tsd-anchor"></a>
					<h3>update<wbr>Controls<wbr>Properties</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Controls<wbr>Properties<span class="tsd-signature-symbol">(</span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ActionTypes.updateControlsPropertiesRequestParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#updatecontrolspropertiesrequestresult" class="tsd-signature-type">updateControlsPropertiesRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:655</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>property: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>需要触发此方法的表单项的property</p>
									</div>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagDefault value">Default value</span> params: <span class="tsd-signature-type">dto.ActionTypes.updateControlsPropertiesRequestParams</span><span class="tsd-signature-symbol"> = {}</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>同execute的参数</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#updatecontrolspropertiesrequestresult" class="tsd-signature-type">updateControlsPropertiesRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updateinitialparams" class="tsd-anchor"></a>
					<h3>update<wbr>Initial<wbr>Params</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Initial<wbr>Params<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.updateInitialParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:358</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>手动更新缓存的</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ActionTypes.updateInitialParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="validatebatch" class="tsd-anchor"></a>
					<h3>validate<wbr>Batch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">validate<wbr>Batch<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.validateBatchParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#validaterequestresult" class="tsd-signature-type">validateRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:515</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>验证batch action</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ActionTypes.validateBatchParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#validaterequestresult" class="tsd-signature-type">validateRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>Promise<validateRequestResult>;</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="validateinput" class="tsd-anchor"></a>
					<h3>validate<wbr>Input</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">validate<wbr>Input<span class="tsd-signature-symbol">(</span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#validatorinputerresult" class="tsd-signature-type">ValidatorInputerResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:671</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>property: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/actiontypes.html#validatorinputerresult" class="tsd-signature-type">ValidatorInputerResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="action.html" class="tsd-kind-icon">Action</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class">
								<a href="action.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="action.html#actionid" class="tsd-kind-icon">action<wbr>Id</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-overwrite tsd-is-protected">
								<a href="action.html#api" class="tsd-kind-icon">api</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="action.html#datadetails" class="tsd-kind-icon">data<wbr>Details</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="action.html#detailparametersmanagers" class="tsd-kind-icon">detail<wbr>Parameters<wbr>Managers</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="action.html#hiddeninputparameters" class="tsd-kind-icon">hidden<wbr>Input<wbr>Parameters</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="action.html#inputs_parameters" class="tsd-kind-icon">inputs_<wbr>parameters</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-inherited">
								<a href="action.html#isanonymous" class="tsd-kind-icon">is<wbr>Anonymous</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="action.html#listname" class="tsd-kind-icon">list<wbr>Name</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="action.html#list_parameters" class="tsd-kind-icon">list_<wbr>parameters</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="action.html#meta" class="tsd-kind-icon">meta</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="action.html#modelname" class="tsd-kind-icon">model<wbr>Name</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="action.html#notqueried" class="tsd-kind-icon">not<wbr>Queried</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="action.html#prefilters" class="tsd-kind-icon">prefilters</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="action.html#selected_list" class="tsd-kind-icon">selected_<wbr>list</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#addexcel" class="tsd-kind-icon">add<wbr>Excel</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#addinputs_parameter" class="tsd-kind-icon">add<wbr>Inputs_<wbr>parameter</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="action.html#anonymous" class="tsd-kind-icon">anonymous</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="action.html#bigactioncallbackonchange" class="tsd-kind-icon">big<wbr>Action<wbr>Callback<wbr>OnChange</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#bigactioncheck" class="tsd-kind-icon">big<wbr>Action<wbr>Check</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#bigactiondetail" class="tsd-kind-icon">big<wbr>Action<wbr>Detail</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#bigactionfileurl" class="tsd-kind-icon">big<wbr>Action<wbr>File<wbr>Url</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#bigactionimport" class="tsd-kind-icon">big<wbr>Action<wbr>Import</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#bigactionsingal" class="tsd-kind-icon">big<wbr>Action<wbr>Singal</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#clearaction" class="tsd-kind-icon">clear<wbr>Action</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#clearexcel" class="tsd-kind-icon">clear<wbr>Excel</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#clearinputs_parameter" class="tsd-kind-icon">clear<wbr>Inputs_<wbr>parameter</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#clearlistname" class="tsd-kind-icon">clear<wbr>List<wbr>Name</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#dryexecute" class="tsd-kind-icon">dry<wbr>Execute</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#execute" class="tsd-kind-icon">execute</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#executebatch" class="tsd-kind-icon">execute<wbr>Batch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#executeeach" class="tsd-kind-icon">execute<wbr>Each</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="action.html#executeinneraction" class="tsd-kind-icon">execute<wbr>Inner<wbr>Action</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#executenow" class="tsd-kind-icon">execute<wbr>Now</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#getauthorslist" class="tsd-kind-icon">get<wbr>Authors<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#getbatchexceltemplate" class="tsd-kind-icon">get<wbr>Batch<wbr>Excel<wbr>Template</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#getdatasource" class="tsd-kind-icon">get<wbr>Data<wbr>Source</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#getdetailparametersmanager" class="tsd-kind-icon">get<wbr>Detail<wbr>Parameters<wbr>Manager</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#getdetailparametersmanagerbyname" class="tsd-kind-icon">get<wbr>Detail<wbr>Parameters<wbr>Manager<wbr>ByName</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#getform" class="tsd-kind-icon">get<wbr>Form</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="action.html#getinputsparameters" class="tsd-kind-icon">get<wbr>Inputs<wbr>Parameters</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="action.html#handlehiddentypeparameters" class="tsd-kind-icon">handle<wbr>Hidden<wbr>Type<wbr>Parameters</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="action.html#handlenovalueinputparamsters" class="tsd-kind-icon">handle<wbr>NoValue<wbr>Input<wbr>Paramsters</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="action.html#onbigactionmessagenotify" class="tsd-kind-icon">on<wbr>Big<wbr>Action<wbr>Message<wbr>Notify</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#query" class="tsd-kind-icon">query</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="action.html#queryforyou" class="tsd-kind-icon">query<wbr>For<wbr>You</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#registeronbigactionexecute" class="tsd-kind-icon">register<wbr>OnBig<wbr>Action<wbr>Execute</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#setactionid" class="tsd-kind-icon">set<wbr>Action<wbr>Id</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#setlistname" class="tsd-kind-icon">set<wbr>List<wbr>Name</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="action.html#unanonymous" class="tsd-kind-icon">un<wbr>Anonymous</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#updatecontrolsproperties" class="tsd-kind-icon">update<wbr>Controls<wbr>Properties</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#updateinitialparams" class="tsd-kind-icon">update<wbr>Initial<wbr>Params</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#validatebatch" class="tsd-kind-icon">validate<wbr>Batch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="action.html#validateinput" class="tsd-kind-icon">validate<wbr>Input</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>