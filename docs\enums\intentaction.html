<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>IntentAction | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="intentaction.html">IntentAction</a>
				</li>
			</ul>
			<h1>Enumeration IntentAction</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Enumeration members</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="intentaction.html#action" class="tsd-kind-icon">Action</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="intentaction.html#actions" class="tsd-kind-icon">Actions</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="intentaction.html#chat" class="tsd-kind-icon">Chat</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="intentaction.html#detail" class="tsd-kind-icon">Detail</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="intentaction.html#exporttargetdetail" class="tsd-kind-icon">Export<wbr>Target<wbr>Detail</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="intentaction.html#exporttargetlist" class="tsd-kind-icon">Export<wbr>Target<wbr>List</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="intentaction.html#exportworkflowlist" class="tsd-kind-icon">Export<wbr>Workflow<wbr>List</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="intentaction.html#openworkflowdetail" class="tsd-kind-icon">Open<wbr>Workflow<wbr>Detail</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="intentaction.html#openworkflowoperator" class="tsd-kind-icon">Open<wbr>Workflow<wbr>Operator</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="intentaction.html#showcomponent" class="tsd-kind-icon">Show<wbr>Component</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="intentaction.html#showloglist" class="tsd-kind-icon">Show<wbr>Log<wbr>List</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="intentaction.html#showreport" class="tsd-kind-icon">Show<wbr>Report</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="intentaction.html#startprocess" class="tsd-kind-icon">Start<wbr>Process</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="intentaction.html#table" class="tsd-kind-icon">Table</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="intentaction.html#workflow" class="tsd-kind-icon">Workflow</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="intentaction.html#workflowlist" class="tsd-kind-icon">Workflow<wbr>List</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Enumeration members</h2>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="action" class="tsd-anchor"></a>
					<h3>Action</h3>
					<div class="tsd-signature tsd-kind-icon">Action<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;execute&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:872</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="actions" class="tsd-anchor"></a>
					<h3>Actions</h3>
					<div class="tsd-signature tsd-kind-icon">Actions<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;executes&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:873</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="chat" class="tsd-anchor"></a>
					<h3>Chat</h3>
					<div class="tsd-signature tsd-kind-icon">Chat<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;openChat&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:867</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="detail" class="tsd-anchor"></a>
					<h3>Detail</h3>
					<div class="tsd-signature tsd-kind-icon">Detail<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;showDetail&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:871</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="exporttargetdetail" class="tsd-anchor"></a>
					<h3>Export<wbr>Target<wbr>Detail</h3>
					<div class="tsd-signature tsd-kind-icon">Export<wbr>Target<wbr>Detail<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;exportTargetDetail&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:877</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="exporttargetlist" class="tsd-anchor"></a>
					<h3>Export<wbr>Target<wbr>List</h3>
					<div class="tsd-signature tsd-kind-icon">Export<wbr>Target<wbr>List<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;exportTargetList&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:876</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="exportworkflowlist" class="tsd-anchor"></a>
					<h3>Export<wbr>Workflow<wbr>List</h3>
					<div class="tsd-signature tsd-kind-icon">Export<wbr>Workflow<wbr>List<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;exportWorkflowList&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:878</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="openworkflowdetail" class="tsd-anchor"></a>
					<h3>Open<wbr>Workflow<wbr>Detail</h3>
					<div class="tsd-signature tsd-kind-icon">Open<wbr>Workflow<wbr>Detail<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;openWorkflowDetail&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:874</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="openworkflowoperator" class="tsd-anchor"></a>
					<h3>Open<wbr>Workflow<wbr>Operator</h3>
					<div class="tsd-signature tsd-kind-icon">Open<wbr>Workflow<wbr>Operator<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;openWorkflowOperator&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:879</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="showcomponent" class="tsd-anchor"></a>
					<h3>Show<wbr>Component</h3>
					<div class="tsd-signature tsd-kind-icon">Show<wbr>Component<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;showComponent&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:882</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="showloglist" class="tsd-anchor"></a>
					<h3>Show<wbr>Log<wbr>List</h3>
					<div class="tsd-signature tsd-kind-icon">Show<wbr>Log<wbr>List<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;showLogList&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:875</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="showreport" class="tsd-anchor"></a>
					<h3>Show<wbr>Report</h3>
					<div class="tsd-signature tsd-kind-icon">Show<wbr>Report<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;showReport&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:881</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="startprocess" class="tsd-anchor"></a>
					<h3>Start<wbr>Process</h3>
					<div class="tsd-signature tsd-kind-icon">Start<wbr>Process<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;startProcess&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:880</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="table" class="tsd-anchor"></a>
					<h3>Table</h3>
					<div class="tsd-signature tsd-kind-icon">Table<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;showList&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:870</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="workflow" class="tsd-anchor"></a>
					<h3>Workflow</h3>
					<div class="tsd-signature tsd-kind-icon">Workflow<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;showWorkflows&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:868</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="workflowlist" class="tsd-anchor"></a>
					<h3>Workflow<wbr>List</h3>
					<div class="tsd-signature tsd-kind-icon">Workflow<wbr>List<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;showWorkFlowList&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:869</li>
						</ul>
					</aside>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-enum">
						<a href="intentaction.html" class="tsd-kind-icon">Intent<wbr>Action</a>
						<ul>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="intentaction.html#action" class="tsd-kind-icon">Action</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="intentaction.html#actions" class="tsd-kind-icon">Actions</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="intentaction.html#chat" class="tsd-kind-icon">Chat</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="intentaction.html#detail" class="tsd-kind-icon">Detail</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="intentaction.html#exporttargetdetail" class="tsd-kind-icon">Export<wbr>Target<wbr>Detail</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="intentaction.html#exporttargetlist" class="tsd-kind-icon">Export<wbr>Target<wbr>List</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="intentaction.html#exportworkflowlist" class="tsd-kind-icon">Export<wbr>Workflow<wbr>List</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="intentaction.html#openworkflowdetail" class="tsd-kind-icon">Open<wbr>Workflow<wbr>Detail</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="intentaction.html#openworkflowoperator" class="tsd-kind-icon">Open<wbr>Workflow<wbr>Operator</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="intentaction.html#showcomponent" class="tsd-kind-icon">Show<wbr>Component</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="intentaction.html#showloglist" class="tsd-kind-icon">Show<wbr>Log<wbr>List</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="intentaction.html#showreport" class="tsd-kind-icon">Show<wbr>Report</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="intentaction.html#startprocess" class="tsd-kind-icon">Start<wbr>Process</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="intentaction.html#table" class="tsd-kind-icon">Table</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="intentaction.html#workflow" class="tsd-kind-icon">Workflow</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="intentaction.html#workflowlist" class="tsd-kind-icon">Workflow<wbr>List</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>