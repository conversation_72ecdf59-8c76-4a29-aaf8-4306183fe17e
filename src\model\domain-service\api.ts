import type { Method } from "axios"

import axios from "../../core/axios/index"
export default class DomainServiceApi {
    constructor(
        private subProjectName: string,
        private serviceName: string,
        private apiName: string
    ) {}

    public request<ParamsType, DataType, ReturnType>(
        method: Method,
        config: {
            params?: ParamsType
            data?: DataType
            headers?: { [key: string]: string | number }
        }
    ) {
        let serviceName = this.serviceName
        if (config.headers && config.headers["anonymous"] == 1) {
            if (!serviceName.startsWith("anonymous/")) {
                serviceName = `anonymous/${serviceName}`
            }
        }
        return axios.request<ReturnType>({
            method,
            url: `/general/project/${this.subProjectName}/service/${serviceName}/${this.apiName}`,
            params: config.params,
            data: config.data,
            headers: config.headers,
        })
    }
}
