import { global } from "./global"
import { fakeLocalStorage, fakeSessionStorage } from "./storage"

type Timer = ReturnType<typeof setTimeout>

export function TimersManager(storageKey: string) {
    function getCachedata() {
        const result = (
            global.ssr ? fakeSessionStorage : fakeLocalStorage
        ).getItem(storageKey)
        if (result == null) return []
        if (result === "") return []
        return JSON.parse(result) as Timer[]
    }
    function clear() {
        const data = getCachedata()
        data.forEach((id) => clearTimeout(id))
        ;(global.ssr ? fakeSessionStorage : fakeLocalStorage).removeItem(
            storageKey
        )
    }
    return {
        get(): Timer[] {
            return getCachedata()
        },
        push(id: Timer) {
            clear()
            const data = getCachedata()
            data.push(id)
            ;(global.ssr ? fakeSessionStorage : fakeLocalStorage).setItem(
                storageKey,
                JSON.stringify(data)
            )
        },
        clear() {
            clear()
        },
    }
}
