# https://circleci.com/docs/2.0/language-javascript/
version: 2
jobs:
  'node-10':
    docker:
      - image: circleci/node:10
    steps:
      - checkout
      # Download and cache dependencies
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "package.json" }}
            # fallback to using the latest cache if no exact match is found
            - v1-dependencies-
      - run: npm install
      - save_cache:
          paths:
            - node_modules
          key: v1-dependencies-{{ checksum "package.json" }}
      - run: npm test
      - run: npm run cov:send
      - run: npm run cov:check
  'node-12':
    docker:
      - image: circleci/node:12
    steps:
      - checkout
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "package.json" }}
            - v1-dependencies-
      - run: npm install
      - save_cache:
          paths:
            - node_modules
          key: v1-dependencies-{{ checksum "package.json" }}
      - run: npm test
      - run: npm run cov:send
      - run: npm run cov:check
  'node-latest':
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "package.json" }}
            - v1-dependencies-
      - run: npm install
      - save_cache:
          paths:
            - node_modules
          key: v1-dependencies-{{ checksum "package.json" }}
      - run: npm test
      - run: npm run cov:send
      - run: npm run cov:check

workflows:
  version: 2
  build:
    jobs:
      - 'node-10'
      - 'node-12'
      - 'node-latest'
