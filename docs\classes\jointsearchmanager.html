<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>JointSearchManager | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="jointsearchmanager.html">JointSearchManager</a>
				</li>
			</ul>
			<h1>Class JointSearchManager</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-comment">
				<div class="tsd-comment tsd-typography">
					<div class="lead">
						<p> 用于JoinstSearch搜索，封装了复杂的参数结构，提供了方法以操作参数。
						均可链式调用</p>
					</div>
				</div>
			</section>
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">JointSearchManager</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="jointsearchmanager.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section tsd-is-private tsd-is-private-protected">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="jointsearchmanager.html#api" class="tsd-kind-icon">api</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="jointsearchmanager.html#joint_name" class="tsd-kind-icon">joint_<wbr>name</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="jointsearchmanager.html#requestparams" class="tsd-kind-icon">request<wbr>Params</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#addextraformdata" class="tsd-kind-icon">add<wbr>Extra<wbr>Form<wbr>Data</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#addprefilters" class="tsd-kind-icon">add<wbr>Prefilters</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#addsearchfield" class="tsd-kind-icon">add<wbr>Search<wbr>Field</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#addselectedlist" class="tsd-kind-icon">add<wbr>Selected<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#addtagfilters" class="tsd-kind-icon">add<wbr>Tag<wbr>Filters</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#clearextraformdata" class="tsd-kind-icon">clear<wbr>Extra<wbr>Form<wbr>Data</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#clearkeyword" class="tsd-kind-icon">clear<wbr>Keyword</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#clearprefitlers" class="tsd-kind-icon">clear<wbr>Prefitlers</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#clearsearchfield" class="tsd-kind-icon">clear<wbr>Search<wbr>Field</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#clearselected" class="tsd-kind-icon">clear<wbr>Selected</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#clearselectedlist" class="tsd-kind-icon">clear<wbr>Selected<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#cleartagfilters" class="tsd-kind-icon">clear<wbr>Tag<wbr>Filters</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="jointsearchmanager.html#getinitparams" class="tsd-kind-icon">get<wbr>Init<wbr>Params</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#keyword" class="tsd-kind-icon">keyword</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#pageindex" class="tsd-kind-icon">page<wbr>Index</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#pagesize" class="tsd-kind-icon">page<wbr>Size</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#query" class="tsd-kind-icon">query</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#reset" class="tsd-kind-icon">reset</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#searchall" class="tsd-kind-icon">search<wbr>All</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#setactionid" class="tsd-kind-icon">set<wbr>Action<wbr>Id</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jointsearchmanager.html#setselected" class="tsd-kind-icon">set<wbr>Selected</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new <wbr>Joint<wbr>Search<wbr>Manager<span class="tsd-signature-symbol">(</span>joint_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, api<span class="tsd-signature-symbol">: </span><a href="modelapi.html" class="tsd-signature-type">ModelApi</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="jointsearchmanager.html" class="tsd-signature-type">JointSearchManager</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:9</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>joint_name: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>api: <a href="modelapi.html" class="tsd-signature-type">ModelApi</a></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="jointsearchmanager.html" class="tsd-signature-type">JointSearchManager</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="api" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> api</h3>
					<div class="tsd-signature tsd-kind-icon">api<span class="tsd-signature-symbol">:</span> <a href="modelapi.html" class="tsd-signature-type">ModelApi</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/model/joint-search-manager.ts:10</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="joint_name" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> joint_<wbr>name</h3>
					<div class="tsd-signature tsd-kind-icon">joint_<wbr>name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/model/joint-search-manager.ts:10</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="requestparams" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> request<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">request<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.ActionTypes.JointSearchParams</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/model/joint-search-manager.ts:9</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addextraformdata" class="tsd-anchor"></a>
					<h3>add<wbr>Extra<wbr>Form<wbr>Data</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Extra<wbr>Form<wbr>Data<span class="tsd-signature-symbol">(</span>formParam<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:102</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加额外参数</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>formParam: <span class="tsd-signature-type">unknown</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addprefilters" class="tsd-anchor"></a>
					<h3>add<wbr>Prefilters</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Prefilters<span class="tsd-signature-symbol">(</span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:110</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加prefilters</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>prefilters: <span class="tsd-signature-type">dto.prefilters</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addsearchfield" class="tsd-anchor"></a>
					<h3>add<wbr>Search<wbr>Field</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Search<wbr>Field<span class="tsd-signature-symbol">(</span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:54</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>设置搜索模式。</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>property: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addselectedlist" class="tsd-anchor"></a>
					<h3>add<wbr>Selected<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Selected<wbr>List<span class="tsd-signature-symbol">(</span>selected_list<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:86</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加selected_list</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>selected_list: <span class="tsd-signature-type">dto.ActionTypes.selectedList</span><span class="tsd-signature-symbol">[]</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addtagfilters" class="tsd-anchor"></a>
					<h3>add<wbr>Tag<wbr>Filters</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Tag<wbr>Filters<span class="tsd-signature-symbol">(</span>tags<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.TagManagerTypes.TagFilter</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:78</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加标签</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>tags: <span class="tsd-signature-type">dto.TagManagerTypes.TagFilter</span><span class="tsd-signature-symbol">[]</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearextraformdata" class="tsd-anchor"></a>
					<h3>clear<wbr>Extra<wbr>Form<wbr>Data</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Extra<wbr>Form<wbr>Data<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:165</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>清空额外参数</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearkeyword" class="tsd-anchor"></a>
					<h3>clear<wbr>Keyword</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Keyword<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:130</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>重置关键词</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearprefitlers" class="tsd-anchor"></a>
					<h3>clear<wbr>Prefitlers</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Prefitlers<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:117</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>清空prefilters</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearsearchfield" class="tsd-anchor"></a>
					<h3>clear<wbr>Search<wbr>Field</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Search<wbr>Field<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:151</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>重置搜索模式为全部搜索</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearselected" class="tsd-anchor"></a>
					<h3>clear<wbr>Selected</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Selected<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:137</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>重置已选项</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearselectedlist" class="tsd-anchor"></a>
					<h3>clear<wbr>Selected<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Selected<wbr>List<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:144</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>重置slelected_list</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="cleartagfilters" class="tsd-anchor"></a>
					<h3>clear<wbr>Tag<wbr>Filters</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Tag<wbr>Filters<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:158</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>清空标签</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="getinitparams" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> get<wbr>Init<wbr>Params</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Init<wbr>Params<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.JointSearchParams</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:13</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">dto.ActionTypes.JointSearchParams</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="keyword" class="tsd-anchor"></a>
					<h3>keyword</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">keyword<span class="tsd-signature-symbol">(</span>keyword<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:31</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>关键词，默认是空字符串</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>keyword: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="pageindex" class="tsd-anchor"></a>
					<h3>page<wbr>Index</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">page<wbr>Index<span class="tsd-signature-symbol">(</span>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:70</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>第几页</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>index: <span class="tsd-signature-type">number</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="pagesize" class="tsd-anchor"></a>
					<h3>page<wbr>Size</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">page<wbr>Size<span class="tsd-signature-symbol">(</span>size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:62</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>每页多少条数据</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>size: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="query" class="tsd-anchor"></a>
					<h3>query</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#searchjointapiresult" class="tsd-signature-type">searchJointApiResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:124</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>执行搜索</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#searchjointapiresult" class="tsd-signature-type">searchJointApiResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="reset" class="tsd-anchor"></a>
					<h3>reset</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">reset<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:172</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>重置所有参数</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="searchall" class="tsd-anchor"></a>
					<h3>search<wbr>All</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">search<wbr>All<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:46</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>设置搜索模式为全部搜索</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="setactionid" class="tsd-anchor"></a>
					<h3>set<wbr>Action<wbr>Id</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Action<wbr>Id<span class="tsd-signature-symbol">(</span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:94</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>设置actionId</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>actionId: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="setselected" class="tsd-anchor"></a>
					<h3>set<wbr>Selected</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Selected<span class="tsd-signature-symbol">(</span>ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/joint-search-manager.ts:39</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>设置已选项的 id数组</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>ids: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="jointsearchmanager.html" class="tsd-kind-icon">Joint<wbr>Search<wbr>Manager</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class">
								<a href="jointsearchmanager.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="jointsearchmanager.html#api" class="tsd-kind-icon">api</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="jointsearchmanager.html#joint_name" class="tsd-kind-icon">joint_<wbr>name</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="jointsearchmanager.html#requestparams" class="tsd-kind-icon">request<wbr>Params</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#addextraformdata" class="tsd-kind-icon">add<wbr>Extra<wbr>Form<wbr>Data</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#addprefilters" class="tsd-kind-icon">add<wbr>Prefilters</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#addsearchfield" class="tsd-kind-icon">add<wbr>Search<wbr>Field</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#addselectedlist" class="tsd-kind-icon">add<wbr>Selected<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#addtagfilters" class="tsd-kind-icon">add<wbr>Tag<wbr>Filters</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#clearextraformdata" class="tsd-kind-icon">clear<wbr>Extra<wbr>Form<wbr>Data</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#clearkeyword" class="tsd-kind-icon">clear<wbr>Keyword</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#clearprefitlers" class="tsd-kind-icon">clear<wbr>Prefitlers</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#clearsearchfield" class="tsd-kind-icon">clear<wbr>Search<wbr>Field</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#clearselected" class="tsd-kind-icon">clear<wbr>Selected</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#clearselectedlist" class="tsd-kind-icon">clear<wbr>Selected<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#cleartagfilters" class="tsd-kind-icon">clear<wbr>Tag<wbr>Filters</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="jointsearchmanager.html#getinitparams" class="tsd-kind-icon">get<wbr>Init<wbr>Params</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#keyword" class="tsd-kind-icon">keyword</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#pageindex" class="tsd-kind-icon">page<wbr>Index</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#pagesize" class="tsd-kind-icon">page<wbr>Size</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#query" class="tsd-kind-icon">query</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#reset" class="tsd-kind-icon">reset</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#searchall" class="tsd-kind-icon">search<wbr>All</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#setactionid" class="tsd-kind-icon">set<wbr>Action<wbr>Id</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jointsearchmanager.html#setselected" class="tsd-kind-icon">set<wbr>Selected</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>