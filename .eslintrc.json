{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json"}, "env": {"es6": true}, "ignorePatterns": ["node_modules", "build", "coverage"], "plugins": ["import", "eslint-comments", "functional"], "extends": ["eslint:recommended", "plugin:eslint-comments/recommended", "plugin:@typescript-eslint/recommended", "plugin:import/typescript", "plugin:functional/lite", "prettier", "prettier/@typescript-eslint"], "globals": {"BigInt": true, "console": true, "WebAssembly": true, "uni": "readonly"}, "rules": {"@typescript-eslint/explicit-module-boundary-types": "off", "functional/no-class": "off", "functional/no-throw-statement": "off", "functional/no-this-expression": "off", "functional/prefer-readonly-type": "off", "functional/immutable-data": "off", "functional/no-let": "off", "functional/functional-parameters": "off", "@typescript-eslint/no-var-requires": "off", "prefer-spread": "off", "functional/no-return-void": "off", "functional/no-mixed-type": "off", "no-async-promise-executor": "off", "functional/no-loop-statement": "off", "@typescript-eslint/no-namespace": "off", "functional/no-method-signature": "off", "no-prototype-builtins": "off", "eslint-comments/disable-enable-pair": ["error", {"allowWholeFile": true}], "eslint-comments/no-unused-disable": "error", "import/order": ["error", {"newlines-between": "always", "alphabetize": {"order": "asc"}}], "sort-imports": ["error", {"ignoreDeclarationSort": true, "ignoreCase": true}]}}