<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Global | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="global.html">Global</a>
				</li>
			</ul>
			<h1>Class Global</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">Global</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="global.html#_sseurl" class="tsd-kind-icon">_SSEURL</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="global.html#_baseurl" class="tsd-kind-icon">_baseURL</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="global.html#_initdata" class="tsd-kind-icon">_init<wbr>Data</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="global.html#_rootentrance" class="tsd-kind-icon">_root<wbr>Entrance</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="global.html#_ssr" class="tsd-kind-icon">_ssr</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="global.html#memoryscene" class="tsd-kind-icon">memory<wbr>Scene</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class"><a href="global.html#sse" class="tsd-kind-icon">sse</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Accessors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="global.html#sseurl" class="tsd-kind-icon">SSEURL</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="global.html#baseurl" class="tsd-kind-icon">base<wbr>Url</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="global.html#initdata" class="tsd-kind-icon">init<wbr>Data</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="global.html#issuperadmin" class="tsd-kind-icon">is<wbr>Super<wbr>Admin</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="global.html#jwttoken" class="tsd-kind-icon">jwt<wbr>Token</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="global.html#jwtuserid" class="tsd-kind-icon">jwt<wbr>User<wbr>Id</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="global.html#projectname" class="tsd-kind-icon">project<wbr>Name</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="global.html#rootentrance" class="tsd-kind-icon">root<wbr>Entrance</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="global.html#ssr" class="tsd-kind-icon">ssr</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="global.html#storage" class="tsd-kind-icon">storage</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="global.html#uid" class="tsd-kind-icon">uid</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="global.html#username" class="tsd-kind-icon">username</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="global.html#clear" class="tsd-kind-icon">clear</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="global.html#clearjwttoken" class="tsd-kind-icon">clearJWTToken</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="global.html#getcurrenttoken" class="tsd-kind-icon">get<wbr>Current<wbr>Token</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="global.html#getscene4header" class="tsd-kind-icon">get<wbr>Scene4<wbr>Header</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="global.html#getxidtoken" class="tsd-kind-icon">get<wbr>Xid<wbr>Token</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="global.html#getxidtokens" class="tsd-kind-icon">get<wbr>Xid<wbr>Tokens</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="global.html#removexidtoken" class="tsd-kind-icon">remove<wbr>Xid<wbr>Token</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="global.html#setonetimescene" class="tsd-kind-icon">set<wbr>One<wbr>Time<wbr>Scene</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="global.html#setxidtoken" class="tsd-kind-icon">set<wbr>Xid<wbr>Token</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="global.html#setxidtokens" class="tsd-kind-icon">set<wbr>Xid<wbr>Tokens</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="_sseurl" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> _SSEURL</h3>
					<div class="tsd-signature tsd-kind-icon">_SSEURL<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &quot;&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/global.ts:29</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="_baseurl" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> _baseURL</h3>
					<div class="tsd-signature tsd-kind-icon">_baseURL<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &quot;&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/global.ts:28</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="_initdata" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> _init<wbr>Data</h3>
					<div class="tsd-signature tsd-kind-icon">_init<wbr>Data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol"> = JSON.parse(this.storage.getItem(INIT_DATA_STORAGE) || &quot;{}&quot;)</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/global.ts:33</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="_rootentrance" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> _root<wbr>Entrance</h3>
					<div class="tsd-signature tsd-kind-icon">_root<wbr>Entrance<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = this.storage.getItem(ROOT_ENTRANCE)</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/global.ts:30</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="_ssr" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> _ssr</h3>
					<div class="tsd-signature tsd-kind-icon">_ssr<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/global.ts:26</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="memoryscene" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> memory<wbr>Scene</h3>
					<div class="tsd-signature tsd-kind-icon">memory<wbr>Scene<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.Index.Scene</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span><span class="tsd-signature-symbol"> = null</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/global.ts:31</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class">
					<a name="sse" class="tsd-anchor"></a>
					<h3>sse</h3>
					<div class="tsd-signature tsd-kind-icon">sse<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/global.ts:32</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Accessors</h2>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="sseurl" class="tsd-anchor"></a>
					<h3>SSEURL</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> SSEURL<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> SSEURL<span class="tsd-signature-symbol">(</span>v<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:71</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:67</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>v: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="baseurl" class="tsd-anchor"></a>
					<h3>base<wbr>Url</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> baseUrl<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> baseUrl<span class="tsd-signature-symbol">(</span>v<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:63</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:59</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>v: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="initdata" class="tsd-anchor"></a>
					<h3>init<wbr>Data</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> initData<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../modules/index.html#initdata" class="tsd-signature-type">InitData</a></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> initData<span class="tsd-signature-symbol">(</span>v<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.Index.InitData</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:157</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <a href="../modules/index.html#initdata" class="tsd-signature-type">InitData</a></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:144</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>v: <span class="tsd-signature-type">dto.Index.InitData</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="issuperadmin" class="tsd-anchor"></a>
					<h3>is<wbr>Super<wbr>Admin</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> isSuperAdmin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> isSuperAdmin<span class="tsd-signature-symbol">(</span>v<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:140</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:136</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>v: <span class="tsd-signature-type">boolean</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="jwttoken" class="tsd-anchor"></a>
					<h3>jwt<wbr>Token</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> jwtToken<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> jwtToken<span class="tsd-signature-symbol">(</span>JwtToken<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:85</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:81</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>JwtToken: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="jwtuserid" class="tsd-anchor"></a>
					<h3>jwt<wbr>User<wbr>Id</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> jwtUserId<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:93</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="projectname" class="tsd-anchor"></a>
					<h3>project<wbr>Name</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> projectName<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> projectName<span class="tsd-signature-symbol">(</span>v<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:132</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:128</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>v: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="rootentrance" class="tsd-anchor"></a>
					<h3>root<wbr>Entrance</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> rootEntrance<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> rootEntrance<span class="tsd-signature-symbol">(</span>v<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:108</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:100</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>v: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="ssr" class="tsd-anchor"></a>
					<h3>ssr</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> ssr<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> ssr<span class="tsd-signature-symbol">(</span>ssr<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:45</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:37</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>ssr: <span class="tsd-signature-type">boolean</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="storage" class="tsd-anchor"></a>
					<h3>storage</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> storage<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Storage</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:49</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Storage</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="uid" class="tsd-anchor"></a>
					<h3>uid</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> uid<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> uid<span class="tsd-signature-symbol">(</span>v<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:124</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:120</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>v: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="username" class="tsd-anchor"></a>
					<h3>username</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> username<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> username<span class="tsd-signature-symbol">(</span>v<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:116</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:112</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>v: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clear" class="tsd-anchor"></a>
					<h3>clear</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:53</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearjwttoken" class="tsd-anchor"></a>
					<h3>clearJWTToken</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clearJWTToken<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:76</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getcurrenttoken" class="tsd-anchor"></a>
					<h3>get<wbr>Current<wbr>Token</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Current<wbr>Token<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:229</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getscene4header" class="tsd-anchor"></a>
					<h3>get<wbr>Scene4<wbr>Header</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Scene4<wbr>Header<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">ReadonlyArray</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/index.html#scene" class="tsd-signature-type">Scene</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:161</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">ReadonlyArray</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/index.html#scene" class="tsd-signature-type">Scene</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getxidtoken" class="tsd-anchor"></a>
					<h3>get<wbr>Xid<wbr>Token</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Xid<wbr>Token<span class="tsd-signature-symbol">(</span>xid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../globals.html#xidtokencache" class="tsd-signature-type">XidTokenCache</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:210</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>xid: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="../globals.html#xidtokencache" class="tsd-signature-type">XidTokenCache</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getxidtokens" class="tsd-anchor"></a>
					<h3>get<wbr>Xid<wbr>Tokens</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Xid<wbr>Tokens<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../globals.html#xidtokencache" class="tsd-signature-type">XidTokenCache</a><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:221</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <a href="../globals.html#xidtokencache" class="tsd-signature-type">XidTokenCache</a><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="removexidtoken" class="tsd-anchor"></a>
					<h3>remove<wbr>Xid<wbr>Token</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">remove<wbr>Xid<wbr>Token<span class="tsd-signature-symbol">(</span>xid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:201</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>xid: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="setonetimescene" class="tsd-anchor"></a>
					<h3>set<wbr>One<wbr>Time<wbr>Scene</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">set<wbr>One<wbr>Time<wbr>Scene<span class="tsd-signature-symbol">(</span>v<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.Index.InitData</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:174</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>设置一个内存级别的 initData Scene 缓存，优先级大于storage中存储的</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>v: <span class="tsd-signature-type">dto.Index.InitData</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="setxidtoken" class="tsd-anchor"></a>
					<h3>set<wbr>Xid<wbr>Token</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Xid<wbr>Token<span class="tsd-signature-symbol">(</span>xid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, token<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:178</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>xid: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>token: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="setxidtokens" class="tsd-anchor"></a>
					<h3>set<wbr>Xid<wbr>Tokens</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Xid<wbr>Tokens<span class="tsd-signature-symbol">(</span>items<span class="tsd-signature-symbol">: </span><a href="../globals.html#xidtokencache" class="tsd-signature-type">XidTokenCache</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/global.ts:194</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>items: <a href="../globals.html#xidtokencache" class="tsd-signature-type">XidTokenCache</a><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="global.html" class="tsd-kind-icon">Global</a>
						<ul>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="global.html#_sseurl" class="tsd-kind-icon">_SSEURL</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="global.html#_baseurl" class="tsd-kind-icon">_baseURL</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="global.html#_initdata" class="tsd-kind-icon">_init<wbr>Data</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="global.html#_rootentrance" class="tsd-kind-icon">_root<wbr>Entrance</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="global.html#_ssr" class="tsd-kind-icon">_ssr</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="global.html#memoryscene" class="tsd-kind-icon">memory<wbr>Scene</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class">
								<a href="global.html#sse" class="tsd-kind-icon">sse</a>
							</li>
							<li class=" tsd-kind-accessor tsd-parent-kind-class">
								<a href="global.html#sseurl" class="tsd-kind-icon">SSEURL</a>
							</li>
							<li class=" tsd-kind-accessor tsd-parent-kind-class">
								<a href="global.html#baseurl" class="tsd-kind-icon">base<wbr>Url</a>
							</li>
							<li class=" tsd-kind-accessor tsd-parent-kind-class">
								<a href="global.html#initdata" class="tsd-kind-icon">init<wbr>Data</a>
							</li>
							<li class=" tsd-kind-accessor tsd-parent-kind-class">
								<a href="global.html#issuperadmin" class="tsd-kind-icon">is<wbr>Super<wbr>Admin</a>
							</li>
							<li class=" tsd-kind-accessor tsd-parent-kind-class">
								<a href="global.html#jwttoken" class="tsd-kind-icon">jwt<wbr>Token</a>
							</li>
							<li class=" tsd-kind-get-signature tsd-parent-kind-class">
								<a href="global.html#jwtuserid" class="tsd-kind-icon">jwt<wbr>User<wbr>Id</a>
							</li>
							<li class=" tsd-kind-accessor tsd-parent-kind-class">
								<a href="global.html#projectname" class="tsd-kind-icon">project<wbr>Name</a>
							</li>
							<li class=" tsd-kind-accessor tsd-parent-kind-class">
								<a href="global.html#rootentrance" class="tsd-kind-icon">root<wbr>Entrance</a>
							</li>
							<li class=" tsd-kind-accessor tsd-parent-kind-class">
								<a href="global.html#ssr" class="tsd-kind-icon">ssr</a>
							</li>
							<li class=" tsd-kind-get-signature tsd-parent-kind-class">
								<a href="global.html#storage" class="tsd-kind-icon">storage</a>
							</li>
							<li class=" tsd-kind-accessor tsd-parent-kind-class">
								<a href="global.html#uid" class="tsd-kind-icon">uid</a>
							</li>
							<li class=" tsd-kind-accessor tsd-parent-kind-class">
								<a href="global.html#username" class="tsd-kind-icon">username</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="global.html#clear" class="tsd-kind-icon">clear</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="global.html#clearjwttoken" class="tsd-kind-icon">clearJWTToken</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="global.html#getcurrenttoken" class="tsd-kind-icon">get<wbr>Current<wbr>Token</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="global.html#getscene4header" class="tsd-kind-icon">get<wbr>Scene4<wbr>Header</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="global.html#getxidtoken" class="tsd-kind-icon">get<wbr>Xid<wbr>Token</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="global.html#getxidtokens" class="tsd-kind-icon">get<wbr>Xid<wbr>Tokens</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="global.html#removexidtoken" class="tsd-kind-icon">remove<wbr>Xid<wbr>Token</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="global.html#setonetimescene" class="tsd-kind-icon">set<wbr>One<wbr>Time<wbr>Scene</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="global.html#setxidtoken" class="tsd-kind-icon">set<wbr>Xid<wbr>Token</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="global.html#setxidtokens" class="tsd-kind-icon">set<wbr>Xid<wbr>Tokens</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>