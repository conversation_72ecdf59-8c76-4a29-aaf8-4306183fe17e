<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>JwtTokenChecker | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="jwttokenchecker.html">JwtTokenChecker</a>
				</li>
			</ul>
			<h1>Class JwtTokenChecker</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">JwtTokenChecker</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section tsd-is-private tsd-is-private-protected">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="jwttokenchecker.html#expiretime" class="tsd-kind-icon">expire<wbr>Time</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="jwttokenchecker.html#intervaltime" class="tsd-kind-icon">interval<wbr>Time</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="jwttokenchecker.html#safetime" class="tsd-kind-icon">safe<wbr>Time</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jwttokenchecker.html#clear" class="tsd-kind-icon">clear</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="jwttokenchecker.html#refreshtoken" class="tsd-kind-icon">refresh<wbr>Token</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="jwttokenchecker.html#start" class="tsd-kind-icon">start</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="jwttokenchecker.html#startcheck" class="tsd-kind-icon">start<wbr>Check</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="expiretime" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> expire<wbr>Time</h3>
					<div class="tsd-signature tsd-kind-icon">expire<wbr>Time<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/token-manager.ts:10</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="intervaltime" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagReadonly">Readonly</span> interval<wbr>Time</h3>
					<div class="tsd-signature tsd-kind-icon">interval<wbr>Time<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = 60 * 1e3</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/token-manager.ts:14</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>检查间隔时间</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="safetime" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagReadonly">Readonly</span> safe<wbr>Time</h3>
					<div class="tsd-signature tsd-kind-icon">safe<wbr>Time<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = 24 * 60 * 60 * 1e3</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/token-manager.ts:18</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>还剩多久时刷新token(一天)</p>
						</div>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clear" class="tsd-anchor"></a>
					<h3>clear</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/token-manager.ts:74</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="refreshtoken" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> refresh<wbr>Token</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">refresh<wbr>Token<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/token-manager.ts:31</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="start" class="tsd-anchor"></a>
					<h3>start</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">start<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/token-manager.ts:20</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="startcheck" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> start<wbr>Check</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">start<wbr>Check<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/token-manager.ts:53</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="jwttokenchecker.html" class="tsd-kind-icon">Jwt<wbr>Token<wbr>Checker</a>
						<ul>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="jwttokenchecker.html#expiretime" class="tsd-kind-icon">expire<wbr>Time</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="jwttokenchecker.html#intervaltime" class="tsd-kind-icon">interval<wbr>Time</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="jwttokenchecker.html#safetime" class="tsd-kind-icon">safe<wbr>Time</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jwttokenchecker.html#clear" class="tsd-kind-icon">clear</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="jwttokenchecker.html#refreshtoken" class="tsd-kind-icon">refresh<wbr>Token</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="jwttokenchecker.html#start" class="tsd-kind-icon">start</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="jwttokenchecker.html#startcheck" class="tsd-kind-icon">start<wbr>Check</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>