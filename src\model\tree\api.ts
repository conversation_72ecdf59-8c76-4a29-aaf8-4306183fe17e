import axios from "../../core/axios/index"
import { encodeParams } from "../../core/tools/form"
import type * as dto from "../../def/index"

/**
 * @deprecated 请使用TreeApi2
 */
export class TreeApi {
    constructor(private modelName: string) {}

    public getNodeAllAncestor<T>(node: string | number) {
        return axios.get<T>(
            `general/tree/${this.modelName}/node/${node}/all/ancestor`
        )
    }

    public getNode(node: string | number, parent: boolean) {
        return axios.get<dto.TreeTypes.Node>(
            `general/tree/${this.modelName}/${node}/get?parent=${parent}`
        )
    }

    public queryTreeWithMeta() {
        return axios.get<dto.TreeTypes.TreeWithMeta>(
            `general/tree/${this.modelName}/list/meta`
        )
    }

    public queryAllTreeList(q: {
        searchKeyword: string
        parent?: string | number
        prefilters?: dto.prefilters
        listName?: string
        searchProperties: string[]
        depth?: number
    }) {
        return axios.post<dto.TreeTypes.Node[]>(
            `general/tree/${this.modelName}/list`,
            q
        )
    }

    public queryTreeByRoot(
        node: string | number,
        summary: dto.TreeTypes.TreeSummary
    ) {
        let params = ""
        if (summary !== undefined) {
            params = `?summary=${encodeParams(summary)}`
        }
        return axios.get<dto.TreeTypes.Node[]>(
            `general/tree/${this.modelName}/${node}${params}`
        )
    }

    public queryTreeWithData(
        listTreeWithDataDef: dto.TreeTypes.ListTreeWithDataDef
    ) {
        let params = ""
        if (listTreeWithDataDef !== undefined) {
            params = `?params=${encodeParams(listTreeWithDataDef)}`
        }
        return axios.get<dto.TreeTypes.Node[]>(
            `general/tree/${this.modelName}/data${params}`
        )
    }

    public queryTreeLazy(parent: string | number, prefilters?: dto.prefilters) {
        const q = { parent, prefilters }
        return axios.post<dto.TreeTypes.Node[]>(
            `general/tree/${this.modelName}/list/lazy`,
            q
        )
    }

    public queryTreeLazyWithMeta(
        parent: string | number,
        listName: string,
        prefilters?: dto.prefilters
    ) {
        const q = { parent, listName, prefilters }
        return axios.post<dto.TreeTypes.TreeWithMeta>(
            `general/tree/${this.modelName}/list/lazy/meta`,
            q
        )
    }
}

export class TreeApi2 {
    constructor(private modelName: string) {}

    public getNodeAllAncestor<T>(node: string | number) {
        return axios.get<T>(
            `general/tree/${this.modelName}/node/${node}/all/ancestor`
        )
    }

    public getNode(node: string | number, parent: boolean) {
        return axios.get<dto.TreeTypes.Node2>(
            `general/tree/${this.modelName}/${node}/get/v2?parent=${parent}`
        )
    }

    public queryTreeWithMeta() {
        return axios.get<dto.TreeTypes.TreeWithMeta2>(
            `general/tree/${this.modelName}/list/meta/v2`
        )
    }

    public queryAllTreeList(q: {
        searchKeyword: string
        parent?: string | number
        prefilters?: dto.prefilters
        listName?: string
        searchProperties: string[]
        depth?: number
    }) {
        return axios.post<dto.TreeTypes.Node2[]>(
            `general/tree/${this.modelName}/list/v2`,
            q
        )
    }

    public queryTreeByRoot(
        node: string | number,
        summary: dto.TreeTypes.TreeSummary
    ) {
        let params = ""
        if (summary !== undefined) {
            params = `?summary=${encodeParams(summary)}`
        }
        return axios.get<dto.TreeTypes.Node2[]>(
            `general/tree/${this.modelName}/${node}/v2${params}`
        )
    }

    public queryTreeWithData(
        listTreeWithDataDef: dto.TreeTypes.ListTreeWithDataDef
    ) {
        let params = ""
        if (listTreeWithDataDef !== undefined) {
            params = `?params=${encodeParams(listTreeWithDataDef)}`
        }
        return axios.get<dto.TreeTypes.Node2[]>(
            `general/tree/${this.modelName}/data/v2${params}`
        )
    }

    public queryTreeLazy(parent: string | number, prefilters?: dto.prefilters) {
        const q = { parent, prefilters }
        return axios.post<dto.TreeTypes.Node2[]>(
            `general/tree/${this.modelName}/list/lazy/v2`,
            q
        )
    }

    public queryTreeLazyWithMeta(
        parent: string | number,
        listName: string,
        prefilters?: dto.prefilters
    ) {
        const q = { parent, listName, prefilters }
        return axios.post<dto.TreeTypes.TreeWithMeta2>(
            `general/tree/${this.modelName}/list/lazy/meta/v2`,
            q
        )
    }
}
