import type * as dto from "../../def/index"
import { Action } from "../action/action"

import ModelSchemaManagerApi from "./api"

export class ModelSchemaManager {
    private api: ModelSchemaManagerApi
    constructor(private params: dto.ModelSchemaManagerTypes.ConstructorParams) {
        this.api = new ModelSchemaManagerApi(params)
    }

    private schemeModelName = "newModelScheme"

    public async queryV1() {
        return this.api.queryV1()
    }

    public async query() {
        return this.api.query()
    }

    public async rename(params) {
        return this.api.rename(params)
    }

    public async setDefaultV2(schemeType, schemeId) {
        return this.api.setDefaultV2(schemeType, schemeId)
    }

    public async add(params: dto.ModelSchemaManagerTypes.addAPIParams) {
        return new Action({
            model_name: this.schemeModelName,
            action_name: "add",
        })
            .addInputs_parameter({
                type: this.params.type,
                target_name: this.params.targetName || "default",
                name: params.name,
                content: JSON.stringify(params.content),
                model_name: this.params.model_name,
            })
            .execute()
    }

    public del(id: number, v: number) {
        return new Action({
            model_name: this.schemeModelName,
            action_name: "delete",
        })
            .updateInitialParams({
                selected_list: [{ v, id }],
            })
            .execute()
    }

    public setDefault(id: number, v: number) {
        return new Action({
            model_name: this.schemeModelName,
            action_name: "set_default",
        })
            .updateInitialParams({
                selected_list: [{ v, id }],
            })
            .execute()
    }

    public removeDefault(id: number, v: number) {
        return new Action({
            model_name: this.schemeModelName,
            action_name: "set_default",
        })
            .updateInitialParams({
                selected_list: [{ v, id }],
            })
            .addInputs_parameter({
                is_default: "0",
            })
            .execute()
    }

    public save(params: dto.ModelSchemaManagerTypes.saveAPIParams) {
        return new Action({
            model_name: this.schemeModelName,
            action_name: "save",
        })
            .updateInitialParams({
                selected_list: [{ v: params.v, id: params.id }],
            })
            .addInputs_parameter({
                name: params.name,
                content: params.content,
            })
            .execute()
    }

    public share(schemaName: string, content: string, user_id: string) {
        return new Action({
            model_name: this.schemeModelName,
            action_name: "share",
        })
            .addInputs_parameter({
                user_id,
                model_name: this.params.model_name,
                type: this.params.type,
                name: schemaName,
                content,
                target_name: this.params.targetName,
            })
            .execute()
    }
}
