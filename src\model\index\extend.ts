import { global } from "../../core/global"
import {
    metaRow,
    metaRow2,
    SdkListRowPredict,
    SdkListRowPredictObject,
} from "../../def/index"
import { sanitizeData } from "../../helpers/xss"

export enum SdkListRowType {
    Default = 0,
    Number = 1,
    Boolean = 2,
    Array = 3,
}

const defaultValue = new Map<SdkListRowType, unknown>([
    [SdkListRowType.Array, []],
    [SdkListRowType.Number, 0],
    [SdkListRowType.Default, ""],
    [SdkListRowType.Boolean, false],
])

export type PrevertXssConfig = {
    // ['b.c', 'b.d[1]']
    whitelist?: string[]
}

export class UniplatSdkExtender {
    /**
     * 转换复杂对象数组到普通对象数组
     * @param rows
     * @param predicts 你可以传入一个数组来构造新对象，或者传入一个对象以作为映射配置如
     * 键值将作为构造的对象键值，value 会根据配置的项动态
     * {
     *   id: '',
     *   age: 10 (默认0，如果不是0会以输入的值作为默认值),
     *   male: false,
     *   status: 'label' (拥有label的话会额外增添_label属性),
     *   aliasName: 'Model#Name#Id', (如果不是label，尝试别名匹配)
     *   aliasNameWithLabel: 'Model#Name#Id_label'
     * }
     */
    public buildRows<T>(
        rows: metaRow[] | metaRow2[],
        predicts: SdkListRowPredict[] | SdkListRowPredictObject,
        prevertXss?: PrevertXssConfig
    ): T[] {
        const items: T[] = []
        for (const item of rows) {
            items.push(this.buildRow<T>(item, predicts, prevertXss))
        }
        return items
    }

    private buildRowFieldGroups(
        o: any,
        row: metaRow2 | metaRow,
        prevertXss?: PrevertXssConfig
    ) {
        if (row.fieldGroups) {
            for (const p in row.fieldGroups) {
                if (o[p] === undefined) {
                    o[p] = row.fieldGroups[p].value
                }
            }
        }
        
        // 只要配置了prevertXss 就进行xss过滤，也可以配置ignores进行过滤
        if (prevertXss) {
            return sanitizeData(o, prevertXss.whitelist)
        }
        return o
    }

    /**
     * 转换复杂对象到普通对象
     * @param item
     * @param predicts  @see UniplatSdkExtender.buildRows
     */
    public buildRow<T>(
        row: metaRow | metaRow2,
        predicts: SdkListRowPredict[] | SdkListRowPredictObject,
        prevertXss?: PrevertXssConfig
    ) {
        const o = {} as unknown
        const r2 = row as metaRow2
        if (r2.objectData) {
            Object.assign(o, r2.objectData)
            Object.assign(o, {
                id: r2.keyValue,
                v: r2.uniplatVersion,
            })
            return this.buildRowFieldGroups(o, row, prevertXss) as T
        }
        const item = row as metaRow
        if (predicts instanceof Array) {
            for (const p of predicts) {
                const v = item[p.value] as {
                    value: unknown
                    display: string | unknown
                }
                const t = p.type || SdkListRowType.Default
                const k = p.alias ?? p.value
                if (k === "tags") {
                    o[k] = v || {}
                    continue
                }
                o[k] = (v && v.value) ?? defaultValue.get(+t)
                if (p.label) {
                    o[k + "_label"] = v && v.display
                }
            }
        } else {
            for (const p in predicts) {
                let v = item[p] as {
                    value: unknown
                    display: string | unknown
                }
                const pv = predicts[p]
                if ((pv + "").includes("__")) {
                    v = item[(pv + "").split("__")[0] || p] as {
                        value: unknown
                        display: string | unknown
                    }
                }
                if (pv === "") {
                    o[p] = (v && v.value) || ""
                    continue
                }
                if (+pv === pv) {
                    o[p] = (v && v.value) ?? pv
                    continue
                }
                if (pv instanceof Array) {
                    o[p] = (v && v.value) ?? []
                    continue
                }
                if (pv === true || pv === false) {
                    o[p] = (v && v.value) ?? pv
                    continue
                }
                if (pv === "label") {
                    o[p] = v && v.value
                    o[p + "_label"] = v && v.display
                    continue
                }
                const k = (pv + "").replace("_label", "")
                const alias = item[k] as {
                    value: unknown
                    display: string | unknown
                }
                if (pv === "tags") {
                    o[p] = v || {}
                    continue
                }
                if (pv === "object_data") {
                    o[p] = v || {}
                    continue
                }
                if (alias) {
                    o[p] = alias.value
                    if ((pv + "").indexOf("label") > -1) {
                        o[p + "_label"] = alias.display
                    }
                    continue
                }
                if ((pv + "").includes("__files")) {
                    if (!v) {
                        o[p] = []
                    } else {
                        const files = (v.value + "")
                            .split(",")
                            .filter((i) => i)
                            .map((i) => `${global.baseUrl}${i}`)
                        o[p] = files
                    }
                    continue
                }
                if ((pv + "").includes("__file")) {
                    if (!v) {
                        o[p] = ""
                    } else {
                        o[p] = v.value ? `${global.baseUrl}${v.value}` : ""
                    }
                    continue
                }
            }

            if (!o["id"] && item.id) {
                o["id"] = item.id.value
            }
            if (!o["_access_key"] && item._access_key) {
                o["_access_key"] = item._access_key.value
            }
            if (!o["v"] && item.uniplat_version) {
                o["v"] = item.uniplat_version.value
            }
        }
        return this.buildRowFieldGroups(o, row, prevertXss) as T
    }

    public buildActionParameter(parameter: { [key: string]: unknown }) {
        const p: { property: string; value: unknown }[] = []
        for (const o in parameter) {
            p.push({ property: o, value: parameter[o] })
        }
        return p
    }

    public convertMaster2Object(
        items: {
            type: string
            property: string
            default_value: string | number
            ext_properties: {
                mapping: {
                    mapping_values: { key: string; value: string | number }[]
                }
            }
        }[]
    ) {
        const o: unknown = {}
        for (const item of items) {
            if (item.type === "text") {
                o[item.property] = item.default_value
                continue
            }
            if (
                item.type === "mapping" &&
                item.ext_properties &&
                item.ext_properties.mapping
            ) {
                o[item.property] = item.ext_properties.mapping.mapping_values
                continue
            }
            o[item.property] = item.default_value
        }
        return o
    }
}
