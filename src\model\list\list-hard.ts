import type * as dto from "../../def/index"

import { List, List2 } from "./list"

export class ListHard extends List {
    public query(params: dto.ListTypes.queryPropsHard) {
        return super.query(params)
    }

    public queryMeta(params: dto.ListTypes.queryPropsHard) {
        return super.queryMeta(params)
    }
}

export class ListHard2 extends List2 {
    public query(params: dto.ListTypes.queryPropsHard) {
        return super.query(params)
    }

    public queryMeta(params: dto.ListTypes.queryPropsHard) {
        return super.queryMeta(params)
    }
}
