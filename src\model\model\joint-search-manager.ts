import type * as dto from "../../def/index"

import type { ModelApi } from "./api"
/**
 *  用于JoinstSearch搜索，封装了复杂的参数结构，提供了方法以操作参数。
 * 均可链式调用
 */
export class JointSearchManager {
    private requestParams: dto.ActionTypes.JointSearchParams
    constructor(private joint_name: string, private api: ModelApi) {
        this.requestParams = this.getInitParams()
    }
    private getInitParams(): dto.ActionTypes.JointSearchParams {
        return {
            search_field: "all",
            keyword: "",
            page_index: 0,
            selected: [],
            actionId: "",
            form_params: {},
            tagFilters: [],
            selected_list: [],
            prefilters: [],
        }
    }
    /**
     * 关键词，默认是空字符串
     * @param keyword
     */

    public keyword(keyword: string) {
        this.requestParams.keyword = keyword
        return this
    }
    /**
     * 设置已选项的 id数组
     * @param ids
     */
    public setSelected(ids: number[]) {
        this.requestParams.selected = ids
        return this
    }
    /**
     * 设置搜索模式为全部搜索
     */
    public searchAll() {
        this.requestParams.search_field = "all"
        return this
    }
    /**
     * 设置搜索模式。
     * @param property
     */
    public addSearchField(property: string) {
        this.requestParams.search_field = property
        return this
    }
    /**
     * 每页多少条数据
     * @param index
     */
    public pageSize(size: number) {
        this.requestParams.pageSize = size
        return this
    }
    /**
     * 第几页
     * @param index
     */
    public pageIndex(index: number) {
        this.requestParams.page_index = index
        return this
    }
    /**
     * 添加标签
     * @param tags
     */
    public addTagFilters(tags: dto.TagManagerTypes.TagFilter[]) {
        this.requestParams.tagFilters = tags
        return this
    }
    /**
     * 添加selected_list
     * @param selected_list
     */
    public addSelectedList(selected_list: dto.ActionTypes.selectedList[]) {
        this.requestParams.selected_list = selected_list
        return this
    }
    /**
     * 设置actionId
     * @param actionId
     */
    public setActionId(actionId: string) {
        this.requestParams.actionId = actionId
        return this
    }
    /**
     * 添加额外参数
     * @param formParam
     */
    public addExtraFormData(formParam: unknown) {
        this.requestParams.form_params = formParam
        return this
    }
    /**
     * 添加prefilters
     * @param rawPrefilters
     */
    public addPrefilters(prefilters: dto.prefilters) {
        this.requestParams.prefilters = prefilters
        return this
    }
    /**
     * 清空prefilters
     */
    public clearPrefitlers() {
        this.requestParams.prefilters = []
        return this
    }
    /**
     * 执行搜索
     */
    public query() {
        return this.api.jointSearch(this.joint_name, this.requestParams)
    }
    /**
     * 重置关键词
     */
    public clearKeyword() {
        this.requestParams.keyword = ""
        return this
    }
    /**
     * 重置已选项
     */
    public clearSelected() {
        this.requestParams.selected = []
        return this
    }
    /**
     * 重置slelected_list
     */
    public clearSelectedList() {
        this.requestParams.selected_list = []
        return this
    }
    /**
     * 重置搜索模式为全部搜索
     */
    public clearSearchField() {
        return this.searchAll()
    }

    /**
     * 清空标签
     */
    public clearTagFilters() {
        this.requestParams.tagFilters = []
        return this
    }
    /**
     * 清空额外参数
     */
    public clearExtraFormData() {
        this.requestParams.form_params = {}
        return this
    }
    /**
     * 重置所有参数
     */
    public reset() {
        this.requestParams = this.getInitParams()
        return this
    }
}
