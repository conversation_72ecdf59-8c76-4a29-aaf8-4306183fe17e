import { events } from "../../core/events"
import type { QueryObject } from "../../core/object2query"
import type * as dto from "../../def/index"
import { Action } from "../action/action"
import { Chat } from "../chat/chat"
import { Detail, Detail2 } from "../detail/detail"
import { ListEasy, ListEasy2 } from "../list/list-easy"
import { ListHard, ListHard2 } from "../list/list-hard"

import { ModelApi } from "./api"
import { JointSearchManager } from "./joint-search-manager"
type ModelApiType = InstanceType<typeof ModelApi>
export class Model {
    private api: ModelApi
    constructor(private name: string) {
        this.api = new ModelApi(this.name)
    }

    private callbackOnChange = (msg: dto.SSE.msg) => {
        if (msg) {
            return null
        }
    }

    public async toolbalEnhancerValid<T>(behavior: string, jsonStr: string) {
        return this.api.toolbarEnhancerValid<T>(behavior, jsonStr)
    }

    /**
     * @deprecated(请使用rawList2)
     */
    public rawList(listName?: string) {
        const param: { model_name: string; list_name?: string } = {
            model_name: this.name,
        }
        if (listName) {
            param.list_name = listName
        }
        return new ListHard(param)
    }

    public rawList2(listName?: string) {
        const param: { model_name: string; list_name?: string } = {
            model_name: this.name,
        }
        if (listName) {
            param.list_name = listName
        }
        return new ListHard2(param)
    }

    /**
     * @deprecated(请使用list2)
     */
    public list<RowType extends dto.ListRow = dto.ListRow>(listName?: string) {
        const param: { model_name: string; list_name?: string } = {
            model_name: this.name,
        }
        if (listName) {
            param.list_name = listName
        }
        return new ListEasy<RowType>(param)
    }

    public list2<RowType extends dto.ListRow = dto.ListRow>(listName?: string) {
        const param: { model_name: string; list_name?: string } = {
            model_name: this.name,
        }
        if (listName) {
            param.list_name = listName
        }
        return new ListEasy2<RowType>(param)
    }

    /**
     * @deprecated(请使用detail2)
     */
    public detail<
        RowType extends dto.ListRow = dto.ListRow,
        LogRow extends dto.DetailTypes.GetLogsRequestRow = dto.DetailTypes.GetLogsRequestRow
    >(keyValue: string | number, detailName?: string) {
        return new Detail<RowType, LogRow>({
            model_name: this.name,
            keyValue,
            detailName,
        })
    }

    public detail2<
        LogRow extends dto.DetailTypes.GetLogsRequestRow = dto.DetailTypes.GetLogsRequestRow
    >(keyValue: string | number, detailName?: string) {
        return new Detail2<LogRow>({
            model_name: this.name,
            keyValue,
            detailName,
        })
    }

    public chat(id: number | string, orgID: string) {
        return new Chat(this.name, id, orgID)
    }

    public action(actionName: string) {
        return new Action({
            model_name: this.name,
            action_name: actionName,
        })
    }
    /**
     * 用于级联懒加载
     * @param name
     * @param form_params
     * @param selected_list
     * @param suffixData
     */
    public mappingFetch(params: dto.ModelTypes.mappingFetchAPIParams) {
        return this.api.mappingFetch(params)
    }

    public jointSearch(joint_name: string) {
        return new JointSearchManager(joint_name, this.api)
    }

    public intentSearch(
        intentName: string,
        params: { actionId: string; form_params: unknown }
    ) {
        return this.api.intentSearch(intentName, params)
    }

    public groupSearch(params: dto.ModelTypes.groupSearchApiParams) {
        return this.api.groupSearch(params)
    }

    public getForwardURL(forward: string, params?: unknown) {
        return this.api.getForwardURL(forward, params)
    }

    public getRequest<T>(apiName: string, query: QueryObject | string) {
        return this.api.getModelRequest<T>(apiName, query)
    }

    public postModelRequest<T>(apiName: string, params: FormData) {
        return this.api.postModelRequest<T>(apiName, params)
    }

    /**
     * 用户时间filter的自定义按钮。
     * 根据时间的标识符返回符合当下的具体时间
     */
    public getTimeByIdentifier(startIdentifier: string, endIdentifier: string) {
        return Promise.all([
            this.api.getTimeByIdentifier(startIdentifier),
            this.api.getTimeByIdentifier(endIdentifier),
        ])
    }

    public getRemarkList(params: Parameters<ModelApiType["getRemarkList"]>[0]) {
        return this.api.getRemarkList(params)
    }

    /**
     * 注册当前模型的数据变化时的回掉
     * @param CallBackOfModelUpdated
     */
    public registerOnChange(cb: dto.SSE.callBackOfModelUpdatedFullMsg) {
        this.callbackOnChange = cb
        return events.addTransportMessageListener(this.onTransportMessage)
    }

    private onTransportMessage = (msg: dto.SSE.msg) => {
        const models = msg.dataUpdates.filter((x) => x.model === this.name)
        if (models.length === 0) return
        this.callbackOnChange && this.callbackOnChange(msg)
    }

    public getHistoryVersion(objectId: string | number, field: string) {
        return this.api.getHistoryVersion(objectId, field)
    }

    public superCascaderFetch(
        params: dto.ModelTypes.SuperCascaderFetchApiParams
    ) {
        return this.api.superCascaderFetch(params)
    }

    public superCascaderSearch(
        params: dto.ModelTypes.SuperCascaderSearchApiParams
    ) {
        return this.api.superCascaderSearch(params)
    }

    public getSection(sectionName: string) {
        return this.api.getSection(sectionName)
    }

    public getMemberSelectOptionalValue(action: string) {
        return this.api.getTimeByIdentifier(action)
    }
}
