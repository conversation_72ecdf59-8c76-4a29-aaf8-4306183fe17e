// eslint-disable-next-line no-undef
const UglifyJsPlugin = require("uglifyjs-webpack-plugin")

function buildEntry(items) {
    const o = {}
    for (const item of items) {
        const l = item.split("/")
        const name = l[l.length - 1].replace(".ts", "")
        o[name + ".min.js"] = item
    }
    return o
}

// eslint-disable-next-line no-undef
module.exports = (env, options) => {
    const pro = options.mode === "production"
    const plugins = []

    if (pro) {
        plugins.push(new UglifyJsPlugin())
    }

    return {
        devtool: pro ? "hidden-source-map" : "cheap-source-map",
        entry: buildEntry(["./src/mjs.ts"]),
        output: { filename: "[name]", libraryTarget: "umd" },
        plugins: plugins,
        resolve: { extensions: [".ts", ".js"] },
        module: {
            rules: [
                {
                    test: /\.ts$/,
                    use: [
                        {
                            loader: "ts-loader",
                            options: {
                                configFile: "tsconfigmjs.json",
                            },
                        },
                    ],
                },
            ],
        },
    }
}
