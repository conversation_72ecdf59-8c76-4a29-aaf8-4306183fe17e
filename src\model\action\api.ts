import { Anony<PERSON><PERSON><PERSON> } from "../../core/anonymous-api"
import axios from "../../core/axios/index"
import { global } from "../../core/global"
import { encodeParamsWithCrypto } from "../../core/tools/form"
import type * as dto from "../../def/index"
const version_control = true
export default class ActionApi extends AnonymousApi {
    constructor(private model_name: string, private action_name: string) {
        super()
    }

    public async bigActionSignal(taskId: number, signal: string) {
        return axios.get<dto.ActionTypes.BigActionDetailResult>(
            `${this.urlPrefix}/model/${this.model_name}/action/${this.action_name}/bigAction/${taskId}/signal/${signal}`
        )
    }

    public async bigActionDetail(query: dto.ActionTypes.BigActionDetailQuery) {
        return axios.post<dto.ActionTypes.BigActionDetailResult>(
            `${this.urlPrefix}/model/${this.model_name}/action/${this.action_name}/bigAction/detail`,
            query
        )
    }

    public async bigActionCheck() {
        return axios.post<dto.ActionTypes.BigActionCheckResp>(
            `${this.urlPrefix}/model/${this.model_name}/action/${this.action_name}/bigAction/check`
        )
    }

    public bigActionFileUrl() {
        return `${this.urlPrefix}/model/${this.model_name}/action/${this.action_name}/bigAction/file`
    }

    public async bigActionImport(param) {
        return await axios.post(
            `${this.urlPrefix}/model/${this.model_name}/action/${this.action_name}/bigAction`,
            param
        )
    }

    public async getActionDetail(parameters: {
        selected_list: dto.ActionTypes.selectedList[]
        prefilters: dto.prefilter[]
        workflowExecuteContext?: dto.workflow2.WorkflowExecuteContext
        intent?: dto.ActionIntent
    }) {
        return await axios.post<dto.ActionTypes.info>(
            `${this.urlPrefix}/model/${this.model_name}/action/${this.action_name}/property2`,
            {
                parameters: JSON.stringify(parameters),
            }
        )
    }

    public async executeAction(
        parameters: {
            selected_list: dto.ActionTypes.selectedList[]
            prefilters: dto.prefilters
            actionId: string
        } & dto.ActionTypes.executeParams
    ) {
        return await axios.post<
            { [key: string]: unknown } & { id: string; ids: string[] }
        >(
            `${this.urlPrefix}/model/${this.model_name}/action2/${this.action_name}/execute`,
            { ...parameters, version_control }
        )
    }

    public createEachActionSSEURL(
        parameters: {
            prefilters: dto.prefilters
            actionId: string
            name?: string
        } & dto.ActionTypes.executeEachParams
    ) {
        return `${global.baseUrl}${this.urlPrefix}/model/sse/${
            this.model_name
        }/action/${
            this.action_name
        }/execute/each?parameters=${encodeParamsWithCrypto(parameters)}`
    }

    public async validateBatchAction(
        parameters: dto.ActionTypes.validateBatchParams & {
            prefilters: dto.prefilters
            actionId: string
        }
    ) {
        return await axios.post<dto.ActionTypes.validateRequestResult>(
            `${this.urlPrefix}/model/${this.model_name}/action2/${this.action_name}/validate`,
            { ...parameters, version_control }
        )
    }

    public async executeBatchAction(
        parameters: {
            selected_list: dto.ActionTypes.selectedList[]
            prefilters: dto.prefilters
            list_parameters: dto.ActionTypes.list_parameter[]
            tagInfosForList: dto.ActionTypes.TagInfos[]
            actionId: string
        } & dto.ActionTypes.executeParams
    ) {
        return await axios.post(
            `${this.urlPrefix}/model/${this.model_name}/action2/${this.action_name}/execute`,
            { ...parameters, version_control }
        )
    }

    public getAuthorsList() {
        return axios.get<dto.ActionTypes.getAuthorsListRequestResult>(
            `${this.urlPrefix}/accept_auth/usersOfAction/${this.model_name}/${this.action_name}`
        )
    }

    public getDataSource(params: dto.ActionTypes.getDataSourceRequestParams) {
        const query = params.additionQuery
            ? require("qs").stringify(params.additionQuery, {
                  arrayFormat: "repeat",
              })
            : ""
        return axios.get<dto.ActionTypes.getDataSourceRequestResult>(
            `${this.urlPrefix}/model/${this.model_name}/request/${params.funcName}/?${query}`
        )
    }

    public getBatchExcelTemplate(schemaName: string) {
        return axios.get<string>(
            `${this.urlPrefix}/model/${this.model_name}/action/${this.action_name}/exportSchema/${schemaName}`
        )
    }

    public updateControlsProperties(
        property: string,
        params: {
            selected_list: dto.ActionTypes.selectedList[]
            prefilters: dto.prefilters
            actionId: string
        } & dto.ActionTypes.executeParams
    ) {
        return axios.post<dto.ActionTypes.updateControlsPropertiesRequestResult>(
            `${this.urlPrefix}/model/${this.model_name}/action2/${
                this.action_name
            }/update/${encodeURIComponent(property)}`,
            { ...params, version_control }
        )
    }

    public validateInput(
        property: string,
        parameters: {
            selected_list: dto.ActionTypes.selectedList[]
            prefilters: dto.prefilters
            actionId: string
        } & dto.ActionTypes.executeParams
    ) {
        return axios.post<dto.ActionTypes.ValidatorInputerResult>(
            `${this.urlPrefix}/model/${this.model_name}/action2/${this.action_name}/${property}/validate`,
            { ...parameters, version_control }
        )
    }

    public clearAction(actionId: string) {
        return axios.post<void>(
            `${this.urlPrefix}/model/action/${actionId}/clear`
        )
    }

    public executeInnerAction<T>(
        actionName: string,
        parameters: {
            inputs_parameters: dto.ActionTypes.inputs_parameters[]
            selected_list: dto.ActionTypes.selectedList[]
        }
    ) {
        const d = new FormData()
        d.append("parameters", JSON.stringify(parameters))
        return axios.post<T>(
            `${this.urlPrefix}/model/${this.model_name}/action/${this.action_name}/update/${actionName}`,
            d
        )
    }
}
