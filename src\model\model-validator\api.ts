import axios from "../../core/axios/index"
import type * as dto from "../../def/index"

export default class ModelValidatorApi {
    constructor(private dataModelId: string) {}
    public validate() {
        return axios.post<dto.ModelValidatorTypes.ModelValidations>(
            `general/configs/validate/${this.dataModelId}`
        )
    }

    public replaceGroovyWithJson() {
        return axios.post<dto.ModelValidatorTypes.ModelValidations>(
            `general/configs/replaceGroovyWithJson/${this.dataModelId}`
        )
    }

    public execute(actionId: string, dataId: string) {
        return axios.post<dto.ModelValidatorTypes.ModelValidations>(
            `general/configs/${this.dataModelId}/${actionId}/${dataId}`
        )
    }
}
