import axios from "../../core/axios/index"
import { global } from "../../core/global"
export class gatewayApi {
    private get isAnonymous() {
        return !!global.jwtToken
    }
    constructor(private gatewayName: string, private apiName: string) {}

    public request<ParamsType, ReturnType>(params: ParamsType) {
        return axios.post<ReturnType>(
            `general/gateway/${this.isAnonymous ? "anonymous/" : ""}${
                this.gatewayName
            }/${this.apiName}`,
            params
        )
    }
}
