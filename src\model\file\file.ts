import type * as dto from "../../def/index"
import { List } from "../list/list"

import FileApi from "./api"

export class FileManager {
    private api: FileApi
    private _list?: List

    constructor(private model_name: string) {
        this.api = new FileApi(model_name)
    }

    public async listFileDomainTree(): Promise<dto.FileModel.DomainTree[]> {
        return this.api.fetchFileDomainTree()
    }

    public async listFileDatasources(): Promise<dto.FileModel.DataSources[]> {
        return this.api.fetchFileDatasources()
    }

    public async getFileListMeta(
        parameters: dto.ListTypes.queryPropsHard
    ): Promise<dto.ListTypes.getListDataRequestResult> {
        const param: { model_name: string; list_name?: string } = {
            model_name: this.model_name,
        }
        this._list = new List(param)
        const listQueryParams = this._list.fullfillParams(parameters)
        return this.api.fetchListMeta(listQueryParams)
    }
}
