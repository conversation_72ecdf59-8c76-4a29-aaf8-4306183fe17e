/* eslint-disable @typescript-eslint/no-explicit-any */
import type * as dto from "../../def/index"

import { isSimpleRelationshipFilter } from "./../filters-filter"

function trim(str: string) {
    if (!str) {
        return ""
    }
    return str.replace(/^\s+|\s+$/gm, "")
}

export function buildFilters(
    filters: dto.ListTypes.getListDataRequestResult["meta"]["filters"],
    prefilter: dto.prefilters
) {
    return filters
        .filter((x) => {
            /**
             * SimpleRelationshipFilte肯定和prefilter没关系
             */
            if (isSimpleRelationshipFilter(x)) {
                return true
            }
            return prefilter.map((x) => x.property).indexOf(x.full_property) < 0
        })
        .map((x, index) => metaFilter(x, index, ""))
}

const alias = [
    "去年",
    "上季度",
    "上月",
    "上周",
    "昨天",
    "本周",
    "本月",
    "本季度",
    "今年",
]

function buildDateString(d: Date, minute: boolean) {
    const v = `${d.getFullYear()}-${d.getMonth() < 9 ? "0" : ""}${
        d.getMonth() + 1
    }-${d.getDate() < 10 ? "0" : ""}${d.getDate()}`
    return minute ? `${v} 00:00:00` : v
}

const oneDay = 24 * 60 * 60 * 1000

function convert2Range(v: string, minute: boolean) {
    const start = new Date()
    const end = new Date()
    if (v === "去年") {
        start.setFullYear(start.getFullYear() - 1, 0, 1)
        end.setFullYear(start.getFullYear() - 1, 11, 31)
        return [buildDateString(start, minute), buildDateString(end, minute)]
    }
    if (v === "上季度") {
        const m = start.getMonth() + 1
        const q1 = m <= 3
        const q2 = m > 3 && m <= 6
        const q3 = m > 6 && m <= 9
        if (q1) {
            const y = start.getFullYear()
            start.setFullYear(y - 1)
            start.setMonth(9)
            start.setDate(1)
            end.setFullYear(y - 1)
            end.setMonth(11)
            end.setDate(31)
        } else if (q2) {
            start.setMonth(0)
            start.setDate(1)
            end.setMonth(2)
            end.setDate(31)
        } else if (q3) {
            start.setMonth(3)
            start.setDate(1)
            end.setMonth(5)
            end.setDate(30)
        } else {
            start.setMonth(6)
            start.setDate(1)
            end.setMonth(8)
            end.setDate(30)
        }
        return [buildDateString(start, minute), buildDateString(end, minute)]
    }
    if (v === "上月") {
        start.setTime(start.getTime() - start.getDate() * oneDay)
        start.setDate(1)
        end.setTime(end.getTime() - end.getDate() * oneDay)
        return [buildDateString(start, minute), buildDateString(end, minute)]
    }
    if (v === "上周") {
        start.setTime(start.getTime() - (start.getDay() + 7 - 1) * oneDay)
        end.setTime(end.getTime() - end.getDay() * oneDay)
        return [buildDateString(start, minute), buildDateString(end, minute)]
    }
    if (v === "昨天") {
        start.setTime(start.getTime() - oneDay)
        return [buildDateString(start, minute), buildDateString(end, minute)]
    }
    if (v === "本周") {
        start.setTime(start.getTime() - oneDay * (start.getDay() - 1))
        return [buildDateString(start, minute), buildDateString(end, minute)]
    }
    if (v === "本月") {
        start.setTime(start.getTime() - oneDay * (start.getDate() - 1))
        return [buildDateString(start, minute), buildDateString(end, minute)]
    }
    if (v === "本季度") {
        const m = start.getMonth() + 1
        const q1 = m <= 3
        const q2 = m > 3 && m <= 6
        const q3 = m > 6 && m <= 9
        if (q1) {
            start.setMonth(0)
            start.setDate(1)
            end.setMonth(2)
            end.setDate(31)
        } else if (q2) {
            start.setMonth(3)
            start.setDate(1)
            end.setMonth(5)
            end.setDate(30)
        } else if (q3) {
            start.setMonth(6)
            start.setDate(1)
            end.setMonth(8)
            end.setDate(30)
        } else {
            start.setMonth(9)
            start.setDate(1)
            end.setMonth(11)
            end.setDate(31)
        }
        return [buildDateString(start, minute), buildDateString(end, minute)]
    }
    start.setFullYear(start.getFullYear(), 0, 1)
    return [buildDateString(start, minute), buildDateString(end, minute)]
}

function convertDateTimeDefaultValue(v: string, minute: boolean) {
    if (v) {
        if (alias.includes(v)) {
            return v || convert2Range(v, minute)
        }
        return JSON.parse(v)
    }
    return []
}

export function metaFilter(filter: any, index: number, pageName: string) {
    const x = filter
    const res: any = {
        ...x,
        label: x.label,
        type: x.type,
        property: x.full_property,
        width: x.width,
        hint: x.hint,
        group: x.group,
        min: "",
        max: "",
        ext_properties: x.ext_properties,
        status: "",
        trim: x.trim || false,
        pageName: pageName,
        visible: !pageName,
        is_param: x.is_param || false,
        treeModelName: x.treeModelName,
        optionalValues: x.optionalValues,
        strategy: x.strategy || "",
    }

    res["match"] = x.match || "exact"

    if (["date"].includes(x.type)) {
        const d = []
        if (x.defaultValue == "") {
            res["status"] = d
        } else {
            res["status"] = convertDateTimeDefaultValue(x.defaultValue, false)
        }
    } else if (["datetime"].includes(x.type)) {
        const dt = []
        if (x.defaultValue == "") {
            res["status"] = dt
        } else {
            console.log("datetime default value", x.defaultValue)
            res["status"] = convertDateTimeDefaultValue(x.defaultValue, true)
        }
    } else if (x.type == "text-date") {
        res["status"] = x.defaultValue || ""
    } else if (x.type == "text-month" || x.type === "text-year") {
        res["status"] = x.defaultValue || ""
    } else if (x.type == "search") {
        if (x.defaultValue == "") {
            res["status"] = {
                index: index,
                include: false,
                range: [],
            }
        } else {
            const range = JSON.parse(x.defaultValue)
            res["status"] = {
                index: index,
                include: true,
                range: range.map((id) => {
                    return {
                        id,
                        checked: true,
                    }
                }),
            }
        }
    } else if (
        x.type == "enum" ||
        x.type == "cascader" ||
        x.type == "checkbox-group"
    ) {
        if (x.defaultValue == "") {
            res["status"] = []
        } else {
            res["status"] = JSON.parse(x.defaultValue)
        }
    } else if (x.type === "boolean") {
        res["status"] = x.defaultValue
    } else if (x.type === "text") {
        res["status"] = x.defaultValue
    } else if (x.type === "cascader") {
        res["status"] = []
        // eslint-disable-next-line no-empty
    } else if (x.type === "combo_text") {
    } else if (x.type === "fulltext") {
        res["status"] = x.defaultValue
    } else if (x.type === "tree") {
        // tree 需要 index 这个属性来定位 filter
        res.status = {
            index: index,
            selectedList: [],
            innerDisplay: "",
        }
    }
    return res
}

export function getAllFilters(
    filters: any[],
    slave_filters?: dto.Tools.SlaveFilterItem[]
): dto.Tools.FiltersType {
    const visibleFilters = filters.filter((item: any) => item.visible)
    const o = {
        date_filters: visibleFilters
            .filter((x: any) => ["date"].includes(x.type))
            .map((x: any) => {
                return {
                    property: x.property,
                    start: x.status[0],
                    end: x.status[1],
                    is_param: x.is_param || undefined,
                    customSqlClauseBuilder: x.customSqlClauseBuilder,
                }
            }),
        datetime_filters: visibleFilters
            .filter((x: any) => ["datetime"].includes(x.type))
            .map((x: any) => {
                return {
                    property: x.property,
                    start: x.status[0],
                    end: x.status[1],
                    is_param: x.is_param || undefined,
                    customSqlClauseBuilder: x.customSqlClauseBuilder,
                }
            }),
        text_filters: visibleFilters
            .filter(
                (x: any) =>
                    x.type == "text" ||
                    x.type == "text-date" ||
                    x.type == "text-month" ||
                    x.type == "text-year" ||
                    x.type == "enum_radio"
            )
            .filter((x: any) => x.status && x.status != "")
            .map((x: any) => {
                const status = x.trim ? trim(x.status) : x.status
                const result = {
                    property: x.property,
                    status: status,
                    is_param: x.is_param || undefined,
                    customSqlClauseBuilder: x.customSqlClauseBuilder,
                }
                if (x.type == "text") {
                    return {
                        ...result,
                        match: x.match,
                    }
                }
                return result
            }),
        combo_text_filters: visibleFilters
            .filter((x: any) => x.type == "combo_text")
            .filter((x: any) => x.status && x.status != "")
            .map((x: any) => {
                const status = x.trim ? trim(x.status) : x.status
                return {
                    fields: x.property,
                    status: status,
                    match: x.match,
                    is_param: x.is_param || undefined,
                    customSqlClauseBuilder: x.customSqlClauseBuilder,
                }
            }),
        date_between_filters: visibleFilters
            .filter((x: any) => x.type == "date_between")
            .filter((x: any) => x.status && x.status != "")
            .map((x: any) => {
                return {
                    date: x.status,
                    startDate: x.property.startDate,
                    endDate: x.property.endDate,
                    is_param: x.is_param || undefined,
                    customSqlClauseBuilder: x.customSqlClauseBuilder,
                }
            }),
        number_filters: visibleFilters
            .filter((x: any) => x.type == "number")
            .filter(
                (x: any) =>
                    (x.min > -1 && x.min !== "") || (x.max > -1 && x.max !== "")
            )
            .map((x: any) => {
                return {
                    property: x.property,
                    min: x.min,
                    max: x.max,
                    is_param: x.is_param || undefined,
                    customSqlClauseBuilder: x.customSqlClauseBuilder,
                }
            }),
        search_filters: visibleFilters
            .filter(
                (x: any) =>
                    x.type == "search" ||
                    x.type === "intentSearch" ||
                    x.type === "memberSelect"
            )
            .map((x: any) => {
                let range
                let include
                if (x.range == null) {
                    /** report里走这个分支 */
                    if (x.status.range) {
                        range = x.status.range.map((k: any) => {
                            if (typeof k === "string") {
                                return k
                            }
                            if (
                                x.ext_properties &&
                                x.ext_properties.joint &&
                                x.ext_properties.joint.key_field
                            ) {
                                return k[x.ext_properties.joint.key_field]
                            }
                        })
                    } else if (x.status && Array.isArray(x.status)) {
                        range = x.status
                    }

                    include = x.status.include
                } else {
                    const r = Array.isArray(x.range) ? x.range : x.range.range
                    include = r.length > 0
                    range = r
                }
                return {
                    property: x.property,
                    range,
                    include,
                    is_param: x.is_param || undefined,
                    customSqlClauseBuilder: x.customSqlClauseBuilder,
                }
            })
            .filter((i) => i.include !== undefined || i.range !== undefined),
        boolean_filters: visibleFilters
            .filter((x: any) => x.type == "boolean")
            .filter((x: any) => x.status && x.status != "")
            .map((x: any) => {
                return {
                    property: x.property,
                    status: x.status,
                    is_param: x.is_param || undefined,
                    customSqlClauseBuilder: x.customSqlClauseBuilder,
                }
            }),
        enum_filters: filters
            .filter((x: any) => x.type == "enum" || x.type == "checkbox-group")
            .filter((x: any) => x.status && x.status.length > 0)
            .map((x: any) => {
                return {
                    property: x.property,
                    status: x.status,
                    is_param: x.is_param || undefined,
                    customSqlClauseBuilder: x.customSqlClauseBuilder,
                    strategy: x.strategy || "",
                }
            }),
        cascader_filters: visibleFilters
            .filter((x: any) => x.type == "cascader")
            .map((x: any) => {
                const filed_values: any = {}
                for (let i = 0; i < x.property.length; i++) {
                    if (x.status[i]) {
                        filed_values[x.property[i]] = x.status[i] || ""
                    }
                }

                return {
                    filed_values: filed_values,
                    is_param: x.is_param || undefined,
                    customSqlClauseBuilder: x.customSqlClauseBuilder,
                }
            })
            .filter((filter: any) => {
                const fieldKeys = Object.keys(filter.filed_values)
                return fieldKeys.length > 0
            }),
        full_text_filters: visibleFilters
            .filter((x: any) => x.type == "fulltext")
            .filter((x: any) => x.status && x.status != "")
            .map((x: any) => {
                const status = x.trim ? trim(x.status) : x.status
                const result = {
                    property: x.property,
                    status: status,
                    is_param: x.is_param || undefined,
                    customSqlClauseBuilder: x.customSqlClauseBuilder,
                }
                return result
            }),
        combine_full_text_filters: visibleFilters
            .filter((x: any) => x.type == "combineFulltext")
            .filter((x: any) => x.status && x.status != "")
            .map((x: any) => {
                const status = x.trim ? trim(x.status) : x.status
                return {
                    properties: x.property,
                    status: status,
                    is_param: x.is_param || undefined,
                    customSqlClauseBuilder: x.customSqlClauseBuilder,
                }
            }),
        tree_filters: visibleFilters
            .filter((x: any) => x.type == "tree")
            .map((x: any) => {
                let ids = x.status.selectedList.map((node) => node.id).join(",")
                if (
                    x.ext_properties &&
                    x.ext_properties.dataPattern &&
                    x.ext_properties.select.valueType
                ) {
                    const label = x.ext_properties.select.valueType
                    ids = x.status.selectedList
                        .map((node) => node.data[label])
                        .join(",")
                }
                const o = {
                    field: x.property,
                    ids: ids,
                    direct: x.status.direct,
                    dataPattern:
                        x.status?.dataPattern ||
                        x.ext_properties?.dataPattern ||
                        undefined,
                    is_param: x.is_param || undefined,
                    treeModelName: x.treeModelName,
                    customSqlClauseBuilder: x.customSqlClauseBuilder,
                }
                x.depth !== undefined && Object.assign(o, { depth: x.depth })
                return o
            }),
        workflow_process_name_filters: visibleFilters
            .filter((x: any) => x.type == "workflow_process_name")
            .filter((x: any) => x.status && x.status != "")
            .map((x: any) => {
                const status = x.trim ? trim(x.status) : x.status
                return {
                    status: status,
                }
            }),
        workflow_process_state_filters: visibleFilters
            .filter((x: any) => x.type == "workflow_process_state")
            .filter((x: any) => x.status && x.status != "")
            .map((x: any) => {
                const status = x.trim ? trim(x.status) : x.status
                return {
                    status: status,
                }
            }),
        workflow_process_task_filters: visibleFilters
            .filter((x: any) => x.type == "workflow_process_task")
            .filter((x: any) => x.status && x.status != "")
            .map((x: any) => {
                const status = x.trim ? trim(x.status) : x.status
                return {
                    status: status,
                }
            }),
        workflow_instance_state_filters: visibleFilters
            .filter((x: any) => x.type == "workflow_instance_state")
            .filter((x: any) => x.status && x.status != "")
            .map((x: any) => {
                const status = x.trim ? trim(x.status) : x.status
                return {
                    status: status,
                }
            }),
        simple_relationship_filters: visibleFilters
            .filter((x: any) => x.type == "simpleRelationship")
            .map((x: any) => {
                return {
                    model: x.model,
                    entityIds: x.entityIds,
                    predicate: x.status,
                }
            }),
        workflow_process_name_multi_filters: visibleFilters
            .filter((x: any) => x.type == "workflow_process_name_multi")
            .filter((x: any) => x.status && x.status != "")
            .map((x: any) => {
                const status = x.trim ? trim(x.status) : x.status
                return {
                    status: status,
                }
            }),
        workflow_process_state_multi_filters: visibleFilters
            .filter((x: any) => x.type == "workflow_process_state_multi")
            .filter((x: any) => x.status && x.status != "")
            .map((x: any) => {
                const status = x.trim ? trim(x.status) : x.status
                return {
                    status: status,
                }
            }),
        workflow_process_task_multi_filters: visibleFilters
            .filter((x: any) => x.type == "workflow_process_task_multi")
            .filter((x: any) => x.status && x.status != "")
            .map((x: any) => {
                const status = x.trim ? trim(x.status) : x.status
                return {
                    status: status,
                }
            }),
    } as any as dto.Tools.FiltersType

    if (slave_filters) {
        Object.assign(o, { slave_filters })
    }

    const combo_texts_filters = visibleFilters
        .filter(
            (i) => i.type === "combo_text_enum" && i.status && i.status.length
        )
        .map((i) => ({
            fields: i.property,
            status: i.status,
        }))
    if (combo_texts_filters && combo_texts_filters.length) {
        Object.assign(o, { combo_texts_filters })
    }

    return o
}
