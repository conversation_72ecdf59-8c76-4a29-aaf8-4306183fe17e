/* eslint-disable @typescript-eslint/no-explicit-any */
import { createFiltersHandler } from "../../core/handle-filter"
import { getAllFilters } from "../../core/tools/filter"
import type * as dto from "../../def/index"

import { CubeBuilder } from "./CubeBuilder"
import { PivotTableApi } from "./api"

export class PivotTable {
    private api: PivotTableApi

    private notQueried = true
    private refreshParams?: dto.PivotTableTypes.refreshAPIParams
    private getDetailParams?: dto.PivotTableTypes.getDetailAPIParams
    private prefilters: dto.prefilters = []
    private keyValuefilters: dto.ListTypes.KeyValueFilters = []
    private rows: unknown[] = []
    private columns: unknown[] = []
    private values: unknown[] = []

    private meta?: dto.PivotTableTypes.queryResult
    private refreshData?: dto.PivotTableTypes.refreshResult
    constructor(model_name: string, pivottableName: string) {
        this.api = new PivotTableApi(model_name, pivottableName)
    }

    public addValues(data: unknown[]) {
        this.values = data
        return this
    }

    public clearValues() {
        this.values = []
        return this
    }

    public addRows(rows: unknown[]) {
        this.rows = rows
    }

    public addColumns(rows: unknown[]) {
        this.columns = rows
    }

    public clearRows() {
        this.rows = []
    }

    public clearColumns() {
        this.columns = []
    }

    public addRawPrefilters(prefilters: dto.prefilters) {
        this.prefilters = prefilters
        return this
    }

    public addFilter(filter: dto.ListTypes.KeyValueFilters[number]) {
        const property = filter.property
        const oldFilterIndex = this.keyValuefilters.findIndex(
            (filter) => filter.property === property
        )
        if (oldFilterIndex > -1) {
            this.keyValuefilters.splice(oldFilterIndex, 1, filter)
        } else {
            this.keyValuefilters.push(filter)
        }
        return this
    }

    public clearFilter() {
        this.keyValuefilters = []
        return this
    }

    public addPrefilter(prefilters: dto.ListTypes.PrefiltersObject) {
        this.clearPrefilter()
        for (const o in prefilters) {
            this.prefilters.push({ property: o, value: prefilters[o] })
        }
        return this
    }

    public clearPrefilter() {
        this.prefilters = []
        return this
    }

    private async queryForYou() {
        if (this.notQueried) {
            await this.query()
        }
    }

    public async query() {
        const data = await this.api.query({ prefilters: this.prefilters })
        this.meta = data
        this.notQueried = false
        return data
    }

    public async refresh(params: dto.PivotTableTypes.refreshParams) {
        await this.queryForYou()
        if (params.filters == null) {
            params.filters = this.handleFilters(this.keyValuefilters)
        }

        const refreshParamsObj = {
            values: this.values,
            rows: this.rows,
            columns: this.columns,
            ...params,
            filters: getAllFilters(params.filters),
            prefilters: this.prefilters,
        }
        const data = await this.api.refresh(refreshParamsObj)
        this.refreshParams = refreshParamsObj
        this.refreshData = data
        return data
    }

    public exportToExcel() {
        if (!this.refreshParams) {
            throw new Error("请先调用refresh方法以初始化")
        }
        return this.api.createExportUrl({
            rows: this.refreshParams.rows,
            columns: this.refreshParams.columns,
            filters: this.refreshParams.filters,
            prefilters: this.prefilters,
            values: this.refreshParams.values,
        })
    }

    public exportDetailAsExcel() {
        if (!this.getDetailParams) {
            throw new Error("请先调用getDetail方法查询详情")
        }
        return this.api.createExportDetailUrl(this.getDetailParams)
    }

    public getDetail(params: dto.PivotTableTypes.getDetaiParams) {
        if (!this.refreshParams) {
            throw new Error("请先调用refresh方法以初始化")
        }
        const getDetailApiParams = {
            ...params,
            filters: this.refreshParams.filters,
            prefilters: this.prefilters,
            values: this.refreshParams.values,
        }
        this.getDetailParams = getDetailApiParams
        return this.api.getDetail(getDetailApiParams)
    }

    public getTableData() {
        if (!this.meta) {
            throw new Error("先调用query")
        }
        if (!this.refreshData) {
            throw new Error("先调用refresh")
        }
        const fieldsObj: {
            [key: string]: dto.PivotTableTypes.queryResult["fields"][number]
        } = {}
        this.meta.fields.forEach((item) => {
            fieldsObj[item.full_property || item.property] = item
        })
        const tableHeaders = CubeBuilder.getHeaders(
            this.refreshData.columns as any,
            fieldsObj as any
        )
        const tableData = CubeBuilder.getTableData(
            this.refreshData.rows,
            fieldsObj as any
        )
        return {
            tableHeaders,
            tableData,
        }
    }

    protected handleFilters(keyValueFilter: dto.ListTypes.KeyValueFilters) {
        if (keyValueFilter.length === 0) return []
        if (!this.meta) {
            throw new Error("请先查询数据")
        }
        return createFiltersHandler(this.meta.fields)(keyValueFilter)
    }
}
