import jwtDecode from "jwt-decode"

import { buildFilters, getAllFilters, metaFilter } from "./core/tools/filter"
import type { DetailTypes, SdkConstructorParams } from "./def"
import {
    enablePayloadEncode,
    encodeBase64,
    has<PERSON>ey,
    set,
} from "./helpers/crypto"
import { sanitizeData } from "./helpers/xss"
import { UniplatSdkExtender } from "./model/index/extend"
import { UniplatSdk } from "./model/index/index"

const uniplatsdk = {
    create: (config: SdkConstructorParams) => new UniplatSdk(config),
    getAllFilters,
    buildFilters,
    metaFilter,
    helper: new UniplatSdkExtender(),
    jwtDecode,
    crypto: {
        set,
        enablePayloadEncode,
        hasKey,
        encodeBase64,
    },
    builder: {
        sanitizeData,
        detailBuilder: {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            build(d: DetailTypes.getDetailRequestResult2<any>) {
                const headers = []
                const sections = []
                const pages = []
                const h = d.meta.header.field_groups
                for (const item of h) {
                    headers.push({ label: item.label, value: item.template })
                }
                for (const item of d.meta.sections) {
                    if (item.field_groups && item.field_groups.length) {
                        const g = []
                        for (const sub of item.field_groups) {
                            g.push({ label: sub.label, value: sub.template })
                        }
                        sections.push({ items: g })
                    }
                }
                for (const item of d.meta.pages) {
                    pages.push(item)
                }
                return {
                    row: d.row,
                    headers,
                    sections,
                    pages,
                    name: d.meta.label,
                    keyValue: d.row.keyValue,
                    raw: d,
                }
            },
        },
    },
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const w = window as any

w.uniplatsdk = uniplatsdk

export default uniplatsdk
