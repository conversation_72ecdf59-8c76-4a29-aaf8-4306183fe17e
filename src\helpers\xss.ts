export function prevertXss(html: string) {
    return (html || "")
        .replace(/<img/gi, "&lt;img")
        .replace(/<script/gi, "&lt;script")
}

export function stripHtmlTags(sHtml: string) {
    return sHtml
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;")
}

function parsePath(path: string): string[] {
    return path.replace(/\[(\d+)]/g, ".$1").split(".")
}

function isInWhitelist(path: string, whitelist: string[]): boolean {
    const pathArray = parsePath(path)
    return whitelist.some((whitePath) => {
        const whiteArray = parsePath(whitePath)
        return whiteArray.every((key, index) => pathArray[index] === key)
    })
}

export function sanitizeData(
    data: unknown,
    whitelist: string[] = [],
    currentPath = ""
) {
    if (typeof data === "string") {
        return isInWhitelist(currentPath, whitelist)
            ? data
            : stripHtmlTags(data)
    } else if (Array.isArray(data)) {
        return data.map((item, index) =>
            sanitizeData(item, whitelist, `${currentPath}[${index}]`)
        )
    } else if (data !== null && typeof data === "object") {
        const d =Object.keys(data).reduce((acc, key) => {
            acc[key] = sanitizeData(data[key], whitelist, `${currentPath}${currentPath ? '.' : ''}${key}`);
            return acc;
          }, {} as AnyObject);
          console.log(d)
        return d
    }
    return data
}
