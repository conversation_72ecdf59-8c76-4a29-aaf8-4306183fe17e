<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Workflow2Api | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="workflow2api.html">Workflow2Api</a>
				</li>
			</ul>
			<h1>Class Workflow2Api</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">Workflow2Api</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="workflow2api.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section tsd-is-private tsd-is-private-protected">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="workflow2api.html#modelname" class="tsd-kind-icon">model<wbr>Name</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#addremark" class="tsd-kind-icon">add<wbr>Remark</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#canceltask" class="tsd-kind-icon">cancel<wbr>Task</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#canceltaskbatch" class="tsd-kind-icon">cancel<wbr>Task<wbr>Batch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#changemaster" class="tsd-kind-icon">change<wbr>Master</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#changeprocessstate" class="tsd-kind-icon">change<wbr>Process<wbr>State</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#changeprocessstatebatch" class="tsd-kind-icon">change<wbr>Process<wbr>State<wbr>Batch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#changetask" class="tsd-kind-icon">change<wbr>Task</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#changetaskbatch" class="tsd-kind-icon">change<wbr>Task<wbr>Batch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#checkstateeditable" class="tsd-kind-icon">check<wbr>State<wbr>Editable</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#checktaskeditable" class="tsd-kind-icon">check<wbr>Task<wbr>Editable</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#createexporturl" class="tsd-kind-icon">create<wbr>Export<wbr>Url</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#createprocess" class="tsd-kind-icon">create<wbr>Process</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#createprocessdef2" class="tsd-kind-icon">create<wbr>Process<wbr>Def2</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#createtask" class="tsd-kind-icon">create<wbr>Task</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#createtaskbatch" class="tsd-kind-icon">create<wbr>Task<wbr>Batch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#datasoucelist" class="tsd-kind-icon">datasouce<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#detail" class="tsd-kind-icon">detail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#detail2" class="tsd-kind-icon">detail2</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#editprocess" class="tsd-kind-icon">edit<wbr>Process</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#edittask" class="tsd-kind-icon">edit<wbr>Task</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#edittaskbatch" class="tsd-kind-icon">edit<wbr>Task<wbr>Batch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#finishtask" class="tsd-kind-icon">finish<wbr>Task</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#finishtaskbatch" class="tsd-kind-icon">finish<wbr>Task<wbr>Batch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="workflow2api.html#getassociateid" class="tsd-kind-icon">get<wbr>Associate<wbr>Id</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#getdefaulttemplateurl" class="tsd-kind-icon">get<wbr>Default<wbr>Template<wbr>Url</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#getprocessinfo" class="tsd-kind-icon">get<wbr>Process<wbr>Info</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#gettaskdefaultdealer" class="tsd-kind-icon">get<wbr>Task<wbr>Default<wbr>Dealer</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="workflow2api.html#getworkflowprocesslist" class="tsd-kind-icon">get<wbr>Workflow<wbr>Process<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#homecount" class="tsd-kind-icon">home<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#homemasterlistcount" class="tsd-kind-icon">home<wbr>Master<wbr>List<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#hometodolistcount" class="tsd-kind-icon">home<wbr>Todo<wbr>List<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#list" class="tsd-kind-icon">list</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#listallprocessdef" class="tsd-kind-icon">list<wbr>All<wbr>Process<wbr>Def</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#listallprocessname" class="tsd-kind-icon">list<wbr>All<wbr>Process<wbr>Name</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#listallprocessnamewithright" class="tsd-kind-icon">list<wbr>All<wbr>Process<wbr>Name<wbr>With<wbr>Right</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#listfinishtask" class="tsd-kind-icon">list<wbr>Finish<wbr>Task</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#listmodelactions" class="tsd-kind-icon">list<wbr>Model<wbr>Actions</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#listoperation" class="tsd-kind-icon">list<wbr>Operation</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#listprocessinstancepanel" class="tsd-kind-icon">list<wbr>Process<wbr>Instance<wbr>Panel</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#listremark" class="tsd-kind-icon">list<wbr>Remark</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#processdefdetail" class="tsd-kind-icon">process<wbr>Def<wbr>Detail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="workflow2api.html#querydealerlist" class="tsd-kind-icon">query<wbr>Dealer<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#queryprocessbyassociateid" class="tsd-kind-icon">query<wbr>Process<wbr>ByAssociate<wbr>Id</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#queryprocessexistcount" class="tsd-kind-icon">query<wbr>Process<wbr>Exist<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#queryprocesswithcount" class="tsd-kind-icon">query<wbr>Process<wbr>With<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#queryworkflowdealerlist" class="tsd-kind-icon">query<wbr>Workflow<wbr>Dealer<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#queryworkflowdealerlist2" class="tsd-kind-icon">query<wbr>Workflow<wbr>Dealer<wbr>List2</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#rollbackprocess" class="tsd-kind-icon">rollback<wbr>Process</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#saveprocessdef2" class="tsd-kind-icon">save<wbr>Process<wbr>Def2</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#startprocess" class="tsd-kind-icon">start<wbr>Process</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#startprocessbatch" class="tsd-kind-icon">start<wbr>Process<wbr>Batch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#startprocesscheck" class="tsd-kind-icon">start<wbr>Process<wbr>Check</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#updateonline" class="tsd-kind-icon">update<wbr>Online</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#updateprocessdesc" class="tsd-kind-icon">update<wbr>Process<wbr>Desc</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#updatestatefilterparam" class="tsd-kind-icon">update<wbr>State<wbr>Filter<wbr>Param</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#updatetaskfilterparam" class="tsd-kind-icon">update<wbr>Task<wbr>Filter<wbr>Param</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2api.html#workflowbatchrequest" class="tsd-kind-icon">workflow<wbr>Batch<wbr>Request</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new <wbr>Workflow2<wbr>Api<span class="tsd-signature-symbol">(</span>modelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="workflow2api.html" class="tsd-signature-type">Workflow2Api</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:22</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>modelName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="workflow2api.html" class="tsd-signature-type">Workflow2Api</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="modelname" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> model<wbr>Name</h3>
					<div class="tsd-signature tsd-kind-icon">model<wbr>Name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/api.ts:23</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addremark" class="tsd-anchor"></a>
					<h3>add<wbr>Remark</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Remark<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.AddRemarkParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:183</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.AddRemarkParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="canceltask" class="tsd-anchor"></a>
					<h3>cancel<wbr>Task</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">cancel<wbr>Task<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.CancelTaskParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:360</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.CancelTaskParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="canceltaskbatch" class="tsd-anchor"></a>
					<h3>cancel<wbr>Task<wbr>Batch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">cancel<wbr>Task<wbr>Batch<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.CancelTaskParam</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#batchresult" class="tsd-signature-type">BatchResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:367</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.CancelTaskParam</span><span class="tsd-signature-symbol">&gt;</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#batchresult" class="tsd-signature-type">BatchResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="changemaster" class="tsd-anchor"></a>
					<h3>change<wbr>Master</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">change<wbr>Master<span class="tsd-signature-symbol">(</span>request<span class="tsd-signature-symbol">: </span><a href="../globals.html#changemasterrequest" class="tsd-signature-type">ChangeMasterRequest</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:51</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>request: <a href="../globals.html#changemasterrequest" class="tsd-signature-type">ChangeMasterRequest</a></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="changeprocessstate" class="tsd-anchor"></a>
					<h3>change<wbr>Process<wbr>State</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">change<wbr>Process<wbr>State<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.ChangeProcessStateParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:294</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.ChangeProcessStateParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="changeprocessstatebatch" class="tsd-anchor"></a>
					<h3>change<wbr>Process<wbr>State<wbr>Batch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">change<wbr>Process<wbr>State<wbr>Batch<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ChangeProcessStateParam</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#batchresult" class="tsd-signature-type">BatchResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:303</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ChangeProcessStateParam</span><span class="tsd-signature-symbol">&gt;</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#batchresult" class="tsd-signature-type">BatchResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="changetask" class="tsd-anchor"></a>
					<h3>change<wbr>Task</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">change<wbr>Task<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.ChangeTaskParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:344</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.ChangeTaskParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="changetaskbatch" class="tsd-anchor"></a>
					<h3>change<wbr>Task<wbr>Batch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">change<wbr>Task<wbr>Batch<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ChangeTaskParam</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#batchresult" class="tsd-signature-type">BatchResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:351</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ChangeTaskParam</span><span class="tsd-signature-symbol">&gt;</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#batchresult" class="tsd-signature-type">BatchResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="checkstateeditable" class="tsd-anchor"></a>
					<h3>check<wbr>State<wbr>Editable</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">check<wbr>State<wbr>Editable<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><a href="../globals.html#checkeditparam" class="tsd-signature-type">CheckEditParam</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:139</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <a href="../globals.html#checkeditparam" class="tsd-signature-type">CheckEditParam</a></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="checktaskeditable" class="tsd-anchor"></a>
					<h3>check<wbr>Task<wbr>Editable</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">check<wbr>Task<wbr>Editable<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><a href="../globals.html#checkeditparam" class="tsd-signature-type">CheckEditParam</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:146</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <a href="../globals.html#checkeditparam" class="tsd-signature-type">CheckEditParam</a></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="createexporturl" class="tsd-anchor"></a>
					<h3>create<wbr>Export<wbr>Url</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">create<wbr>Export<wbr>Url<span class="tsd-signature-symbol">(</span>parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.exportExcelParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:416</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>parameters: <span class="tsd-signature-type">dto.workflow2.exportExcelParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="createprocess" class="tsd-anchor"></a>
					<h3>create<wbr>Process</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">create<wbr>Process<span class="tsd-signature-symbol">(</span>processDef<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.ProcessEditParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:273</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processDef: <span class="tsd-signature-type">dto.workflow2.ProcessEditParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="createprocessdef2" class="tsd-anchor"></a>
					<h3>create<wbr>Process<wbr>Def2</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">create<wbr>Process<wbr>Def2<span class="tsd-signature-symbol">(</span>request<span class="tsd-signature-symbol">: </span><a href="../globals.html#createprocessrequest" class="tsd-signature-type">CreateProcessRequest</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:75</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>request: <a href="../globals.html#createprocessrequest" class="tsd-signature-type">CreateProcessRequest</a></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="createtask" class="tsd-anchor"></a>
					<h3>create<wbr>Task</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">create<wbr>Task<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.UpdateTaskParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:312</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.UpdateTaskParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="createtaskbatch" class="tsd-anchor"></a>
					<h3>create<wbr>Task<wbr>Batch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">create<wbr>Task<wbr>Batch<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.UpdateTaskParam</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#batchresult" class="tsd-signature-type">BatchResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:319</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.UpdateTaskParam</span><span class="tsd-signature-symbol">&gt;</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#batchresult" class="tsd-signature-type">BatchResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="datasoucelist" class="tsd-anchor"></a>
					<h3>datasouce<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">datasouce<wbr>List<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#datasourcewithcount" class="tsd-signature-type">DatasourceWithCount</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:404</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#datasourcewithcount" class="tsd-signature-type">DatasourceWithCount</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="detail" class="tsd-anchor"></a>
					<h3>detail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">detail<span class="tsd-signature-symbol">(</span>processId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, detailName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#processdetail" class="tsd-signature-type">ProcessDetail</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:236</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processId: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>detailName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#processdetail" class="tsd-signature-type">ProcessDetail</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="detail2" class="tsd-anchor"></a>
					<h3>detail2</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">detail2<span class="tsd-signature-symbol">(</span>processId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, detailName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#processdetail2" class="tsd-signature-type">ProcessDetail2</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:92</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processId: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>detailName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#processdetail2" class="tsd-signature-type">ProcessDetail2</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="editprocess" class="tsd-anchor"></a>
					<h3>edit<wbr>Process</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">edit<wbr>Process<span class="tsd-signature-symbol">(</span>processDef<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.ProcessEditParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:280</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processDef: <span class="tsd-signature-type">dto.workflow2.ProcessEditParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="edittask" class="tsd-anchor"></a>
					<h3>edit<wbr>Task</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">edit<wbr>Task<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.UpdateTaskParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:328</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.UpdateTaskParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="edittaskbatch" class="tsd-anchor"></a>
					<h3>edit<wbr>Task<wbr>Batch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">edit<wbr>Task<wbr>Batch<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.UpdateTaskParam</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#batchresult" class="tsd-signature-type">BatchResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:335</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.UpdateTaskParam</span><span class="tsd-signature-symbol">&gt;</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#batchresult" class="tsd-signature-type">BatchResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="finishtask" class="tsd-anchor"></a>
					<h3>finish<wbr>Task</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">finish<wbr>Task<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.FinishTaskParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:376</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.FinishTaskParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="finishtaskbatch" class="tsd-anchor"></a>
					<h3>finish<wbr>Task<wbr>Batch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">finish<wbr>Task<wbr>Batch<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.FinishTaskParam</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#batchresult" class="tsd-signature-type">BatchResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:383</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.FinishTaskParam</span><span class="tsd-signature-symbol">&gt;</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#batchresult" class="tsd-signature-type">BatchResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="getassociateid" class="tsd-anchor"></a>
					<h3>get<wbr>Associate<wbr>Id</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Associate<wbr>Id&lt;T&gt;<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.ListQueryParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:132</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.ListQueryParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getdefaulttemplateurl" class="tsd-anchor"></a>
					<h3>get<wbr>Default<wbr>Template<wbr>Url</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Default<wbr>Template<wbr>Url<span class="tsd-signature-symbol">(</span>list_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">undefined</span>, page_name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:422</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>list_name: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">undefined</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> page_name: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getprocessinfo" class="tsd-anchor"></a>
					<h3>get<wbr>Process<wbr>Info</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Process<wbr>Info<span class="tsd-signature-symbol">(</span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#processinfo" class="tsd-signature-type">ProcessInfo</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:267</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#processinfo" class="tsd-signature-type">ProcessInfo</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="gettaskdefaultdealer" class="tsd-anchor"></a>
					<h3>get<wbr>Task<wbr>Default<wbr>Dealer</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Task<wbr>Default<wbr>Dealer<span class="tsd-signature-symbol">(</span>request<span class="tsd-signature-symbol">: </span><a href="../globals.html#taskdefaultdealerrequest" class="tsd-signature-type">TaskDefaultDealerRequest</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#workflowuser" class="tsd-signature-type">WorkflowUser</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:44</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取任务的默认处理人</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>request: <a href="../globals.html#taskdefaultdealerrequest" class="tsd-signature-type">TaskDefaultDealerRequest</a></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#workflowuser" class="tsd-signature-type">WorkflowUser</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="getworkflowprocesslist" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> get<wbr>Workflow<wbr>Process<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Workflow<wbr>Process<wbr>List<span class="tsd-signature-symbol">(</span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:450</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>property: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="homecount" class="tsd-anchor"></a>
					<h3>home<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">home<wbr>Count<span class="tsd-signature-symbol">(</span>datasource<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#homeworkflowcount" class="tsd-signature-type">HomeWorkflowCount</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:410</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>datasource: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#homeworkflowcount" class="tsd-signature-type">HomeWorkflowCount</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="homemasterlistcount" class="tsd-anchor"></a>
					<h3>home<wbr>Master<wbr>List<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">home<wbr>Master<wbr>List<wbr>Count<span class="tsd-signature-symbol">(</span>datasource<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:398</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>datasource: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="hometodolistcount" class="tsd-anchor"></a>
					<h3>home<wbr>Todo<wbr>List<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">home<wbr>Todo<wbr>List<wbr>Count<span class="tsd-signature-symbol">(</span>datasource<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:392</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>datasource: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="list" class="tsd-anchor"></a>
					<h3>list</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.ListQueryParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#listresult" class="tsd-signature-type">ListResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:229</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.ListQueryParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#listresult" class="tsd-signature-type">ListResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listallprocessdef" class="tsd-anchor"></a>
					<h3>list<wbr>All<wbr>Process<wbr>Def</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>All<wbr>Process<wbr>Def<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#processinfo" class="tsd-signature-type">ProcessInfo</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:177</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#processinfo" class="tsd-signature-type">ProcessInfo</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listallprocessname" class="tsd-anchor"></a>
					<h3>list<wbr>All<wbr>Process<wbr>Name</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>All<wbr>Process<wbr>Name<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:165</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listallprocessnamewithright" class="tsd-anchor"></a>
					<h3>list<wbr>All<wbr>Process<wbr>Name<wbr>With<wbr>Right</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>All<wbr>Process<wbr>Name<wbr>With<wbr>Right<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:171</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listfinishtask" class="tsd-anchor"></a>
					<h3>list<wbr>Finish<wbr>Task</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>Finish<wbr>Task<span class="tsd-signature-symbol">(</span>processId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#workrecord" class="tsd-signature-type">WorkRecord</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:255</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processId: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#workrecord" class="tsd-signature-type">WorkRecord</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listmodelactions" class="tsd-anchor"></a>
					<h3>list<wbr>Model<wbr>Actions</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>Model<wbr>Actions<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#actioninfo" class="tsd-signature-type">ActionInfo</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:159</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#actioninfo" class="tsd-signature-type">ActionInfo</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listoperation" class="tsd-anchor"></a>
					<h3>list<wbr>Operation</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>Operation<span class="tsd-signature-symbol">(</span>processId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.listOperationParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#processoperation" class="tsd-signature-type">ProcessOperation</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:242</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processId: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.listOperationParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#processoperation" class="tsd-signature-type">ProcessOperation</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listprocessinstancepanel" class="tsd-anchor"></a>
					<h3>list<wbr>Process<wbr>Instance<wbr>Panel</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>Process<wbr>Instance<wbr>Panel<span class="tsd-signature-symbol">(</span>associateId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#processinstancepanel" class="tsd-signature-type">ProcessInstancePanel</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:153</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>associateId: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#processinstancepanel" class="tsd-signature-type">ProcessInstancePanel</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listremark" class="tsd-anchor"></a>
					<h3>list<wbr>Remark</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>Remark<span class="tsd-signature-symbol">(</span>processId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#remark-6" class="tsd-signature-type">Remark</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:261</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processId: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#remark-6" class="tsd-signature-type">Remark</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="processdefdetail" class="tsd-anchor"></a>
					<h3>process<wbr>Def<wbr>Detail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">process<wbr>Def<wbr>Detail<span class="tsd-signature-symbol">(</span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#processconfiginfo" class="tsd-signature-type">ProcessConfigInfo</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:82</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#processconfiginfo" class="tsd-signature-type">ProcessConfigInfo</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="querydealerlist" class="tsd-anchor"></a>
					<h3>query<wbr>Dealer<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">query<wbr>Dealer<wbr>List&lt;T&gt;<span class="tsd-signature-symbol">(</span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:190</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>state: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="queryprocessbyassociateid" class="tsd-anchor"></a>
					<h3>query<wbr>Process<wbr>ByAssociate<wbr>Id</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<wbr>Process<wbr>ByAssociate<wbr>Id<span class="tsd-signature-symbol">(</span>associateId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#associateprocessinfo" class="tsd-signature-type">AssociateProcessInfo</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:111</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>associateId: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#associateprocessinfo" class="tsd-signature-type">AssociateProcessInfo</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="queryprocessexistcount" class="tsd-anchor"></a>
					<h3>query<wbr>Process<wbr>Exist<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<wbr>Process<wbr>Exist<wbr>Count<span class="tsd-signature-symbol">(</span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:215</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>ids: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="queryprocesswithcount" class="tsd-anchor"></a>
					<h3>query<wbr>Process<wbr>With<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<wbr>Process<wbr>With<wbr>Count<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.ListQueryParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#processwithcount" class="tsd-signature-type">ProcessWithCount</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:222</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.ListQueryParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#processwithcount" class="tsd-signature-type">ProcessWithCount</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="queryworkflowdealerlist" class="tsd-anchor"></a>
					<h3>query<wbr>Workflow<wbr>Dealer<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<wbr>Workflow<wbr>Dealer<wbr>List<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#workflowuser" class="tsd-signature-type">WorkflowUser</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:58</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#workflowuser" class="tsd-signature-type">WorkflowUser</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="queryworkflowdealerlist2" class="tsd-anchor"></a>
					<h3>query<wbr>Workflow<wbr>Dealer<wbr>List2</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<wbr>Workflow<wbr>Dealer<wbr>List2<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#workflowuser" class="tsd-signature-type">WorkflowUser</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:25</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#workflowuser" class="tsd-signature-type">WorkflowUser</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="rollbackprocess" class="tsd-anchor"></a>
					<h3>rollback<wbr>Process</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">rollback<wbr>Process<span class="tsd-signature-symbol">(</span>processId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:103</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>请求撤回操作</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processId: <span class="tsd-signature-type">number</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>流程ID</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>true 成功</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="saveprocessdef2" class="tsd-anchor"></a>
					<h3>save<wbr>Process<wbr>Def2</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">save<wbr>Process<wbr>Def2<span class="tsd-signature-symbol">(</span>request<span class="tsd-signature-symbol">: </span><a href="../globals.html#editprocessrequest" class="tsd-signature-type">EditProcessRequest</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:68</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>request: <a href="../globals.html#editprocessrequest" class="tsd-signature-type">EditProcessRequest</a></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="startprocess" class="tsd-anchor"></a>
					<h3>start<wbr>Process</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">start<wbr>Process<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.StartProcessParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:287</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.StartProcessParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="startprocessbatch" class="tsd-anchor"></a>
					<h3>start<wbr>Process<wbr>Batch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">start<wbr>Process<wbr>Batch<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.StartProcessParam</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#batchresult" class="tsd-signature-type">BatchResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:199</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.StartProcessParam</span><span class="tsd-signature-symbol">&gt;</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#batchresult" class="tsd-signature-type">BatchResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="startprocesscheck" class="tsd-anchor"></a>
					<h3>start<wbr>Process<wbr>Check</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">start<wbr>Process<wbr>Check<span class="tsd-signature-symbol">(</span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#startcheckresult" class="tsd-signature-type">StartCheckResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:208</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>ids: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#startcheckresult" class="tsd-signature-type">StartCheckResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updateonline" class="tsd-anchor"></a>
					<h3>update<wbr>Online</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Online<span class="tsd-signature-symbol">(</span>online<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span>, processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:125</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>online: <span class="tsd-signature-type">boolean</span></h5>
								</li>
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updateprocessdesc" class="tsd-anchor"></a>
					<h3>update<wbr>Process<wbr>Desc</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Process<wbr>Desc<span class="tsd-signature-symbol">(</span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, desc<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:117</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>id: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>desc: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updatestatefilterparam" class="tsd-anchor"></a>
					<h3>update<wbr>State<wbr>Filter<wbr>Param</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>State<wbr>Filter<wbr>Param<span class="tsd-signature-symbol">(</span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:439</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updatetaskfilterparam" class="tsd-anchor"></a>
					<h3>update<wbr>Task<wbr>Filter<wbr>Param</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Task<wbr>Filter<wbr>Param<span class="tsd-signature-symbol">(</span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:446</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="workflowbatchrequest" class="tsd-anchor"></a>
					<h3>workflow<wbr>Batch<wbr>Request</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">workflow<wbr>Batch<wbr>Request<span class="tsd-signature-symbol">(</span>request<span class="tsd-signature-symbol">: </span><a href="../globals.html#workflowbatchrequest" class="tsd-signature-type">WorkflowBatchRequest</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#workflowuser" class="tsd-signature-type">WorkflowUser</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/api.ts:34</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>工作流批量处理</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>request: <a href="../globals.html#workflowbatchrequest" class="tsd-signature-type">WorkflowBatchRequest</a></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#workflowuser" class="tsd-signature-type">WorkflowUser</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="workflow2api.html" class="tsd-kind-icon">Workflow2<wbr>Api</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class">
								<a href="workflow2api.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="workflow2api.html#modelname" class="tsd-kind-icon">model<wbr>Name</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#addremark" class="tsd-kind-icon">add<wbr>Remark</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#canceltask" class="tsd-kind-icon">cancel<wbr>Task</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#canceltaskbatch" class="tsd-kind-icon">cancel<wbr>Task<wbr>Batch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#changemaster" class="tsd-kind-icon">change<wbr>Master</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#changeprocessstate" class="tsd-kind-icon">change<wbr>Process<wbr>State</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#changeprocessstatebatch" class="tsd-kind-icon">change<wbr>Process<wbr>State<wbr>Batch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#changetask" class="tsd-kind-icon">change<wbr>Task</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#changetaskbatch" class="tsd-kind-icon">change<wbr>Task<wbr>Batch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#checkstateeditable" class="tsd-kind-icon">check<wbr>State<wbr>Editable</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#checktaskeditable" class="tsd-kind-icon">check<wbr>Task<wbr>Editable</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#createexporturl" class="tsd-kind-icon">create<wbr>Export<wbr>Url</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#createprocess" class="tsd-kind-icon">create<wbr>Process</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#createprocessdef2" class="tsd-kind-icon">create<wbr>Process<wbr>Def2</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#createtask" class="tsd-kind-icon">create<wbr>Task</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#createtaskbatch" class="tsd-kind-icon">create<wbr>Task<wbr>Batch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#datasoucelist" class="tsd-kind-icon">datasouce<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#detail" class="tsd-kind-icon">detail</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#detail2" class="tsd-kind-icon">detail2</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#editprocess" class="tsd-kind-icon">edit<wbr>Process</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#edittask" class="tsd-kind-icon">edit<wbr>Task</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#edittaskbatch" class="tsd-kind-icon">edit<wbr>Task<wbr>Batch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#finishtask" class="tsd-kind-icon">finish<wbr>Task</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#finishtaskbatch" class="tsd-kind-icon">finish<wbr>Task<wbr>Batch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="workflow2api.html#getassociateid" class="tsd-kind-icon">get<wbr>Associate<wbr>Id</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#getdefaulttemplateurl" class="tsd-kind-icon">get<wbr>Default<wbr>Template<wbr>Url</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#getprocessinfo" class="tsd-kind-icon">get<wbr>Process<wbr>Info</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#gettaskdefaultdealer" class="tsd-kind-icon">get<wbr>Task<wbr>Default<wbr>Dealer</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="workflow2api.html#getworkflowprocesslist" class="tsd-kind-icon">get<wbr>Workflow<wbr>Process<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#homecount" class="tsd-kind-icon">home<wbr>Count</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#homemasterlistcount" class="tsd-kind-icon">home<wbr>Master<wbr>List<wbr>Count</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#hometodolistcount" class="tsd-kind-icon">home<wbr>Todo<wbr>List<wbr>Count</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#list" class="tsd-kind-icon">list</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#listallprocessdef" class="tsd-kind-icon">list<wbr>All<wbr>Process<wbr>Def</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#listallprocessname" class="tsd-kind-icon">list<wbr>All<wbr>Process<wbr>Name</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#listallprocessnamewithright" class="tsd-kind-icon">list<wbr>All<wbr>Process<wbr>Name<wbr>With<wbr>Right</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#listfinishtask" class="tsd-kind-icon">list<wbr>Finish<wbr>Task</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#listmodelactions" class="tsd-kind-icon">list<wbr>Model<wbr>Actions</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#listoperation" class="tsd-kind-icon">list<wbr>Operation</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#listprocessinstancepanel" class="tsd-kind-icon">list<wbr>Process<wbr>Instance<wbr>Panel</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#listremark" class="tsd-kind-icon">list<wbr>Remark</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#processdefdetail" class="tsd-kind-icon">process<wbr>Def<wbr>Detail</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="workflow2api.html#querydealerlist" class="tsd-kind-icon">query<wbr>Dealer<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#queryprocessbyassociateid" class="tsd-kind-icon">query<wbr>Process<wbr>ByAssociate<wbr>Id</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#queryprocessexistcount" class="tsd-kind-icon">query<wbr>Process<wbr>Exist<wbr>Count</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#queryprocesswithcount" class="tsd-kind-icon">query<wbr>Process<wbr>With<wbr>Count</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#queryworkflowdealerlist" class="tsd-kind-icon">query<wbr>Workflow<wbr>Dealer<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#queryworkflowdealerlist2" class="tsd-kind-icon">query<wbr>Workflow<wbr>Dealer<wbr>List2</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#rollbackprocess" class="tsd-kind-icon">rollback<wbr>Process</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#saveprocessdef2" class="tsd-kind-icon">save<wbr>Process<wbr>Def2</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#startprocess" class="tsd-kind-icon">start<wbr>Process</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#startprocessbatch" class="tsd-kind-icon">start<wbr>Process<wbr>Batch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#startprocesscheck" class="tsd-kind-icon">start<wbr>Process<wbr>Check</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#updateonline" class="tsd-kind-icon">update<wbr>Online</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#updateprocessdesc" class="tsd-kind-icon">update<wbr>Process<wbr>Desc</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#updatestatefilterparam" class="tsd-kind-icon">update<wbr>State<wbr>Filter<wbr>Param</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#updatetaskfilterparam" class="tsd-kind-icon">update<wbr>Task<wbr>Filter<wbr>Param</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2api.html#workflowbatchrequest" class="tsd-kind-icon">workflow<wbr>Batch<wbr>Request</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>