import type * as dto from "../../def/index"

import PasswordBoxApi from "./api"
export class PasswordBox {
    private api: PasswordBoxApi
    constructor() {
        this.api = new PasswordBoxApi()
    }

    query() {
        return this.api.query()
    }
    delete(passwordName: string) {
        return this.api.delete(passwordName)
    }
    add(params: dto.PasswordBoxTypes.addAPIParams) {
        return this.api.add(params)
    }
}
