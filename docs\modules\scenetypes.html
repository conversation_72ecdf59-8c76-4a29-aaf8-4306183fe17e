<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>SceneTypes | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="scenetypes.html">SceneTypes</a>
				</li>
			</ul>
			<h1>Namespace SceneTypes</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Type aliases</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="scenetypes.html#loadlistapiparams" class="tsd-kind-icon">Load<wbr>List<wbr>Api<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="scenetypes.html#sceneconfig" class="tsd-kind-icon">Scene<wbr>Config</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="scenetypes.html#scenedata" class="tsd-kind-icon">Scene<wbr>Data</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="scenetypes.html#scenedatalist" class="tsd-kind-icon">Scene<wbr>Data<wbr>List</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="scenetypes.html#scenedatas" class="tsd-kind-icon">Scene<wbr>Datas</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Type aliases</h2>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="loadlistapiparams" class="tsd-anchor"></a>
					<h3>Load<wbr>List<wbr>Api<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">Load<wbr>List<wbr>Api<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>itemIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>itemSize<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>keyword<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>parent<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>scene<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:400</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>item<wbr>Index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>item<wbr>Size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>keyword<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> parent<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>scene<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="sceneconfig" class="tsd-anchor"></a>
					<h3>Scene<wbr>Config</h3>
					<div class="tsd-signature tsd-kind-icon">Scene<wbr>Config<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>fields<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>template<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>width<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>placeholder<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>search<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"list"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"tree"</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:376</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>fields<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>template<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>width<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>placeholder<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>search<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter">
										<h5>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>search<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"list"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"tree"</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="scenedata" class="tsd-anchor"></a>
					<h3>Scene<wbr>Data</h3>
					<div class="tsd-signature tsd-kind-icon">Scene<wbr>Data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">; </span>fields<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>key<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:382</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>fields<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>key<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="scenedatalist" class="tsd-anchor"></a>
					<h3>Scene<wbr>Data<wbr>List</h3>
					<div class="tsd-signature tsd-kind-icon">Scene<wbr>Data<wbr>List<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>limit_begin<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>limit_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>list<span class="tsd-signature-symbol">: </span><a href="scenetypes.html#scenedata" class="tsd-signature-type">SceneData</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:393</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>limit_<wbr>begin<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>limit_<wbr>size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>list<span class="tsd-signature-symbol">: </span><a href="scenetypes.html#scenedata" class="tsd-signature-type">SceneData</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>record_<wbr>count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="scenedatas" class="tsd-anchor"></a>
					<h3>Scene<wbr>Datas</h3>
					<div class="tsd-signature tsd-kind-icon">Scene<wbr>Datas<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{}</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:389</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-index-signature">
								<h5><span class="tsd-signature-symbol">[</span>name: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><a href="scenetypes.html#scenedata" class="tsd-signature-type">SceneData</a></h5>
							</li>
						</ul>
					</div>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
					<li class="current tsd-kind-namespace">
						<a href="scenetypes.html">Scene<wbr>Types</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="scenetypes.html#loadlistapiparams" class="tsd-kind-icon">Load<wbr>List<wbr>Api<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="scenetypes.html#sceneconfig" class="tsd-kind-icon">Scene<wbr>Config</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="scenetypes.html#scenedata" class="tsd-kind-icon">Scene<wbr>Data</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="scenetypes.html#scenedatalist" class="tsd-kind-icon">Scene<wbr>Data<wbr>List</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="scenetypes.html#scenedatas" class="tsd-kind-icon">Scene<wbr>Datas</a>
					</li>
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>