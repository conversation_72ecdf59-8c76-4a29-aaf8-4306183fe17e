import type { Method } from "axios"

import DomainServiceApi from "./api"

export class DomainService {
    private api: DomainServiceApi
    constructor(subProjectName: string, serviceName: string, apiName: string) {
        this.api = new DomainServiceApi(subProjectName, serviceName, apiName)
    }
    public request<ParamsType, DataType, ReturnType>(
        method: Method,
        config: {
            params?: ParamsType
            data?: DataType
            headers?: { [key: string]: string | number }
        } = {}
    ) {
        return this.api.request<ParamsType, DataType, ReturnType>(
            method,
            config
        )
    }

    public get<T>(params?: unknown, headers?: Record<string, string | number>) {
        return this.api.request<unknown, unknown, T>("get", { params, headers })
    }

    public post<T>(
        data?: unknown,
        params?: unknown,
        headers?: Record<string, string | number>
    ) {
        return this.api.request<unknown, unknown, T>("post", {
            data,
            params,
            headers,
        })
    }

    public anonymousGet<T>(
        params?: unknown,
        headers?: Record<string, string | number>
    ) {
        const rebuildHeaders = Object.assign({ anonymous: 1 }, headers || {})
        return this.api.request<unknown, unknown, T>("get", {
            params,
            headers: rebuildHeaders,
        })
    }

    public anonymousPost<T>(
        data?: unknown,
        params?: unknown,
        headers?: Record<string, string | number>
    ) {
        const rebuildHeaders = Object.assign({ anonymous: 1 }, headers || {})
        return this.api.request<unknown, unknown, T>("post", {
            data,
            params,
            headers: rebuildHeaders,
        })
    }
}
