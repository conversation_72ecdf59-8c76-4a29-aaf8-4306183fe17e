import { formatDate } from "../../core/date"
import type * as dto from "../../def/index"
import { ListTypes } from "../../def/index"
import { Model } from "../model/model"

import { ListEasy, ListEasy2 } from "./list-easy"

export function getFormItem(data: dto.HumbleFilter) {
    const ConstructorMap = {
        date: DateItem,
        boolean: BooleanItem,
        enum: EnumItem,
        number: NumberItem,
        tree: TreeItem,
        search: SearchItem,
        text: MatchItem,
        "text-date": MatchItem,
        "text-month": MatchItem,
        enum_radio: MatchItem,
        combo_text: MatchItem,
    }
    type a<T extends typeof ConstructorMap> = T[keyof T]
    const constructor: a<typeof ConstructorMap> = ConstructorMap[data.type]
    if (constructor == null) {
        /* 
             "checkbox-group","fulltext","combineFulltext",
         */
        return new FormItem(data)
    }
    return new constructor(data)
}

export class FormItem {
    constructor(protected data: dto.HumbleFilter) {}
    protected defaultValue(): unknown {
        return ""
    }

    protected getMappingValues() {
        const type = this.data.type
        if (!["boolean", "enum"].includes(type)) {
            return null
        }
        const ext_properties = this.data.ext_properties as {
            mapping: {
                mapping_values: unknown[]
            }
        }
        return ext_properties.mapping.mapping_values
    }
    public get property() {
        return this.data.property
    }
    public getKeyValue() {
        return {
            label: this.data.label,
            type: this.data.type,
            property: this.data.property,
            value: this.defaultValue(),
        }
    }
}
export class MatchItem extends FormItem {
    private defaultMatcher = ListTypes.filterMatchType.exact
    public getKeyValue() {
        const result = super.getKeyValue()
        return {
            ...result,
            match: this.defaultMatcher,
        }
    }

    public setMatcher(type: ListTypes.filterMatchType) {
        this.defaultMatcher = type
    }
}

export class BooleanItem extends FormItem {
    public getKeyValue() {
        const result = super.getKeyValue()
        return {
            ...result,
            options: this.getMappingValues(),
        }
    }
}
export class TreeItem extends FormItem {
    protected defaultValue(): unknown {
        return []
    }
}

type SearchExtProperties = {
    joint_name: string
    model_name: string
}
export class SearchItem extends FormItem {
    public getJointSearch() {
        return new Model(this.modelName).jointSearch(this.jointName)
    }

    private get modelName() {
        const ext_properties = this.data.ext_properties as SearchExtProperties
        return ext_properties.model_name
    }
    private get jointName() {
        const ext_properties = this.data.ext_properties as SearchExtProperties
        return ext_properties.joint_name
    }

    protected defaultValue(): unknown {
        return []
    }

    public getKeyValue() {
        const result = super.getKeyValue()

        return {
            ...result,
            include: false,
        }
    }
}

export class EnumItem extends FormItem {
    protected defaultValue(): string[] {
        return []
    }
    public getKeyValue() {
        const result = super.getKeyValue()
        return {
            ...result,
            options: this.getMappingValues(),
        }
    }
}

export class DateItem extends FormItem {
    protected defaultValue(): unknown {
        return []
    }
}
export class NumberItem extends FormItem {
    protected defaultValue(): unknown {
        return {
            max: 100,
            min: 0,
        }
    }
}

type Filter = {
    label: string
    type: string
    property: string
    value: unknown
    match?: ListTypes.filterMatchType
}

export class ListForm {
    private filters: Filter[]
    private form: { [key: string]: unknown } = {}

    constructor(
        protected listEasyInstance: ListEasy,
        private formItems: FormItem[]
    ) {}

    private createFilters() {
        this.filters = this.formItems.map((item) => item.getKeyValue())
    }

    public getFilter(property: string) {
        return this.formItems.find((k) => k.property === property)
    }

    public getFilters() {
        this.createFilters()
        const result = {}
        this.filters.forEach((k) => {
            result[k.property] = k
        })
        return result
    }

    public getObject() {
        this.createFilters()
        const result = {}
        this.filters.forEach((k) => {
            result[k.property] = k.value
        })
        this.form = result
        return result
    }

    public get labels() {
        this.createFilters()
        const result = {}
        this.filters.forEach((k) => {
            result[k.property] = k.label
        })
        return result
    }

    public done() {
        this.filters
            .map((filter) => {
                const type = filter.type
                let value = this.form[filter.property]
                if (type === "date") {
                    value = (this.form[filter.property] as Date[]).map(
                        (date) => {
                            return formatDate.call(date, "yyyy-MM-dd")
                        }
                    )
                }
                return {
                    ...filter,
                    value,
                }
            })
            .forEach((filter) =>
                this.listEasyInstance.addFilter(
                    filter as dto.ListTypes.KeyValueFilters[number]
                )
            )
    }
}

export class ListForm2 {
    private filters: Filter[]
    private form: { [key: string]: unknown } = {}

    constructor(
        protected listEasyInstance: ListEasy2,
        private formItems: FormItem[]
    ) {}

    private createFilters() {
        this.filters = this.formItems.map((item) => item.getKeyValue())
    }

    public getFilter(property: string) {
        return this.formItems.find((k) => k.property === property)
    }

    public getFilters() {
        this.createFilters()
        const result = {}
        this.filters.forEach((k) => {
            result[k.property] = k
        })
        return result
    }

    public getObject() {
        this.createFilters()
        const result = {}
        this.filters.forEach((k) => {
            result[k.property] = k.value
        })
        this.form = result
        return result
    }

    public get labels() {
        this.createFilters()
        const result = {}
        this.filters.forEach((k) => {
            result[k.property] = k.label
        })
        return result
    }

    public done() {
        this.filters
            .map((filter) => {
                const type = filter.type
                let value = this.form[filter.property]
                if (type === "date") {
                    value = (this.form[filter.property] as Date[]).map(
                        (date) => {
                            return formatDate.call(date, "yyyy-MM-dd")
                        }
                    )
                }
                return {
                    ...filter,
                    value,
                }
            })
            .forEach((filter) =>
                this.listEasyInstance.addFilter(
                    filter as dto.ListTypes.KeyValueFilters[number]
                )
            )
    }
}
