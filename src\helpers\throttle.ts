/* eslint-disable @typescript-eslint/no-explicit-any */
export function throttle(time = 100) {
    let pending = false
    return function (target: any, name: string): any {
        const originFunc = target[name]
        const newFunc = function (this: any, ...params: any[]) {
            if (pending) {
                return
            }
            pending = true
            new Promise(async (res) => {
                try {
                    await originFunc.apply(this, params)
                } finally {
                    setTimeout(res, time)
                }
            }).finally(() => (pending = false))
        }
        target[name] = newFunc
        return target
    }
}