<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>InputTypes | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="inputtypes.html">InputTypes</a>
				</li>
			</ul>
			<h1>Enumeration InputTypes</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Enumeration members</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#boolean" class="tsd-kind-icon">boolean</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#cascader" class="tsd-kind-icon">cascader</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#checkbox" class="tsd-kind-icon">checkbox</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#color" class="tsd-kind-icon">color</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#date" class="tsd-kind-icon">date</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#datetime" class="tsd-kind-icon">datetime</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#enum" class="tsd-kind-icon">enum</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#file" class="tsd-kind-icon">file</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#grid" class="tsd-kind-icon">grid</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#hidden" class="tsd-kind-icon">hidden</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#json" class="tsd-kind-icon">json</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#label" class="tsd-kind-icon">label</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#mapping" class="tsd-kind-icon">mapping</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#money" class="tsd-kind-icon">money</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#month" class="tsd-kind-icon">month</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#multi_file" class="tsd-kind-icon">multi_<wbr>file</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#multi_mapping" class="tsd-kind-icon">multi_<wbr>mapping</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#multi_search" class="tsd-kind-icon">multi_<wbr>search</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#number" class="tsd-kind-icon">number</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#qart" class="tsd-kind-icon">qart</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#rich_text" class="tsd-kind-icon">rich_<wbr>text</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#search" class="tsd-kind-icon">search</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#text" class="tsd-kind-icon">text</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#textarea" class="tsd-kind-icon">textarea</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#tip" class="tsd-kind-icon">tip</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="inputtypes.html#tree" class="tsd-kind-icon">tree</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Enumeration members</h2>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="boolean" class="tsd-anchor"></a>
					<h3>boolean</h3>
					<div class="tsd-signature tsd-kind-icon">boolean<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;boolean&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:11</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="cascader" class="tsd-anchor"></a>
					<h3>cascader</h3>
					<div class="tsd-signature tsd-kind-icon">cascader<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;cascader&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:24</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="checkbox" class="tsd-anchor"></a>
					<h3>checkbox</h3>
					<div class="tsd-signature tsd-kind-icon">checkbox<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;checkbox-group&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:10</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="color" class="tsd-anchor"></a>
					<h3>color</h3>
					<div class="tsd-signature tsd-kind-icon">color<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;color&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:7</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="date" class="tsd-anchor"></a>
					<h3>date</h3>
					<div class="tsd-signature tsd-kind-icon">date<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;date&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:13</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="datetime" class="tsd-anchor"></a>
					<h3>datetime</h3>
					<div class="tsd-signature tsd-kind-icon">datetime<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;datetime&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:14</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="enum" class="tsd-anchor"></a>
					<h3>enum</h3>
					<div class="tsd-signature tsd-kind-icon">enum<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;enum&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:29</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="file" class="tsd-anchor"></a>
					<h3>file</h3>
					<div class="tsd-signature tsd-kind-icon">file<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;file&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:20</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="grid" class="tsd-anchor"></a>
					<h3>grid</h3>
					<div class="tsd-signature tsd-kind-icon">grid<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;grid&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:23</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="hidden" class="tsd-anchor"></a>
					<h3>hidden</h3>
					<div class="tsd-signature tsd-kind-icon">hidden<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;hidden&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:28</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="json" class="tsd-anchor"></a>
					<h3>json</h3>
					<div class="tsd-signature tsd-kind-icon">json<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;json&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:12</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="label" class="tsd-anchor"></a>
					<h3>label</h3>
					<div class="tsd-signature tsd-kind-icon">label<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;label&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:5</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="mapping" class="tsd-anchor"></a>
					<h3>mapping</h3>
					<div class="tsd-signature tsd-kind-icon">mapping<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;mapping&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:8</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="money" class="tsd-anchor"></a>
					<h3>money</h3>
					<div class="tsd-signature tsd-kind-icon">money<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;money&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:17</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="month" class="tsd-anchor"></a>
					<h3>month</h3>
					<div class="tsd-signature tsd-kind-icon">month<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;month&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:15</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="multi_file" class="tsd-anchor"></a>
					<h3>multi_<wbr>file</h3>
					<div class="tsd-signature tsd-kind-icon">multi_<wbr>file<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;multi_file&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:21</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="multi_mapping" class="tsd-anchor"></a>
					<h3>multi_<wbr>mapping</h3>
					<div class="tsd-signature tsd-kind-icon">multi_<wbr>mapping<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;multi_mapping&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:9</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="multi_search" class="tsd-anchor"></a>
					<h3>multi_<wbr>search</h3>
					<div class="tsd-signature tsd-kind-icon">multi_<wbr>search<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;multi_search&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:19</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="number" class="tsd-anchor"></a>
					<h3>number</h3>
					<div class="tsd-signature tsd-kind-icon">number<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;number&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:16</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="qart" class="tsd-anchor"></a>
					<h3>qart</h3>
					<div class="tsd-signature tsd-kind-icon">qart<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;qart&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:25</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="rich_text" class="tsd-anchor"></a>
					<h3>rich_<wbr>text</h3>
					<div class="tsd-signature tsd-kind-icon">rich_<wbr>text<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;rich_text&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:26</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="search" class="tsd-anchor"></a>
					<h3>search</h3>
					<div class="tsd-signature tsd-kind-icon">search<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;search&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:18</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="text" class="tsd-anchor"></a>
					<h3>text</h3>
					<div class="tsd-signature tsd-kind-icon">text<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;text&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:6</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="textarea" class="tsd-anchor"></a>
					<h3>textarea</h3>
					<div class="tsd-signature tsd-kind-icon">textarea<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;textarea&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:22</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="tip" class="tsd-anchor"></a>
					<h3>tip</h3>
					<div class="tsd-signature tsd-kind-icon">tip<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;tip&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:4</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="tree" class="tsd-anchor"></a>
					<h3>tree</h3>
					<div class="tsd-signature tsd-kind-icon">tree<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;tree&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/form-item.ts:27</li>
						</ul>
					</aside>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-enum">
						<a href="inputtypes.html" class="tsd-kind-icon">Input<wbr>Types</a>
						<ul>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#boolean" class="tsd-kind-icon">boolean</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#cascader" class="tsd-kind-icon">cascader</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#checkbox" class="tsd-kind-icon">checkbox</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#color" class="tsd-kind-icon">color</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#date" class="tsd-kind-icon">date</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#datetime" class="tsd-kind-icon">datetime</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#enum" class="tsd-kind-icon">enum</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#file" class="tsd-kind-icon">file</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#grid" class="tsd-kind-icon">grid</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#hidden" class="tsd-kind-icon">hidden</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#json" class="tsd-kind-icon">json</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#label" class="tsd-kind-icon">label</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#mapping" class="tsd-kind-icon">mapping</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#money" class="tsd-kind-icon">money</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#month" class="tsd-kind-icon">month</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#multi_file" class="tsd-kind-icon">multi_<wbr>file</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#multi_mapping" class="tsd-kind-icon">multi_<wbr>mapping</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#multi_search" class="tsd-kind-icon">multi_<wbr>search</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#number" class="tsd-kind-icon">number</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#qart" class="tsd-kind-icon">qart</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#rich_text" class="tsd-kind-icon">rich_<wbr>text</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#search" class="tsd-kind-icon">search</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#text" class="tsd-kind-icon">text</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#textarea" class="tsd-kind-icon">textarea</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#tip" class="tsd-kind-icon">tip</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="inputtypes.html#tree" class="tsd-kind-icon">tree</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>