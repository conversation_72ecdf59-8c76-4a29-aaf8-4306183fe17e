<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>DetailTypes | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="detailtypes.html">DetailTypes</a>
				</li>
			</ul>
			<h1>Namespace DetailTypes</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Type aliases</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="detailtypes.html#fieldgroup" class="tsd-kind-icon">Field<wbr>Group</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter"><a href="detailtypes.html#getlogsrequest" class="tsd-kind-icon">Get<wbr>Logs<wbr>Request</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="detailtypes.html#getlogsrequestrow" class="tsd-kind-icon">Get<wbr>Logs<wbr>Request<wbr>Row</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter"><a href="detailtypes.html#smartqueryresult" class="tsd-kind-icon">Smart<wbr>Query<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="detailtypes.html#constructortype" class="tsd-kind-icon">constructor<wbr>Type</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="detailtypes.html#createexporturl" class="tsd-kind-icon">create<wbr>Export<wbr>Url</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="detailtypes.html#detailsqueryparam" class="tsd-kind-icon">details<wbr>Query<wbr>Param</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="detailtypes.html#getdetailapiparams" class="tsd-kind-icon">get<wbr>DetailAPIParams</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter"><a href="detailtypes.html#getdetailrequestresult" class="tsd-kind-icon">get<wbr>Detail<wbr>Request<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="detailtypes.html#section" class="tsd-kind-icon">section</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Type aliases</h2>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="fieldgroup" class="tsd-anchor"></a>
					<h3>Field<wbr>Group</h3>
					<div class="tsd-signature tsd-kind-icon">Field<wbr>Group<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>align<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>autoHeight<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>canSort<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>catalog<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>description<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fields<span class="tsd-signature-symbol">: </span><a href="tools.html#tree_filter.__type-14.field" class="tsd-signature-type">field</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>fixed<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>intents<span class="tsd-signature-symbol">: </span><a href="../globals.html#rowintent" class="tsd-signature-type">RowIntent</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label_width<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>optional<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>renderMode<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"html"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"text"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"pre"</span><span class="tsd-signature-symbol">; </span>span<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>tagGroups<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tagsOnNewLine<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>tagsStyle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">"tag"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"color-text"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"text"</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>template<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>visible<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">true</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:795</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>align<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>auto<wbr>Height<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>can<wbr>Sort<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>catalog<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>description<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>fields<span class="tsd-signature-symbol">: </span><a href="tools.html#tree_filter.__type-14.field" class="tsd-signature-type">field</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>fixed<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>intents<span class="tsd-signature-symbol">: </span><a href="../globals.html#rowintent" class="tsd-signature-type">RowIntent</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>label_<wbr>width<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>optional<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>render<wbr>Mode<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"html"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"text"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"pre"</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>span<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tag<wbr>Groups<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tags<wbr>OnNew<wbr>Line<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tags<wbr>Style<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">"tag"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"color-text"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"text"</span><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>template<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>visible<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">true</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
					<a name="getlogsrequest" class="tsd-anchor"></a>
					<h3>Get<wbr>Logs<wbr>Request</h3>
					<div class="tsd-signature tsd-kind-icon">Get<wbr>Logs<wbr>Request&lt;T&gt;<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>columns<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>page_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>page_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>rows<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:786</li>
						</ul>
					</aside>
					<h4 class="tsd-type-parameters-title">Type parameters</h4>
					<ul class="tsd-type-parameters">
						<li>
							<h4>T<span class="tsd-signature-symbol">: </span><a href="detailtypes.html#getlogsrequestrow" class="tsd-signature-type">GetLogsRequestRow</a> = <span class="tsd-signature-type">GetLogsRequestRow</span></h4>
						</li>
					</ul>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>columns<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>page_<wbr>count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>page_<wbr>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>record_<wbr>count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>rows<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="getlogsrequestrow" class="tsd-anchor"></a>
					<h3>Get<wbr>Logs<wbr>Request<wbr>Row</h3>
					<div class="tsd-signature tsd-kind-icon">Get<wbr>Logs<wbr>Request<wbr>Row<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{}</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:783</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-index-signature">
								<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
					<a name="smartqueryresult" class="tsd-anchor"></a>
					<h3>Smart<wbr>Query<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">Smart<wbr>Query<wbr>Result&lt;RowType, LogRow&gt;<span class="tsd-signature-symbol">:</span> <a href="detailtypes.html#getdetailrequestresult" class="tsd-signature-type">getDetailRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">LogRow</span><span class="tsd-signature-symbol">&gt;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:767</li>
						</ul>
					</aside>
					<h4 class="tsd-type-parameters-title">Type parameters</h4>
					<ul class="tsd-type-parameters">
						<li>
							<h4>RowType<span class="tsd-signature-symbol">: </span><a href="../globals.html#listrow" class="tsd-signature-type">ListRow</a> = <span class="tsd-signature-type">ListRow</span></h4>
						</li>
						<li>
							<h4>LogRow<span class="tsd-signature-symbol">: </span><a href="detailtypes.html#getlogsrequestrow" class="tsd-signature-type">GetLogsRequestRow</a> = <span class="tsd-signature-type">DetailTypes.GetLogsRequestRow</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="constructortype" class="tsd-anchor"></a>
					<h3>constructor<wbr>Type</h3>
					<div class="tsd-signature tsd-kind-icon">constructor<wbr>Type<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>detailName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>keyValue<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>model_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:777</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> detail<wbr>Name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>key<wbr>Value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>model_<wbr>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="createexporturl" class="tsd-anchor"></a>
					<h3>create<wbr>Export<wbr>Url</h3>
					<div class="tsd-signature tsd-kind-icon">create<wbr>Export<wbr>Url<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>export_template<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>template_name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:851</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> export_<wbr>template<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> template_<wbr>name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="detailsqueryparam" class="tsd-anchor"></a>
					<h3>details<wbr>Query<wbr>Param</h3>
					<div class="tsd-signature tsd-kind-icon">details<wbr>Query<wbr>Param<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>itemIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>itemSize<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:772</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>item<wbr>Index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>item<wbr>Size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="getdetailapiparams" class="tsd-anchor"></a>
					<h3>get<wbr>DetailAPIParams</h3>
					<div class="tsd-signature tsd-kind-icon">get<wbr>DetailAPIParams<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>details<span class="tsd-signature-symbol">?: </span><a href="detailtypes.html#detailsqueryparam" class="tsd-signature-type">detailsQueryParam</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>log<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>pageIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>showLog<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:830</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> details<span class="tsd-signature-symbol">?: </span><a href="detailtypes.html#detailsqueryparam" class="tsd-signature-type">detailsQueryParam</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> log<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>pageIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>showLog<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> }</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter">
										<h5>page<wbr>Index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>show<wbr>Log<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
					<a name="getdetailrequestresult" class="tsd-anchor"></a>
					<h3>get<wbr>Detail<wbr>Request<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">get<wbr>Detail<wbr>Request<wbr>Result&lt;T, LogRow&gt;<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>details<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span><span class="tsd-signature-symbol">; </span>log<span class="tsd-signature-symbol">: </span><a href="detailtypes.html#getlogsrequest" class="tsd-signature-type">GetLogsRequest</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">LogRow</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span><span class="tsd-signature-symbol">; </span>meta<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Meta</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">"meta"</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">; </span>row<span class="tsd-signature-symbol">: </span><a href="../globals.html#metarow" class="tsd-signature-type">metaRow</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>action<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>subProjectName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:837</li>
						</ul>
					</aside>
					<h4 class="tsd-type-parameters-title">Type parameters</h4>
					<ul class="tsd-type-parameters">
						<li>
							<h4>T<span class="tsd-signature-symbol">: </span><a href="../globals.html#listrow" class="tsd-signature-type">ListRow</a> = <span class="tsd-signature-type">ListRow</span></h4>
						</li>
						<li>
							<h4>LogRow<span class="tsd-signature-symbol">: </span><a href="detailtypes.html#getlogsrequestrow" class="tsd-signature-type">GetLogsRequestRow</a> = <span class="tsd-signature-type">GetLogsRequestRow</span></h4>
						</li>
					</ul>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>details<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>log<span class="tsd-signature-symbol">: </span><a href="detailtypes.html#getlogsrequest" class="tsd-signature-type">GetLogsRequest</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">LogRow</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>meta<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Meta</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">"meta"</span><span class="tsd-signature-symbol">]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>row<span class="tsd-signature-symbol">: </span><a href="../globals.html#metarow" class="tsd-signature-type">metaRow</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>action<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>sub<wbr>Project<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="section" class="tsd-anchor"></a>
					<h3>section</h3>
					<div class="tsd-signature tsd-kind-icon">section<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>dialogPages<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>dialog_width<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>expansion<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>fieldGroupsSpan<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>field_groups<span class="tsd-signature-symbol">: </span><a href="detailtypes.html#fieldgroup" class="tsd-signature-type">FieldGroup</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>intents<span class="tsd-signature-symbol">: </span><a href="../globals.html#rowintent" class="tsd-signature-type">RowIntent</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>pageGroupsSpan<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>page_groups<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>span<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:816</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>actions<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">action</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>dialog<wbr>Pages<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>dialog_<wbr>width<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>expansion<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>field<wbr>Groups<wbr>Span<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>field_<wbr>groups<span class="tsd-signature-symbol">: </span><a href="detailtypes.html#fieldgroup" class="tsd-signature-type">FieldGroup</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>intents<span class="tsd-signature-symbol">: </span><a href="../globals.html#rowintent" class="tsd-signature-type">RowIntent</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>page<wbr>Groups<wbr>Span<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>page_<wbr>groups<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>span<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
						</ul>
					</div>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
					<li class="current tsd-kind-namespace">
						<a href="detailtypes.html">Detail<wbr>Types</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="detailtypes.html#fieldgroup" class="tsd-kind-icon">Field<wbr>Group</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
						<a href="detailtypes.html#getlogsrequest" class="tsd-kind-icon">Get<wbr>Logs<wbr>Request</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="detailtypes.html#getlogsrequestrow" class="tsd-kind-icon">Get<wbr>Logs<wbr>Request<wbr>Row</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
						<a href="detailtypes.html#smartqueryresult" class="tsd-kind-icon">Smart<wbr>Query<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="detailtypes.html#constructortype" class="tsd-kind-icon">constructor<wbr>Type</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="detailtypes.html#createexporturl" class="tsd-kind-icon">create<wbr>Export<wbr>Url</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="detailtypes.html#detailsqueryparam" class="tsd-kind-icon">details<wbr>Query<wbr>Param</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="detailtypes.html#getdetailapiparams" class="tsd-kind-icon">get<wbr>DetailAPIParams</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
						<a href="detailtypes.html#getdetailrequestresult" class="tsd-kind-icon">get<wbr>Detail<wbr>Request<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="detailtypes.html#section" class="tsd-kind-icon">section</a>
					</li>
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>