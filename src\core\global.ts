import type * as dto from "../def/index"
import { storageEncoder } from "../helpers/crypto"
import { text2md5 } from "../helpers/md5"

import { fakeLocalStorage, fakeSessionStorage } from "./storage"
import { isTokenValid, parseToken } from "./token-checker"

const JWT_TOKEN_STORAGE = "JWT_TOKEN"
const XID_JWT_TOKEN_STORAGE = "XID_JWT_TOKEN"
const INIT_DATA_STORAGE = "initData"
const ROOT_ENTRANCE = "root entrance"
const USER_NAME = "用户名"
const USER_ID = "userId"
const IS_SUPER_ADMIN = "us super admin"
const PROJECT_NAME = "PROJECT_NAME"

function addSlash(url: string) {
    return url.endsWith("/") ? url : `${url}/`
}

type XidTokenCache = {
    xid: string
    userId: string
    token: string
}

export class Global {
    private _ssr = false

    private _baseURL = ""
    private _SSEURL = ""
    private _rootEntrance = this.getItem(ROOT_ENTRANCE)
    private memoryScene: dto.Index.Scene[] | null = null
    public sse = false
    public disableRefreshToken = false
    private refresh = 60 * 60 * 1000

    public getItem(key: string) {
        if (storageEncoder.enabled()) {
            const v = this.storage.getItem(text2md5(key))
            if (v) {
                const d = storageEncoder.decode(v)
                if (typeof d === "object") {
                    return JSON.stringify(d)
                }
                return d as string
            }
            return v
        }
        return this.storage.getItem(key)
    }

    public setItem(key: string, value: string | number) {
        if (storageEncoder.enabled()) {
            this.storage.setItem(
                text2md5(key),
                storageEncoder.encode(value + "")
            )
        } else {
            this.storage.setItem(key, value + "")
        }
    }

    public removeItem(key: string) {
        if (storageEncoder.enabled()) {
            this.storage.removeItem(text2md5(key))
        } else {
            this.storage.removeItem(key)
        }
    }

    private _initData = JSON.parse(this.getItem(INIT_DATA_STORAGE) || "{}")

    public useRootEntranceAsTokenPrefix = false

    private tokenKey() {
        return this.useRootEntranceAsTokenPrefix
            ? `${this.rootEntrance}-${JWT_TOKEN_STORAGE}`
            : JWT_TOKEN_STORAGE
    }

    private xidTokenKey() {
        return this.useRootEntranceAsTokenPrefix
            ? `${this.rootEntrance}-${XID_JWT_TOKEN_STORAGE}`
            : XID_JWT_TOKEN_STORAGE
    }

    public set ssr(ssr: boolean) {
        this._ssr = ssr
        this._rootEntrance = this.getItem(ROOT_ENTRANCE)
        this._initData = JSON.parse(this.getItem(INIT_DATA_STORAGE) || "{}")
    }

    public get ssr() {
        return this._ssr
    }

    public get storage() {
        return this._ssr ? fakeSessionStorage : fakeLocalStorage
    }

    public clear() {
        this._baseURL = ""
        this.removeItem(INIT_DATA_STORAGE)
        this.clearJWTToken()
    }

    public set baseUrl(v: string) {
        this._baseURL = v
    }

    public get baseUrl() {
        return addSlash(this._baseURL)
    }

    public set SSEURL(v: string) {
        this._SSEURL = v
    }

    public get SSEURL() {
        const url = this._SSEURL || this._baseURL
        return addSlash(url)
    }

    public clearJWTToken() {
        this.removeItem(this.tokenKey())
        this.removeItem(this.xidTokenKey())
    }

    public set jwtToken(JwtToken: string) {
        this.setItem(this.tokenKey(), JwtToken)
    }

    public set refreshInterval(v: number) {
        this.refresh = v
    }

    public get refreshInterval() {
        return this.refresh
    }

    // eslint-disable-next-line @typescript-eslint/adjacent-overload-signatures
    public get jwtToken() {
        // 如果默认 storage里没有token，从localStorage里拿
        return (
            this.getItem(this.tokenKey()) ??
            fakeLocalStorage.getItem(this.tokenKey())
        )
    }

    public get jwtUserId() {
        if (!this.jwtToken || !isTokenValid(this.jwtToken)) {
            return ""
        }
        return parseToken(this.jwtToken).user_id + ""
    }

    public set rootEntrance(v: string) {
        if (v) {
            this._rootEntrance = v
            this.setItem(ROOT_ENTRANCE, v)
        }
    }

    // 优先使用内存存储的值，否则多project相互切换会产生干扰
    public get rootEntrance() {
        return this._rootEntrance ?? ""
    }

    public set username(v: string) {
        this.setItem(USER_NAME, v)
    }

    public get username() {
        return this.getItem(USER_NAME) ?? ""
    }

    public set uid(v: string) {
        this.setItem(USER_ID, v)
    }

    public get uid() {
        return this.getItem(USER_ID) ?? ""
    }

    public set projectName(v: string) {
        this.setItem(PROJECT_NAME, v)
    }

    public get projectName() {
        return this.getItem(PROJECT_NAME) ?? ""
    }

    public set isSuperAdmin(v: boolean) {
        this.setItem(IS_SUPER_ADMIN, String(v))
    }

    public get isSuperAdmin() {
        return JSON.parse(this.getItem(IS_SUPER_ADMIN) ?? "false")
    }

    public set initData(v: dto.Index.InitData) {
        this._initData[this.rootEntrance] = v
        this.setItem(
            INIT_DATA_STORAGE,
            JSON.stringify(
                Object.assign(
                    JSON.parse(this.getItem(INIT_DATA_STORAGE) || "{}"),
                    { [this.rootEntrance]: v }
                )
            )
        )
    }

    public get initData() {
        return this._initData[this.rootEntrance] || {}
    }

    public getScene4Header() {
        if (this.memoryScene) {
            return this.memoryScene
        }
        if (this.initData) {
            return this.initData.scenes || []
        }
        return []
    }

    /**
     * 设置一个内存级别的 initData Scene 缓存，优先级大于storage中存储的
     */
    public setOneTimeScene(v: dto.Index.InitData) {
        this.memoryScene = v.scenes as dto.Index.Scene[]
    }

    public setXidToken(xid: string, token: string) {
        const cache = this.getItem(this.xidTokenKey())
        if (cache) {
            let items = JSON.parse(cache) as XidTokenCache[]
            items = items.filter((i) => i.xid !== xid)
            token &&
                items.push({
                    xid,
                    token,
                    userId: this.jwtUserId,
                })
            return this.setXidTokens(items)
        }
        this.setXidTokens([{ xid, token, userId: this.jwtUserId }])
    }

    public setXidTokens(items: XidTokenCache[]) {
        this.setItem(
            this.xidTokenKey(),
            JSON.stringify(items.filter((i) => i.token))
        )
    }

    public removeXidToken(xid: string) {
        const cache = this.getItem(this.xidTokenKey())
        if (cache) {
            let items = JSON.parse(cache) as XidTokenCache[]
            items = items.filter((i) => i.xid !== xid)
            this.setXidTokens(items)
        }
    }

    public getXidToken(xid: string) {
        const cache = this.getItem(this.xidTokenKey())
        if (cache) {
            const items = JSON.parse(cache) as XidTokenCache[]
            return items.find(
                (i) => i.xid === xid && i.userId === this.jwtUserId
            )
        }
        return null
    }

    public getXidTokens() {
        const cache = this.getItem(this.xidTokenKey())
        if (cache) {
            return JSON.parse(cache) as XidTokenCache[]
        }
        return []
    }

    public getCurrentToken() {
        if (this.initData && this.initData.xid) {
            const xidToken = this.getXidToken(this.initData.xid + "")
            if (xidToken && xidToken.token) {
                return xidToken.token
            }
        }
        return this.jwtToken || ""
    }
}

export const global = new Global()
