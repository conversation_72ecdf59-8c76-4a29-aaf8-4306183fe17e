import { Anonymous<PERSON>pi } from "../../core/anonymous-api"
import axios from "../../core/axios/index"
import { global } from "../../core/global"
import { encodeParams, encodeParams4Parameters } from "../../core/tools/form"
import type * as dto from "../../def/index"
export class DetailApi<
    T extends dto.ListRow = dto.ListRow,
    LogRow extends dto.DetailTypes.GetLogsRequestRow = dto.DetailTypes.GetLogsRequestRow
> extends AnonymousApi {
    constructor(
        private model_name: string,
        private keyvalue: string | number,
        private detailName?: string
    ) {
        super()
    }
    public getDetail(queryParams?: dto.DetailTypes.getDetailAPIParams) {
        const detailQueryString = queryParams
            ? `?details=${encodeParams(queryParams)}`
            : ""
        return axios.get<dto.DetailTypes.getDetailRequestResult<T, LogRow>>(
            `${this.urlPrefix}/model/${this.model_name}/key/${
                this.keyvalue
            }/detail/${this.detailName ?? ""}${detailQueryString}`
        )
    }

    public getDetailKeyCustom(
        queryParams?: dto.DetailTypes.getDetailAPIParams
    ) {
        const detailQueryString = queryParams
            ? `?details=${encodeParams(queryParams)}`
            : ""
        return axios.get<dto.DetailTypes.getDetailRequestResult<T, LogRow>>(
            `${this.urlPrefix}/model/${this.model_name}/key_custom/detail/${
                this.detailName ?? ""
            }${detailQueryString}`
        )
    }

    public smartQuery(queryParams?: dto.DetailTypes.getDetailAPIParams) {
        const detailQueryString = queryParams
            ? `?details=${encodeParams(queryParams)}`
            : ""
        return axios.get<dto.DetailTypes.SmartQueryResult<T, LogRow>>(
            `general/model/${this.model_name}/key/${this.keyvalue}/smart${detailQueryString}`
        )
    }

    public getLogs(page_index: number) {
        return axios.get<dto.DetailTypes.GetLogsRequest<LogRow>>(
            `general/model/${this.model_name}/key/${this.keyvalue}/log?page_index=${page_index}`
        )
    }

    public getDefaultTemplateUrl() {
        return axios.get<string>(
            `general/model/${
                this.model_name
            }/exportDetailTemplate?${encodeParams4Parameters({
                name: this.detailName,
            })}`
        )
    }

    public createExportUrl(
        params: dto.DetailTypes.createExportUrl & {
            name?: string
        }
    ) {
        return `${global.baseUrl}general/model/sse/${this.model_name}/key/${
            this.keyvalue
        }/detail/export?${encodeParams4Parameters(params, true)}`
    }
}

export class DetailApi2<
    LogRow extends dto.DetailTypes.GetLogsRequestRow = dto.DetailTypes.GetLogsRequestRow
> extends AnonymousApi {
    constructor(
        private model_name: string,
        private keyvalue: string | number,
        private detailName?: string
    ) {
        super()
    }

    private tail() {
        return this.detailName ?? ""
    }

    public getDetail(queryParams?: dto.DetailTypes.getDetailAPIParams) {
        const detailQueryString = queryParams
            ? `?details=${encodeParams(queryParams)}`
            : ""
        return axios.get<dto.DetailTypes.getDetailRequestResult2<LogRow>>(
            `${this.urlPrefix}/model/${this.model_name}/key3/${
                this.keyvalue
            }/detail/${this.tail()}${detailQueryString}`
        )
    }

    public getDetailKeyCustom(
        queryParams?: dto.DetailTypes.getDetailAPIParams
    ) {
        const detailQueryString = queryParams
            ? `?details=${encodeParams(queryParams)}`
            : ""
        return axios.get<dto.DetailTypes.getDetailRequestResult2<LogRow>>(
            `${this.urlPrefix}/model/${
                this.model_name
            }/key_custom/detail/${this.tail()}${detailQueryString}`
        )
    }

    public smartQuery(queryParams?: dto.DetailTypes.getDetailAPIParams) {
        const detailQueryString = queryParams
            ? `?details=${encodeParams(queryParams)}`
            : ""
        return axios.get<dto.DetailTypes.SmartQueryResult2<LogRow>>(
            `general/model/${this.model_name}/key3/${this.keyvalue}/smart${detailQueryString}`
        )
    }

    public getLogs(page_index: number) {
        return axios.get<dto.DetailTypes.GetLogsRequest<LogRow>>(
            `general/model/${this.model_name}/key/${this.keyvalue}/log?page_index=${page_index}`
        )
    }

    public getDefaultTemplateUrl() {
        return axios.get<string>(
            `general/model/${
                this.model_name
            }/exportDetailTemplate?${encodeParams4Parameters({
                name: this.detailName,
            })}`
        )
    }

    public createExportUrl(
        params: dto.DetailTypes.createExportUrl & {
            name?: string
        }
    ) {
        return `${global.baseUrl}general/model/sse/${this.model_name}/key/${
            this.keyvalue
        }/detail/export?${encodeParams4Parameters(params, true)}`
    }
}
