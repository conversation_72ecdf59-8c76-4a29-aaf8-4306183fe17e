var fs = require("fs")
const packageJSON = "./package.json"
const forDevMain = "src/index.ts"
const forPublishMain = "build/main/index.js"
const forDevType = "src/index.ts"
const forPublishType = "build/main/index.d.ts"

const clientCli3PackageJSON = "../report/client-cli3/package.json"

function updateClientCli3SdkVersion(newVersion) {
    try {
        let content = JSON.parse(
            fs.readFileSync(clientCli3PackageJSON).toString()
        )
        content.dependencies["uniplat-sdk"] = newVersion
        fs.writeFileSync(
            clientCli3PackageJSON,
            JSON.stringify(content, null, 2) + "\n"
        )
    } catch {
        console.warn(`未找到Report项目，不会自动更新其SDK版本号`)
    }
}

function checkout(to, testing = false) {
    let content = JSON.parse(fs.readFileSync(packageJSON).toString())
    if (to === "publish") {
        content.main = forPublishMain
        const currentVersion = +content.version.split("-")[0].split(".")[2]
        if (!testing) {
            content.version = `0.1.${currentVersion + 1}-private`
        }
        content.typings = forPublishType
        updateClientCli3SdkVersion(content.version)
    } else if (to === "dev") {
        content.main = forDevMain
        content.typings = forDevType
    }
    fs.writeFileSync(packageJSON, JSON.stringify(content, null, 4) + "\n")
}

// checkout(process.env.to);
module.exports = checkout
