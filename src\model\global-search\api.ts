import axios from "../../core/axios/index"
import type * as dto from "../../def/index"

export default class GlobalSearchApi {
    public search(params: dto.GlobalSearchTypes.searchApiParams) {
        return axios.get<dto.GlobalSearchTypes.searchRequestResult>(
            `general/es/fulltextsearch/${params.keyword}/page/${params.pageIndex}`
        )
    }
    public exportData(keyword: string) {
        return axios.get<string>(`general/es/export/${keyword}`)
    }

    public exportZip() {
        return axios.get<string>(`general/es/exportZip/`)
    }

    public applicationSearch(
        application: string,
        entrance: string,
        p: dto.GlobalSearchTypes.ApplicationSearchParameter
    ) {
        return axios.get<dto.GlobalSearchTypes.ApplicationSearchResult>(
            `general/application/${application}/${entrance}/globalSearch?parameters=${encodeURIComponent(
                JSON.stringify(p)
            )}`
        )
    }
}
