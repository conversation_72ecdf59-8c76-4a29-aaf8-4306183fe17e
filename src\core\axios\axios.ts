import axios, { AxiosAdapter, AxiosInstance } from "axios"

import { parse, rebuildAxiosConfig } from "../../helpers/crypto"
import { isNoSceneHeaderUrl } from "../../helpers/request"
import { events } from "../events"
import { global } from "../global"
import { isInUniApp } from "../in-uniapp"

import { rebuildToken } from "./builder"

const noAuthHeaderUrls = ["/auth/login/teammix"]

const outlawURLs = [
    "general/es/export",
    "general/es/exportZip",
    "general/deploy/vue",
    "general/deploy",
]

let unactiveChecker = -1
let unactiveAction: (() => void) | null = null
let unactiveTimer = -1
const timeoutKey = `unactive-timeout-ts`

function createAxios() {
    let instance: AxiosInstance
    return {
        init(
            config: {
                adapter?: AxiosAdapter
                timeout?: number
                useInterceptor?: (instance: AxiosInstance) => void
            } = {}
        ) {
            const baseURL = global.baseUrl
            const option = { baseURL }
            !isInUniApp() &&
                Object.assign(option, { timeout: config.timeout || 60e3 })
            instance = axios.create(option)
            if (config.adapter) {
                instance.defaults.adapter = config.adapter
            }
            if (config.useInterceptor) {
                config.useInterceptor(instance)
            }
            instance.interceptors.request.use(
                (config) => {
                    const url = config.url as string
                    const { xid } = rebuildToken(config)
                    !config.headers.Entrance &&
                        global.rootEntrance &&
                        (config.headers.Entrance = encodeURIComponent(
                            global.rootEntrance
                        ))
                    if (
                        !config.headers.CurrentOrg &&
                        !xid &&
                        global.initData.orgId
                    ) {
                        config.headers.CurrentOrg = global.initData.orgId
                    }

                    if (!isNoSceneHeaderUrl(url)) {
                        const scenes = global.getScene4Header()
                        if (scenes && scenes.length) {
                            config.headers.Scenes = JSON.stringify(scenes)
                        }
                    }

                    if (
                        url &&
                        noAuthHeaderUrls.find((i) => url.indexOf(i) > -1)
                    ) {
                        config.headers.Authorization = ""
                    }

                    if (unactiveChecker > 0) {
                        clearTimeout(unactiveTimer)
                        if (typeof window === "object") {
                            // 多个tab页的话，A Tab请求了数据，B Tab也需要继承这个下一次超时时间
                            localStorage.setItem(
                                timeoutKey,
                                new Date().valueOf() + ""
                            )

                            const recheck = () => {
                                unactiveTimer = window.setTimeout(() => {
                                    const last =
                                        localStorage.getItem(timeoutKey)
                                    if (
                                        last &&
                                        +last + unactiveChecker <=
                                            new Date().valueOf()
                                    ) {
                                        unactiveAction && unactiveAction()
                                    } else {
                                        recheck()
                                    }
                                }, unactiveChecker)
                            }

                            recheck()
                        }
                    }

                    return rebuildAxiosConfig(config)
                },
                (error) => Promise.reject(error)
            )
            instance.interceptors.response.use(
                (r) => {
                    const p = parse(r.data) as {
                        rescode?: number
                        data: unknown
                        msg?: string
                        message?: string
                    }
                    if (p.rescode !== 0 && !p.data) {
                        const errorMsg = p.msg || p.message
                        r.data = p
                        events.callUniversalErrorCallback(errorMsg, r)
                        events.callUniversalErrorResponseCallback(r)
                        return Promise.reject(errorMsg)
                    }

                    const url = r.config.url
                    if (outlawURLs.find((u) => url && url.startsWith(u))) {
                        return Promise.resolve(p.msg || p.message)
                    }
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    return Promise.resolve(p.data as any)
                },
                (error) => {
                    console.error(error)
                    if (error?.response?.status === 401) {
                        events.callTokenExpiring(401)
                    }
                    events.callUniversalErrorResponseCallback(error)
                    return Promise.reject(
                        (error.response && error.response.data) || error
                    )
                }
            )
        },
        getAxios: () => instance,
    }
}

export function setUnactiveCallback(duration: number, action: () => void) {
    if (duration) {
        unactiveChecker = duration
        unactiveAction = action
    } else {
        unactiveChecker = -1
        unactiveAction = null
    }
}

export const axiosFactory = createAxios()
