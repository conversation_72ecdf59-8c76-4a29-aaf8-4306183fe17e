import { isSimpleRelationshipFilter } from "../../core/filters-filter"
import type * as dto from "../../def/index"

import { getFormItem, ListForm, ListForm2 } from "./form"
import { List, List2 } from "./list"
type ReturnOfGetList<RowType extends dto.ListRow = dto.ListRow> =
    | Promise<dto.ListTypes.getListDataByTabRequestResult<RowType> | null>
    | Promise<dto.ListTypes.getListDataOfSinglePageRequestResult<RowType>>
type ReturnOfGetList2 =
    | Promise<dto.ListTypes.getListDataByTabRequestResult2 | null>
    | Promise<dto.ListTypes.getListDataOfSinglePageRequestResult2>

type GetListType<RowType extends dto.ListRow = dto.ListRow> =
    ListEasy<RowType>["getList"]
type GetListType2<RowType extends dto.ListRow = dto.ListRow> =
    ListEasy2<RowType>["getList"]
export class ListEasy<
    RowType extends dto.ListRow = dto.ListRow
> extends List<RowType> {
    clearTreeFilters(): this {
        throw new Error("Method not implemented.")
    }
    private filters: dto.ListTypes.KeyValueFilters = []
    public rawFilters?: dto.metaFilter[]
    private prefilter: dto.prefilters = []

    /**
     * 添加filter
     * @param filter
     * @returns 返回当前实例，供链式调用
     */
    public addPrefilter(prefilters: dto.ListTypes.PrefiltersObject) {
        for (const o in prefilters) {
            this.prefilter.push({ property: o, value: prefilters[o] })
        }

        return this
    }

    /**
     * 添加prefilter
     * @param filter
     * @returns 返回当前实例，供链式调用
     */
    public addPrefilterAll(prefilters: dto.prefilters) {
        this.prefilter = prefilters

        return this
    }

    /**
     * 清空filter
     * @returns 返回当前实例，供链式调用
     */
    public clearPrefilter() {
        this.prefilter = []
        return this
    }

    /**
     * 添加filter
     * @param filter
     * @returns 返回当前实例，供链式调用
     */
    public addFilter(filter: dto.ListTypes.KeyValueFilters[number]) {
        const property = filter.property
        const oldFilterIndex = this.filters.findIndex(
            (filter) => filter.property === property
        )
        if (oldFilterIndex > -1) {
            this.filters.splice(oldFilterIndex, 1, filter)
        } else {
            this.filters.push(filter)
        }
        return this
    }
    /**
     * 清空filter
     * @returns 返回当前实例，供链式调用
     */
    public clearFilter() {
        this.filters = []
        return this
    }

    private getItemIndexByPage(pageIndex: number) {
        if (pageIndex <= 0) {
            throw new Error("页码不能小于1")
        }
        if (this.item_size == null) {
            throw new Error("不行")
        }
        return (pageIndex - 1) * this.item_size
    }
    /**
     * 保存要被override的父类getList
     */
    private _getList?:
        | dto.ListTypes.getSingleTabPageListFuncType
        | dto.ListTypes.getSpecificTabListFuncType
    /**
     * 保存item_size以便计算item_index
     */
    private item_size?: number

    private getList(
        tabName: string,
        pageIndex: number
    ): Promise<dto.ListTypes.getListDataByTabRequestResult<RowType> | null>
    private getList(
        pageIndex: number
    ): Promise<dto.ListTypes.getListDataOfSinglePageRequestResult<RowType>>
    private getList(
        a: string | number,
        b?: number,
        c?: number
    ): ReturnOfGetList<RowType> {
        if (this._getList == null) {
            throw new Error("不行")
        }
        if (this.item_size == null) {
            throw new Error("不行")
        }

        const _getList = this._getList
        if (typeof a === "string") {
            const tabName = a
            const pageIndex = b ?? 1
            const item_size = c ?? this.item_size
            return (
                _getList as dto.ListTypes.getSpecificTabListFuncType<RowType>
            )(tabName, this.getItemIndexByPage(pageIndex), item_size)
        } else {
            const pageIndex = a
            const item_size = b ?? this.item_size
            return (
                _getList as dto.ListTypes.getSingleTabPageListFuncType<RowType>
            )(this.getItemIndexByPage(pageIndex), item_size)
        }
    }

    public getForm(): ListForm {
        if (this.rawFilters == null) {
            throw new Error("尚未调用ListEasy.query")
        }
        const formItems = this.rawFilters
            .filter((filter) => !isSimpleRelationshipFilter(filter))
            .map((filter: dto.HumbleFilter) => getFormItem(filter))
        return new ListForm(this, formItems)
    }

    public async query(params: dto.ListTypes.queryPropsEasy) {
        const filters = params.filters ?? this.filters ?? []
        const { pageData, getList: _getList } = await super.query({
            ...params,
            prefilters: this.prefilter,
            filters: super.handleFilters(filters),
        })
        this._getList = _getList
        this.item_size = params.item_size
        this.rawFilters = pageData.meta.filters

        return {
            pageData,
            getList: this.getList.bind(this) as GetListType<RowType>,
        }
    }

    /**
     * 指定查询tab
     * @param index 索引，从1开始
     */
    public async queryTab(
        tab: string,
        index: number,
        size: number,
        filters?: dto.ListTypes.KeyValueFilters
    ) {
        if (!this._getList) {
            throw new Error(`请先调用query方法来初始化`)
        }
        const f = super.fullfillParams({
            pageIndex: index,
            item_size: size,
            filters: super.handleFilters(filters ?? this.filters ?? []),
            prefilters: this.prefilter,
        })
        const q = this._getList as dto.ListTypes.getSpecificTabListFuncType
        const i = (index - 1) * size
        return q(tab, i < 0 ? 0 : i, size, f)
    }

    /**
     * 指定查询tab
     * @param tab 索引，从1开始
     */
    public async queryTab2(tab: string, params?: dto.ListTypes.queryPropsEasy) {
        if (!this._getList) {
            throw new Error(`请先调用query方法来初始化`)
        }
        const f = super.fullfillParams({
            ...params,
            filters: super.handleFilters(params.filters ?? this.filters ?? []),
            prefilters: this.prefilter,
        })
        const q = this._getList as dto.ListTypes.getSpecificTabListFuncType
        return q(tab, f.item_index, f.item_size, f)
    }

    /**
     * 此方法用来取代query方法，此方法效能更高
     */
    public async search(params: dto.ListTypes.queryPropsEasy) {
        const filters = params.filters ?? this.filters ?? []
        if (!this._getList) {
            await super.queryMeta(params)
            const { pageData, getList: _getList } = await super.query({
                ...params,
                prefilters: this.prefilter,
                filters: super.handleFilters(filters),
            })
            this._getList = _getList
            this.item_size = params.item_size
            this.rawFilters = pageData.meta.filters
            return pageData
        }

        return (this._getList as dto.ListTypes.getSingleTabPageListFuncType)(
            (params.pageIndex - 1) * params.item_size,
            params.item_size,
            {
                filters: super.handleFilters(filters),
                item_index: (params.pageIndex - 1) * params.item_size,
                item_size: params.item_size,
                tagFilters: params.tagFilters,
                sorts: params.sorts,
                prefilters: this.prefilter || params.prefilters || [],
                fields: params.fields,
            }
        )
    }
}

export class ListEasy2<
    RowType extends dto.ListRow = dto.ListRow
> extends List2 {
    clearTreeFilters(): this {
        throw new Error("Method not implemented.")
    }
    private filters: dto.ListTypes.KeyValueFilters = []
    public rawFilters?: dto.metaFilter[]
    private prefilter: dto.prefilters = []

    /**
     * 添加filter
     * @param filter
     * @returns 返回当前实例，供链式调用
     */
    public addPrefilter(prefilters: dto.ListTypes.PrefiltersObject) {
        for (const o in prefilters) {
            this.prefilter.push({ property: o, value: prefilters[o] })
        }

        return this
    }
    /**
     * 添加filter
     * @param filter
     * @returns 返回当前实例，供链式调用
     */
    public addPrefilterAll(prefilters: dto.prefilters) {
        this.prefilter = prefilters

        return this
    }
    /**
     * 清空filter
     * @returns 返回当前实例，供链式调用
     */
    public clearPrefilter() {
        this.prefilter = []
        return this
    }

    /**
     * 添加filter
     * @param filter
     * @returns 返回当前实例，供链式调用
     */
    public addFilter(filter: dto.ListTypes.KeyValueFilters[number]) {
        const property = filter.property
        const oldFilterIndex = this.filters.findIndex(
            (filter) => filter.property === property
        )
        if (oldFilterIndex > -1) {
            this.filters.splice(oldFilterIndex, 1, filter)
        } else {
            this.filters.push(filter)
        }
        return this
    }
    /**
     * 清空filter
     * @returns 返回当前实例，供链式调用
     */
    public clearFilter() {
        this.filters = []
        return this
    }

    private getItemIndexByPage(pageIndex: number) {
        if (pageIndex <= 0) {
            throw new Error("页码不能小于1")
        }
        if (this.item_size == null) {
            throw new Error("不行")
        }
        return (pageIndex - 1) * this.item_size
    }
    /**
     * 保存要被override的父类getList
     */
    private _getList?:
        | dto.ListTypes.getSingleTabPageListFuncType2
        | dto.ListTypes.getSpecificTabListFuncType2
    /**
     * 保存item_size以便计算item_index
     */
    private item_size?: number

    private getList(
        tabName: string,
        pageIndex: number
    ): Promise<dto.ListTypes.getListDataByTabRequestResult2 | null>
    private getList(
        pageIndex: number
    ): Promise<dto.ListTypes.getListDataOfSinglePageRequestResult2>
    private getList(
        a: string | number,
        b?: number,
        c?: number
    ): ReturnOfGetList2 {
        if (this._getList == null) {
            throw new Error("不行")
        }
        if (this.item_size == null) {
            throw new Error("不行")
        }

        const _getList = this._getList
        if (typeof a === "string") {
            const tabName = a
            const pageIndex = b ?? 1
            const item_size = c ?? this.item_size
            return (_getList as dto.ListTypes.getSpecificTabListFuncType2)(
                tabName,
                this.getItemIndexByPage(pageIndex),
                item_size
            )
        } else {
            const pageIndex = a
            const item_size = b ?? this.item_size
            return (_getList as dto.ListTypes.getSingleTabPageListFuncType2)(
                this.getItemIndexByPage(pageIndex),
                item_size
            )
        }
    }

    public getForm(): ListForm2 {
        if (this.rawFilters == null) {
            throw new Error("尚未调用ListEasy.query")
        }
        const formItems = this.rawFilters
            .filter((filter) => !isSimpleRelationshipFilter(filter))
            .map((filter: dto.HumbleFilter) => getFormItem(filter))
        return new ListForm2(this, formItems)
    }

    public async query(params: dto.ListTypes.queryPropsEasy) {
        const filters = params.filters ?? this.filters ?? []
        const { pageData, getList: _getList } = await super.query({
            ...params,
            prefilters: this.prefilter,
            filters: super.handleFilters(filters),
        })
        this._getList = _getList
        this.item_size = params.item_size
        this.rawFilters = pageData.meta.filters

        return {
            pageData,
            getList: this.getList.bind(this) as GetListType2<RowType>,
        }
    }

    /**
     * 指定查询tab
     * @param index 索引，从1开始
     */
    public async queryTab(
        tab: string,
        index: number,
        size: number,
        filters?: dto.ListTypes.KeyValueFilters
    ) {
        if (!this._getList) {
            throw new Error(`请先调用query方法来初始化`)
        }
        const f = super.fullfillParams({
            pageIndex: index,
            item_size: size,
            filters: super.handleFilters(filters ?? this.filters ?? []),
            prefilters: this.prefilter,
        })
        const q = this._getList as dto.ListTypes.getSpecificTabListFuncType2
        const i = (index - 1) * size
        return q(tab, i < 0 ? 0 : i, size, f)
    }

    /**
     * 指定查询tab
     * @param tab 索引，从1开始
     */
    public async queryTab2(tab: string, params?: dto.ListTypes.queryPropsEasy) {
        if (!this._getList) {
            throw new Error(`请先调用query方法来初始化`)
        }
        const f = super.fullfillParams({
            ...params,
            filters: super.handleFilters(params.filters ?? this.filters ?? []),
            prefilters: this.prefilter,
        })
        const q = this._getList as dto.ListTypes.getSpecificTabListFuncType2
        return q(tab, f.item_index, f.item_size, f)
    }

    /**
     * 此方法用来取代query方法，此方法效能更高
     */
    public async search(params: dto.ListTypes.queryPropsEasy) {
        const filters = params.filters ?? this.filters ?? []
        if (!this._getList) {
            await super.queryMeta(params)
            const { pageData, getList: _getList } = await super.query({
                ...params,
                prefilters: this.prefilter,
                filters: super.handleFilters(filters),
            })
            this._getList = _getList
            this.item_size = params.item_size
            this.rawFilters = pageData.meta.filters
            return pageData
        }
        return (this._getList as dto.ListTypes.getSingleTabPageListFuncType2)(
            (params.pageIndex - 1) * params.item_size,
            params.item_size,
            {
                filters: super.handleFilters(filters),
                item_index: (params.pageIndex - 1) * params.item_size,
                item_size: params.item_size,
                tagFilters: params.tagFilters,
                sorts: params.sorts,
                prefilters: this.prefilter,
                fields: params.fields,
            }
        )
    }
}
