<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>workflow2 | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="workflow2.html">workflow2</a>
				</li>
			</ul>
			<h1>Namespace workflow2</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Type aliases</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#actioninfo" class="tsd-kind-icon">Action<wbr>Info</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#addremarkparam" class="tsd-kind-icon">Add<wbr>Remark<wbr>Param</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#associateprocessinfo" class="tsd-kind-icon">Associate<wbr>Process<wbr>Info</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#batchfaildetail" class="tsd-kind-icon">Batch<wbr>Fail<wbr>Detail</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter"><a href="workflow2.html#batchparam" class="tsd-kind-icon">Batch<wbr>Param</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#batchresult" class="tsd-kind-icon">Batch<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#canceltaskparam" class="tsd-kind-icon">Cancel<wbr>Task<wbr>Param</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#changeprocessstateparam" class="tsd-kind-icon">Change<wbr>Process<wbr>State<wbr>Param</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#changetaskparam" class="tsd-kind-icon">Change<wbr>Task<wbr>Param</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#datasourcewithcount" class="tsd-kind-icon">Datasource<wbr>With<wbr>Count</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#dealer" class="tsd-kind-icon">Dealer</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#finishtaskparam" class="tsd-kind-icon">Finish<wbr>Task<wbr>Param</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#homeworkflowcount" class="tsd-kind-icon">Home<wbr>Workflow<wbr>Count</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#listqueryparam" class="tsd-kind-icon">List<wbr>Query<wbr>Param</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#listresult" class="tsd-kind-icon">List<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#listrowitem" class="tsd-kind-icon">List<wbr>Row<wbr>Item</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#processdef" class="tsd-kind-icon">Process<wbr>Def</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#processdetail" class="tsd-kind-icon">Process<wbr>Detail</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#processeditparam" class="tsd-kind-icon">Process<wbr>Edit<wbr>Param</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#processinfo" class="tsd-kind-icon">Process<wbr>Info</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#processinstancepanel" class="tsd-kind-icon">Process<wbr>Instance<wbr>Panel</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#processoperation" class="tsd-kind-icon">Process<wbr>Operation</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#processrelation" class="tsd-kind-icon">Process<wbr>Relation</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#processwithcount" class="tsd-kind-icon">Process<wbr>With<wbr>Count</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#remark-6" class="tsd-kind-icon">Remark</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#startcheckresult" class="tsd-kind-icon">Start<wbr>Check<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#startprocessparam" class="tsd-kind-icon">Start<wbr>Process<wbr>Param</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#statedef" class="tsd-kind-icon">State<wbr>Def</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#stategroup" class="tsd-kind-icon">State<wbr>Group</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#taskdef-1" class="tsd-kind-icon">Task<wbr>Def</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#updatetaskparam" class="tsd-kind-icon">Update<wbr>Task<wbr>Param</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#workobject" class="tsd-kind-icon">Work<wbr>Object</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#workrecord" class="tsd-kind-icon">Work<wbr>Record</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#workflowexecutecontext" class="tsd-kind-icon">Workflow<wbr>Execute<wbr>Context</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#workflowfilter-1" class="tsd-kind-icon">Workflow<wbr>Filter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#workflowlistmeta" class="tsd-kind-icon">Workflow<wbr>List<wbr>Meta</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#exportexcelparams" class="tsd-kind-icon">export<wbr>Excel<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="workflow2.html#listoperationparam" class="tsd-kind-icon">list<wbr>Operation<wbr>Param</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Type aliases</h2>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="actioninfo" class="tsd-anchor"></a>
					<h3>Action<wbr>Info</h3>
					<div class="tsd-signature tsd-kind-icon">Action<wbr>Info<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2952</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="addremarkparam" class="tsd-anchor"></a>
					<h3>Add<wbr>Remark<wbr>Param</h3>
					<div class="tsd-signature tsd-kind-icon">Add<wbr>Remark<wbr>Param<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>attachments<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fromScene<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>processId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>remark<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2933</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> attachments<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>附件文件相对路径。多个文件用逗号分割</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> from<wbr>Scene<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>评论来源，可以为空</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>process<wbr>Id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>remark<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="associateprocessinfo" class="tsd-anchor"></a>
					<h3>Associate<wbr>Process<wbr>Info</h3>
					<div class="tsd-signature tsd-kind-icon">Associate<wbr>Process<wbr>Info<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>associateId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2473</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>associate<wbr>Id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>process<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>0-未启动
											1-处理中
										2-已结束</p>
									</div>
								</div>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="batchfaildetail" class="tsd-anchor"></a>
					<h3>Batch<wbr>Fail<wbr>Detail</h3>
					<div class="tsd-signature tsd-kind-icon">Batch<wbr>Fail<wbr>Detail<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>message<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2915</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>message<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
					<a name="batchparam" class="tsd-anchor"></a>
					<h3>Batch<wbr>Param</h3>
					<div class="tsd-signature tsd-kind-icon">Batch<wbr>Param&lt;T&gt;<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2910</li>
						</ul>
					</aside>
					<h4 class="tsd-type-parameters-title">Type parameters</h4>
					<ul class="tsd-type-parameters">
						<li>
							<h4>T</h4>
						</li>
					</ul>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">T</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="batchresult" class="tsd-anchor"></a>
					<h3>Batch<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">Batch<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>fail<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>failDetails<span class="tsd-signature-symbol">: </span><a href="workflow2.html#batchfaildetail" class="tsd-signature-type">BatchFailDetail</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>success<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2921</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>fail<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>fail<wbr>Details<span class="tsd-signature-symbol">: </span><a href="workflow2.html#batchfaildetail" class="tsd-signature-type">BatchFailDetail</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>success<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="canceltaskparam" class="tsd-anchor"></a>
					<h3>Cancel<wbr>Task<wbr>Param</h3>
					<div class="tsd-signature tsd-kind-icon">Cancel<wbr>Task<wbr>Param<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>processId<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>remark<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>task<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2624</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> process<wbr>Id<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> remark<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> task<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="changeprocessstateparam" class="tsd-anchor"></a>
					<h3>Change<wbr>Process<wbr>State<wbr>Param</h3>
					<div class="tsd-signature tsd-kind-icon">Change<wbr>Process<wbr>State<wbr>Param<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>processId<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>remark<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2586</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> process<wbr>Id<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>remark<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>说明</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>待更换的状态</p>
									</div>
								</div>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="changetaskparam" class="tsd-anchor"></a>
					<h3>Change<wbr>Task<wbr>Param</h3>
					<div class="tsd-signature tsd-kind-icon">Change<wbr>Task<wbr>Param<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>currentTask<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>processId<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>remark<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>task<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2640</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>current<wbr>Task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> process<wbr>Id<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> remark<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>评论</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> task<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="datasourcewithcount" class="tsd-anchor"></a>
					<h3>Datasource<wbr>With<wbr>Count</h3>
					<div class="tsd-signature tsd-kind-icon">Datasource<wbr>With<wbr>Count<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>datasource<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>masterCount<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>todoCount<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2975</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>datasource<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>数据源</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>master<wbr>Count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>我负责的数量</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>todo<wbr>Count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>我的代办的数量</p>
									</div>
								</div>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="dealer" class="tsd-anchor"></a>
					<h3>Dealer</h3>
					<div class="tsd-signature tsd-kind-icon">Dealer<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2928</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="finishtaskparam" class="tsd-anchor"></a>
					<h3>Finish<wbr>Task<wbr>Param</h3>
					<div class="tsd-signature tsd-kind-icon">Finish<wbr>Task<wbr>Param<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>nextState<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>nextTask<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#updatetaskparam" class="tsd-signature-type">UpdateTaskParam</a><span class="tsd-signature-symbol">; </span>processId<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>remark<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>task<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2631</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>next<wbr>State<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> next<wbr>Task<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#updatetaskparam" class="tsd-signature-type">UpdateTaskParam</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> process<wbr>Id<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> remark<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> task<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="homeworkflowcount" class="tsd-anchor"></a>
					<h3>Home<wbr>Workflow<wbr>Count</h3>
					<div class="tsd-signature tsd-kind-icon">Home<wbr>Workflow<wbr>Count<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>masterCount<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>todoCount<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2990</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>master<wbr>Count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>我负责的统计数量</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>todo<wbr>Count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>待办的统计数量</p>
									</div>
								</div>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="listqueryparam" class="tsd-anchor"></a>
					<h3>List<wbr>Query<wbr>Param</h3>
					<div class="tsd-signature tsd-kind-icon">List<wbr>Query<wbr>Param<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>columns<span class="tsd-signature-symbol">?: </span><a href="../globals.html#orderobj.__type.column" class="tsd-signature-type">column</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>detailName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>filters<span class="tsd-signature-symbol">?: </span><a href="tools.html#filterstype" class="tsd-signature-type">FiltersType</a><span class="tsd-signature-symbol">; </span>listName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>pageIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>pageSize<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">?: </span><a href="../globals.html#prefilter" class="tsd-signature-type">prefilter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>sorts<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tagFilters<span class="tsd-signature-symbol">?: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">; </span>workflowFilter<span class="tsd-signature-symbol">: </span><a href="workflow2.html#workflowfilter-1" class="tsd-signature-type">WorkflowFilter</a><span class="tsd-signature-symbol">; </span>workflowListName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2669</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>列表查询的参数</p>
						</div>
					</div>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> columns<span class="tsd-signature-symbol">?: </span><a href="../globals.html#orderobj.__type.column" class="tsd-signature-type">column</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> detail<wbr>Name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> filters<span class="tsd-signature-symbol">?: </span><a href="tools.html#filterstype" class="tsd-signature-type">FiltersType</a></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>过滤器</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> list<wbr>Name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>page<wbr>Index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>page<wbr>Size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> prefilters<span class="tsd-signature-symbol">?: </span><a href="../globals.html#prefilter" class="tsd-signature-type">prefilter</a><span class="tsd-signature-symbol">[]</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>前置过滤器</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> sorts<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> tag<wbr>Filters<span class="tsd-signature-symbol">?: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-symbol">]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>workflow<wbr>Filter<span class="tsd-signature-symbol">: </span><a href="workflow2.html#workflowfilter-1" class="tsd-signature-type">WorkflowFilter</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> workflow<wbr>List<wbr>Name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="listresult" class="tsd-anchor"></a>
					<h3>List<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">List<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>exportTemplateNames<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>fieldGroups<span class="tsd-signature-symbol">?: </span><a href="../globals.html#field_group" class="tsd-signature-type">field_group</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>filters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>hasRight<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>hasWorkflowCreateRight<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>headers<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>meta<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#workflowlistmeta" class="tsd-signature-type">WorkflowListMeta</a><span class="tsd-signature-symbol">; </span>online<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span>pageIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>pageSize<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>processWithCounts<span class="tsd-signature-symbol">: </span><a href="workflow2.html#processwithcount" class="tsd-signature-type">ProcessWithCount</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>recordCount<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>rows<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="workflow2.html#listrowitem" class="tsd-signature-type">ListRowItem</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>sortDefs<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>subProjectName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>tagGroups<span class="tsd-signature-symbol">?: </span><a href="tagmanagertypes.html#taggroup-1" class="tsd-signature-type">TagGroup</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2775</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> export<wbr>Template<wbr>Names<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> field<wbr>Groups<span class="tsd-signature-symbol">?: </span><a href="../globals.html#field_group" class="tsd-signature-type">field_group</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>filters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> has<wbr>Right<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> has<wbr>Workflow<wbr>Create<wbr>Right<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> headers<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> meta<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#workflowlistmeta" class="tsd-signature-type">WorkflowListMeta</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> online<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">any</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>page<wbr>Index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>page<wbr>Size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>process<wbr>With<wbr>Counts<span class="tsd-signature-symbol">: </span><a href="workflow2.html#processwithcount" class="tsd-signature-type">ProcessWithCount</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>record<wbr>Count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>rows<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="workflow2.html#listrowitem" class="tsd-signature-type">ListRowItem</a><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> sort<wbr>Defs<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> sub<wbr>Project<wbr>Name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> tag<wbr>Groups<span class="tsd-signature-symbol">?: </span><a href="tagmanagertypes.html#taggroup-1" class="tsd-signature-type">TagGroup</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="listrowitem" class="tsd-anchor"></a>
					<h3>List<wbr>Row<wbr>Item</h3>
					<div class="tsd-signature tsd-kind-icon">List<wbr>Row<wbr>Item<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">; </span>fieldGroup<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">; </span>objects<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="workflow2.html#workobject" class="tsd-signature-type">WorkObject</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2769</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> field<wbr>Group<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> objects<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="workflow2.html#workobject" class="tsd-signature-type">WorkObject</a><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="processdef" class="tsd-anchor"></a>
					<h3>Process<wbr>Def</h3>
					<div class="tsd-signature tsd-kind-icon">Process<wbr>Def<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>cancellable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>changeable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>defaultMasterHandle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>description<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>editable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>group<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>groups<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="workflow2.html#stategroup" class="tsd-signature-type">StateGroup</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>modelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>preHandle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>stateChangeCallback<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>states<span class="tsd-signature-symbol">: </span><a href="workflow2.html#statedef" class="tsd-signature-type">StateDef</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tasks<span class="tsd-signature-symbol">: </span><a href="workflow2.html#taskdef-1" class="tsd-signature-type">TaskDef</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2539</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>流程定义</p>
						</div>
					</div>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>cancellable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>任务可取消</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>changeable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>任务可更换</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>default<wbr>Master<wbr>Handle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>默认负责人钩子</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> description<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>editable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>任务可编辑</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> group<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> groups<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="workflow2.html#stategroup" class="tsd-signature-type">StateGroup</a><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>model<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>pre<wbr>Handle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>前置条件钩子</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>process<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> state<wbr>Change<wbr>Callback<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>states<span class="tsd-signature-symbol">: </span><a href="workflow2.html#statedef" class="tsd-signature-type">StateDef</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tasks<span class="tsd-signature-symbol">: </span><a href="workflow2.html#taskdef-1" class="tsd-signature-type">TaskDef</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="processdetail" class="tsd-anchor"></a>
					<h3>Process<wbr>Detail</h3>
					<div class="tsd-signature tsd-kind-icon">Process<wbr>Detail<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>actions<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>canRollback<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>cancellable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>changeable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>complete<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>detail<span class="tsd-signature-symbol">: </span><a href="workflow2.html#listrowitem" class="tsd-signature-type">ListRowItem</a><span class="tsd-signature-symbol">; </span>detailEditable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>editable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>endStates<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>groups<span class="tsd-signature-symbol">: </span><a href="workflow2.html#stategroup" class="tsd-signature-type">StateGroup</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>hasWorkflowDetail<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>states<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>taskDef<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#taskdef-1" class="tsd-signature-type">TaskDef</a><span class="tsd-signature-symbol">; </span>tasks<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2807</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>actions<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> can<wbr>Rollback<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>true: 可以撤回</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>cancellable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>任务可取消</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>changeable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>任务可更换</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>complete<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>true 流程已完成 false 流程未完成</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>detail<span class="tsd-signature-symbol">: </span><a href="workflow2.html#listrowitem" class="tsd-signature-type">ListRowItem</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5>detail<wbr>Editable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>true 说明当前可编辑
										false 无编辑权限，只能查看信息</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>editable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>任务可编辑</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>end<wbr>States<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>终结节点的状态</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>groups<span class="tsd-signature-symbol">: </span><a href="workflow2.html#stategroup" class="tsd-signature-type">StateGroup</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> has<wbr>Workflow<wbr>Detail<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>true 包含工作流规约的详情页</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>states<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> task<wbr>Def<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#taskdef-1" class="tsd-signature-type">TaskDef</a></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>当前任务的定义，如果存在</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>tasks<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="processeditparam" class="tsd-anchor"></a>
					<h3>Process<wbr>Edit<wbr>Param</h3>
					<div class="tsd-signature tsd-kind-icon">Process<wbr>Edit<wbr>Param<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>action<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>cancellable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>changeable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>dataSource<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>description<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>editable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>groups<span class="tsd-signature-symbol">: </span><a href="workflow2.html#stategroup" class="tsd-signature-type">StateGroup</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>relations<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#processrelation" class="tsd-signature-type">ProcessRelation</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>stateChangeCallback<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>superAdmin<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>tasks<span class="tsd-signature-symbol">: </span><a href="workflow2.html#taskdef-1" class="tsd-signature-type">TaskDef</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2728</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> action<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>前置条件，对应的 action 名称</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>cancellable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>任务可取消</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>changeable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>任务可更换</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> data<wbr>Source<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> description<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>editable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>任务可编辑</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>groups<span class="tsd-signature-symbol">: </span><a href="workflow2.html#stategroup" class="tsd-signature-type">StateGroup</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>process<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> relations<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#processrelation" class="tsd-signature-type">ProcessRelation</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> state<wbr>Change<wbr>Callback<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>流程变动回调配置</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>super<wbr>Admin<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tasks<span class="tsd-signature-symbol">: </span><a href="workflow2.html#taskdef-1" class="tsd-signature-type">TaskDef</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="processinfo" class="tsd-anchor"></a>
					<h3>Process<wbr>Info</h3>
					<div class="tsd-signature tsd-kind-icon">Process<wbr>Info<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>action<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>cancellable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>changeable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>dataSource<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>description<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>editable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>groups<span class="tsd-signature-symbol">: </span><a href="workflow2.html#stategroup" class="tsd-signature-type">StateGroup</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>modelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>relations<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#processrelation" class="tsd-signature-type">ProcessRelation</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>ruleOfDistribution<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">; </span>states<span class="tsd-signature-symbol">: </span><a href="workflow2.html#statedef" class="tsd-signature-type">StateDef</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>superAdmin<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>tasks<span class="tsd-signature-symbol">: </span><a href="workflow2.html#taskdef-1" class="tsd-signature-type">TaskDef</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2693</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>action<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>cancellable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>任务可取消</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>changeable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>任务可更换</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> data<wbr>Source<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> description<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>editable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>任务可编辑</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>groups<span class="tsd-signature-symbol">: </span><a href="workflow2.html#stategroup" class="tsd-signature-type">StateGroup</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>model<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>process<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> relations<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#processrelation" class="tsd-signature-type">ProcessRelation</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> rule<wbr>OfDistribution<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-symbol">]</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>处理人分配的规则</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>states<span class="tsd-signature-symbol">: </span><a href="workflow2.html#statedef" class="tsd-signature-type">StateDef</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>super<wbr>Admin<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tasks<span class="tsd-signature-symbol">: </span><a href="workflow2.html#taskdef-1" class="tsd-signature-type">TaskDef</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="processinstancepanel" class="tsd-anchor"></a>
					<h3>Process<wbr>Instance<wbr>Panel</h3>
					<div class="tsd-signature tsd-kind-icon">Process<wbr>Instance<wbr>Panel<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>dealerName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2956</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>dealer<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>process<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="processoperation" class="tsd-anchor"></a>
					<h3>Process<wbr>Operation</h3>
					<div class="tsd-signature tsd-kind-icon">Process<wbr>Operation<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>afterOperation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>beforeOperation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>createTime<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>dealer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>dealerName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>processId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>remark<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>remarkId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2852</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>after<wbr>Operation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>before<wbr>Operation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>create<wbr>Time<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>dealer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>dealer<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>process<wbr>Id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>remark<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>remark<wbr>Id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="processrelation" class="tsd-anchor"></a>
					<h3>Process<wbr>Relation</h3>
					<div class="tsd-signature tsd-kind-icon">Process<wbr>Relation<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>items<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2722</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>items<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="processwithcount" class="tsd-anchor"></a>
					<h3>Process<wbr>With<wbr>Count</h3>
					<div class="tsd-signature tsd-kind-icon">Process<wbr>With<wbr>Count<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>editable<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>online<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2894</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> editable<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> online<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>当前用户该流程是否在线</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>process<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="remark-6" class="tsd-anchor"></a>
					<h3>Remark</h3>
					<div class="tsd-signature tsd-kind-icon">Remark<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>attachments<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>content<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>createTime<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>dealerName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fromScene<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>processId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2879</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> attachments<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>附件文件相对路径。多个文件会用逗号分割存储，使用时需要手动拆分</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>content<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>create<wbr>Time<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>dealer<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>from<wbr>Scene<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>process<wbr>Id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="startcheckresult" class="tsd-anchor"></a>
					<h3>Start<wbr>Check<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">Start<wbr>Check<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>existCount<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>states<span class="tsd-signature-symbol">: </span><a href="workflow2.html#statedef" class="tsd-signature-type">StateDef</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tasks<span class="tsd-signature-symbol">: </span><a href="workflow2.html#taskdef-1" class="tsd-signature-type">TaskDef</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2904</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>exist<wbr>Count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>states<span class="tsd-signature-symbol">: </span><a href="workflow2.html#statedef" class="tsd-signature-type">StateDef</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tasks<span class="tsd-signature-symbol">: </span><a href="workflow2.html#taskdef-1" class="tsd-signature-type">TaskDef</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="startprocessparam" class="tsd-anchor"></a>
					<h3>Start<wbr>Process<wbr>Param</h3>
					<div class="tsd-signature tsd-kind-icon">Start<wbr>Process<wbr>Param<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>associateId<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>days<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>dealer<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>dealerName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>description<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>dispatchType<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>remark<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>task<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>taskBeginTime<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>taskEndTime<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2571</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> associate<wbr>Id<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> days<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> dealer<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> dealer<wbr>Name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> description<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> dispatch<wbr>Type<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>process<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> remark<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> task<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> task<wbr>Begin<wbr>Time<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> task<wbr>End<wbr>Time<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="statedef" class="tsd-anchor"></a>
					<h3>State<wbr>Def</h3>
					<div class="tsd-signature tsd-kind-icon">State<wbr>Def<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>afterTasks<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Set</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>description<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>disable<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>end<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>group<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>order<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>stateName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>tasks<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#taskdef-1" class="tsd-signature-type">TaskDef</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2488</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>状态</p>
						</div>
					</div>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> after<wbr>Tasks<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Set</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>任务后续状态</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> description<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> disable<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>是否禁用</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>end<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>true 终结节点</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> group<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>分组名称</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> order<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>排序字段</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>state<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> tasks<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#taskdef-1" class="tsd-signature-type">TaskDef</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="stategroup" class="tsd-anchor"></a>
					<h3>State<wbr>Group</h3>
					<div class="tsd-signature tsd-kind-icon">State<wbr>Group<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>order<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>states<span class="tsd-signature-symbol">: </span><a href="workflow2.html#statedef" class="tsd-signature-type">StateDef</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2758</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> order<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>states<span class="tsd-signature-symbol">: </span><a href="workflow2.html#statedef" class="tsd-signature-type">StateDef</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="taskdef-1" class="tsd-anchor"></a>
					<h3>Task<wbr>Def</h3>
					<div class="tsd-signature tsd-kind-icon">Task<wbr>Def<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>afterStates<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Set</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>defaultDays<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>defaultDealerHandle<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>description<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>disable<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>states<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#statedef" class="tsd-signature-type">StateDef</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>taskName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2517</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>任务</p>
						</div>
					</div>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> after<wbr>States<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Set</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>任务后续状态</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> default<wbr>Days<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>任务的默认处理时间,天</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> default<wbr>Dealer<wbr>Handle<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>任务默认的处理人</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> description<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> disable<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> states<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#statedef" class="tsd-signature-type">StateDef</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>task<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="updatetaskparam" class="tsd-anchor"></a>
					<h3>Update<wbr>Task<wbr>Param</h3>
					<div class="tsd-signature tsd-kind-icon">Update<wbr>Task<wbr>Param<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>days<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>dealer<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>dealerName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>newTask<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>processId<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>remark<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>task<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>taskBeginTime<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>taskEndTime<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2598</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> days<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>选择的处理天数</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> dealer<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> dealer<wbr>Name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> new<wbr>Task<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>创建任务时的新任务</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> process<wbr>Id<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> remark<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>备注</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> task<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>流程的当前任务，创建时应该是空</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> task<wbr>Begin<wbr>Time<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> task<wbr>End<wbr>Time<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="workobject" class="tsd-anchor"></a>
					<h3>Work<wbr>Object</h3>
					<div class="tsd-signature tsd-kind-icon">Work<wbr>Object<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>display<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2764</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>display<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="workrecord" class="tsd-anchor"></a>
					<h3>Work<wbr>Record</h3>
					<div class="tsd-signature tsd-kind-icon">Work<wbr>Record<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>completer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>completerName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>createTime<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>creator<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>creatorName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>finishState<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>finishTime<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2867</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>completer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>completer<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>create<wbr>Time<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>creator<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>creator<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>finish<wbr>State<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>finish<wbr>Time<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="workflowexecutecontext" class="tsd-anchor"></a>
					<h3>Workflow<wbr>Execute<wbr>Context</h3>
					<div class="tsd-signature tsd-kind-icon">Workflow<wbr>Execute<wbr>Context<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>associateId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>dealer<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>finish<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>processId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>task<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2963</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>associate<wbr>Id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> dealer<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>finish<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>是否完成</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>process<wbr>Id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> task<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="workflowfilter-1" class="tsd-anchor"></a>
					<h3>Workflow<wbr>Filter</h3>
					<div class="tsd-signature tsd-kind-icon">Workflow<wbr>Filter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>instanceState<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>processName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>queryType<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>state<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>task<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>taskBeginTime<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>taskEndTime<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2654</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>工作流过滤条件</p>
						</div>
					</div>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>instance<wbr>State<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> process<wbr>Name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>query<wbr>Type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>all 全部, to-do 待办, relation 已办</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> state<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> task<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>task<wbr>Begin<wbr>Time<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>task<wbr>End<wbr>Time<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="workflowlistmeta" class="tsd-anchor"></a>
					<h3>Workflow<wbr>List<wbr>Meta</h3>
					<div class="tsd-signature tsd-kind-icon">Workflow<wbr>List<wbr>Meta<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>appointProcessName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>detailName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>manualLoadData<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>modelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>processNameListRight<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>showSwitchComponent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2797</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>appoint<wbr>Process<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> detail<wbr>Name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> manual<wbr>Load<wbr>Data<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>model<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>process<wbr>Name<wbr>List<wbr>Right<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>show<wbr>Switch<wbr>Component<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="exportexcelparams" class="tsd-anchor"></a>
					<h3>export<wbr>Excel<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">export<wbr>Excel<wbr>Params<span class="tsd-signature-symbol">:</span> <a href="workflow2.html#listqueryparam" class="tsd-signature-type">ListQueryParam</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>templateName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2689</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="listoperationparam" class="tsd-anchor"></a>
					<h3>list<wbr>Operation<wbr>Param</h3>
					<div class="tsd-signature tsd-kind-icon">list<wbr>Operation<wbr>Param<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>beginTime<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>endTime<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2946</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> begin<wbr>Time<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> end<wbr>Time<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> type<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
						</ul>
					</div>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
					<li class="current tsd-kind-namespace">
						<a href="workflow2.html">workflow2</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#actioninfo" class="tsd-kind-icon">Action<wbr>Info</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#addremarkparam" class="tsd-kind-icon">Add<wbr>Remark<wbr>Param</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#associateprocessinfo" class="tsd-kind-icon">Associate<wbr>Process<wbr>Info</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#batchfaildetail" class="tsd-kind-icon">Batch<wbr>Fail<wbr>Detail</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace tsd-has-type-parameter">
						<a href="workflow2.html#batchparam" class="tsd-kind-icon">Batch<wbr>Param</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#batchresult" class="tsd-kind-icon">Batch<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#canceltaskparam" class="tsd-kind-icon">Cancel<wbr>Task<wbr>Param</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#changeprocessstateparam" class="tsd-kind-icon">Change<wbr>Process<wbr>State<wbr>Param</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#changetaskparam" class="tsd-kind-icon">Change<wbr>Task<wbr>Param</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#datasourcewithcount" class="tsd-kind-icon">Datasource<wbr>With<wbr>Count</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#dealer" class="tsd-kind-icon">Dealer</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#finishtaskparam" class="tsd-kind-icon">Finish<wbr>Task<wbr>Param</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#homeworkflowcount" class="tsd-kind-icon">Home<wbr>Workflow<wbr>Count</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#listqueryparam" class="tsd-kind-icon">List<wbr>Query<wbr>Param</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#listresult" class="tsd-kind-icon">List<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#listrowitem" class="tsd-kind-icon">List<wbr>Row<wbr>Item</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#processdef" class="tsd-kind-icon">Process<wbr>Def</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#processdetail" class="tsd-kind-icon">Process<wbr>Detail</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#processeditparam" class="tsd-kind-icon">Process<wbr>Edit<wbr>Param</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#processinfo" class="tsd-kind-icon">Process<wbr>Info</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#processinstancepanel" class="tsd-kind-icon">Process<wbr>Instance<wbr>Panel</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#processoperation" class="tsd-kind-icon">Process<wbr>Operation</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#processrelation" class="tsd-kind-icon">Process<wbr>Relation</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#processwithcount" class="tsd-kind-icon">Process<wbr>With<wbr>Count</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#remark-6" class="tsd-kind-icon">Remark</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#startcheckresult" class="tsd-kind-icon">Start<wbr>Check<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#startprocessparam" class="tsd-kind-icon">Start<wbr>Process<wbr>Param</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#statedef" class="tsd-kind-icon">State<wbr>Def</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#stategroup" class="tsd-kind-icon">State<wbr>Group</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#taskdef-1" class="tsd-kind-icon">Task<wbr>Def</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#updatetaskparam" class="tsd-kind-icon">Update<wbr>Task<wbr>Param</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#workobject" class="tsd-kind-icon">Work<wbr>Object</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#workrecord" class="tsd-kind-icon">Work<wbr>Record</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#workflowexecutecontext" class="tsd-kind-icon">Workflow<wbr>Execute<wbr>Context</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#workflowfilter-1" class="tsd-kind-icon">Workflow<wbr>Filter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#workflowlistmeta" class="tsd-kind-icon">Workflow<wbr>List<wbr>Meta</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#exportexcelparams" class="tsd-kind-icon">export<wbr>Excel<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="workflow2.html#listoperationparam" class="tsd-kind-icon">list<wbr>Operation<wbr>Param</a>
					</li>
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>