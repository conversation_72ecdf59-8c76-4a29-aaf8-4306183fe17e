<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>List | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="list.html">List</a>
				</li>
			</ul>
			<h1>Class List&lt;RowType&gt;</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-type-parameters">
				<h3>Type parameters</h3>
				<ul class="tsd-type-parameters">
					<li>
						<h4>RowType<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListRow</span> = <span class="tsd-signature-type">dto.ListRow</span></h4>
					</li>
				</ul>
			</section>
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<a href="anonymousmodel.html" class="tsd-signature-type">AnonymousModel</a>
						<ul class="tsd-hierarchy">
							<li>
								<span class="target">List</span>
								<ul class="tsd-hierarchy">
									<li>
										<a href="listeasy.html" class="tsd-signature-type">ListEasy</a>
									</li>
									<li>
										<a href="listhard.html" class="tsd-signature-type">ListHard</a>
									</li>
								</ul>
							</li>
						</ul>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="list.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="list.html#_listquery" class="tsd-kind-icon">_list<wbr>Query</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-overwrite tsd-is-protected"><a href="list.html#api" class="tsd-kind-icon">api</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="list.html#isanonymous" class="tsd-kind-icon">is<wbr>Anonymous</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="list.html#lefttree" class="tsd-kind-icon">left<wbr>Tree</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="list.html#listqueryparams" class="tsd-kind-icon">list<wbr>Query<wbr>Params</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="list.html#metamodelname" class="tsd-kind-icon">meta<wbr>Model<wbr>Name</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="list.html#pagescolumns" class="tsd-kind-icon">pages<wbr>Columns</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="list.html#props" class="tsd-kind-icon">props</a></li>
							</ul>
						</section>
						<section class="tsd-index-section tsd-is-private-protected">
							<h3>Accessors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-get-signature tsd-parent-kind-class tsd-is-protected"><a href="list.html#modelname" class="tsd-kind-icon">model<wbr>Name</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="list.html#anonymous" class="tsd-kind-icon">anonymous</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="list.html#callbackonchange" class="tsd-kind-icon">callback<wbr>OnChange</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#exporttoexcel" class="tsd-kind-icon">export<wbr>ToExcel</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#exporttoexcelv2" class="tsd-kind-icon">export<wbr>ToExcel<wbr>V2</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#exporttoexcelv2forcsv" class="tsd-kind-icon">export<wbr>ToExcel<wbr>V2For<wbr>Csv</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#exporttoword" class="tsd-kind-icon">export<wbr>ToWord</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#fullfillparams" class="tsd-kind-icon">fullfill<wbr>Params</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#getalltemplateurl" class="tsd-kind-icon">get<wbr>Alltemplate<wbr>Url</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#getdefaulttemplateurl" class="tsd-kind-icon">get<wbr>Default<wbr>Template<wbr>Url</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#getfiltergroup" class="tsd-kind-icon">get<wbr>Filter<wbr>Group</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="list.html#getlistquery" class="tsd-kind-icon">get<wbr>List<wbr>Query</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#getpagecount" class="tsd-kind-icon">get<wbr>Page<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#getpagecountv2" class="tsd-kind-icon">get<wbr>Page<wbr>Count<wbr>V2</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#getrowdetail" class="tsd-kind-icon">get<wbr>Row<wbr>Detail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#getworkflow" class="tsd-kind-icon">get<wbr>Workflow</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><a href="list.html#handlefilters" class="tsd-kind-icon">handle<wbr>Filters</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="list.html#ispageindex" class="tsd-kind-icon">is<wbr>Page<wbr>Index</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="list.html#ontransportmessage" class="tsd-kind-icon">on<wbr>Transport<wbr>Message</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#postparamsforexcel" class="tsd-kind-icon">post<wbr>Params<wbr>For<wbr>Excel</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#query" class="tsd-kind-icon">query</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#querymeta" class="tsd-kind-icon">query<wbr>Meta</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#registeronchange" class="tsd-kind-icon">register<wbr>OnChange</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#removefilters" class="tsd-kind-icon">remove<wbr>Filters</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#setcolumnsforpages" class="tsd-kind-icon">set<wbr>Columns<wbr>For<wbr>Pages</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="list.html#unanonymous" class="tsd-kind-icon">un<wbr>Anonymous</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#updateaction" class="tsd-kind-icon">update<wbr>Action</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="list.html#updatefilterparam" class="tsd-kind-icon">update<wbr>Filter<wbr>Param</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new <wbr>List<span class="tsd-signature-symbol">(</span>props<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.constructor</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="list.html" class="tsd-signature-type">List</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:27</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>props: <span class="tsd-signature-type">dto.ListTypes.constructor</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="list.html" class="tsd-signature-type">List</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="_listquery" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _list<wbr>Query</h3>
					<div class="tsd-signature tsd-kind-icon">_list<wbr>Query<span class="tsd-signature-symbol">:</span> <a href="listquery.html" class="tsd-signature-type">ListQuery</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list.ts:22</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-overwrite tsd-is-protected">
					<a name="api" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagProtected">Protected</span> api</h3>
					<div class="tsd-signature tsd-kind-icon">api<span class="tsd-signature-symbol">:</span> <a href="listapi.html" class="tsd-signature-type">listApi</a></div>
					<aside class="tsd-sources">
						<p>Overrides <a href="anonymousmodel.html">AnonymousModel</a>.<a href="anonymousmodel.html#api">api</a></p>
						<ul>
							<li>Defined in src/model/list/list.ts:20</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited">
					<a name="isanonymous" class="tsd-anchor"></a>
					<h3>is<wbr>Anonymous</h3>
					<div class="tsd-signature tsd-kind-icon">is<wbr>Anonymous<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
						<p>Inherited from <a href="anonymousmodel.html">AnonymousModel</a>.<a href="anonymousmodel.html#isanonymous">isAnonymous</a></p>
						<ul>
							<li>Defined in src/core/anonymous-api.ts:18</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="lefttree" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> left<wbr>Tree</h3>
					<div class="tsd-signature tsd-kind-icon">left<wbr>Tree<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.ListTypes.getListDataRequestResult</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">"meta"</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">"tree"</span><span class="tsd-signature-symbol">]</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list.ts:25</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="listqueryparams" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> list<wbr>Query<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">list<wbr>Query<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.ListTypes.fullfilledQueryProps</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list.ts:23</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="metamodelname" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> meta<wbr>Model<wbr>Name</h3>
					<div class="tsd-signature tsd-kind-icon">meta<wbr>Model<wbr>Name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &quot;&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list.ts:27</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="pagescolumns" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> pages<wbr>Columns</h3>
					<div class="tsd-signature tsd-kind-icon">pages<wbr>Columns<span class="tsd-signature-symbol">:</span> <a href="../globals.html#pagescolumns" class="tsd-signature-type">PagesColumns</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list.ts:24</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="props" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> props</h3>
					<div class="tsd-signature tsd-kind-icon">props<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.ListTypes.constructor</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list.ts:29</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group tsd-is-private-protected">
				<h2>Accessors</h2>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class tsd-is-protected">
					<a name="modelname" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagProtected">Protected</span> model<wbr>Name</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class tsd-is-protected">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> modelName<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:34</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="anonymous" class="tsd-anchor"></a>
					<h3>anonymous</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">anonymous<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="anonymousmodel.html">AnonymousModel</a>.<a href="anonymousmodel.html#anonymous">anonymous</a></p>
								<ul>
									<li>Defined in src/core/anonymous-api.ts:19</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="callbackonchange" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> callback<wbr>OnChange</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">callback<wbr>OnChange<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:21</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="exporttoexcel" class="tsd-anchor"></a>
					<h3>export<wbr>ToExcel</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">export<wbr>ToExcel<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ListTypes.exportExcelParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">(Anonymous function)</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:265</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>导出列表为excel</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagDefault value">Default value</span> params: <span class="tsd-signature-type">dto.ListTypes.exportExcelParams</span><span class="tsd-signature-symbol"> = {}</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">(Anonymous function)</span></h4>
							<p>返回一个开始导出的方法startDownload, 新方法有一个参数用来监听导出进度
							返回的方法是一个Promise，resolve时得到exlce下载链接</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="exporttoexcelv2" class="tsd-anchor"></a>
					<h3>export<wbr>ToExcel<wbr>V2</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">export<wbr>ToExcel<wbr>V2<span class="tsd-signature-symbol">(</span>eid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, headers<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>CurrentOrg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">(Anonymous function)</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:290</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>exportToExcelV2</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eid: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> headers: <span class="tsd-signature-symbol">{ </span>CurrentOrg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> Current<wbr>Org<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">(Anonymous function)</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="exporttoexcelv2forcsv" class="tsd-anchor"></a>
					<h3>export<wbr>ToExcel<wbr>V2For<wbr>Csv</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">export<wbr>ToExcel<wbr>V2For<wbr>Csv<span class="tsd-signature-symbol">(</span>eid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, headers<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>CurrentOrg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">(Anonymous function)</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:307</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>导出为csv</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eid: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> headers: <span class="tsd-signature-symbol">{ </span>CurrentOrg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> Current<wbr>Org<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">(Anonymous function)</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="exporttoword" class="tsd-anchor"></a>
					<h3>export<wbr>ToWord</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">export<wbr>ToWord<span class="tsd-signature-symbol">(</span>eid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, headers<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>CurrentOrg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">(Anonymous function)</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:297</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eid: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> headers: <span class="tsd-signature-symbol">{ </span>CurrentOrg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> Current<wbr>Org<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">(Anonymous function)</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="fullfillparams" class="tsd-anchor"></a>
					<h3>fullfill<wbr>Params</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">fullfill<wbr>Params<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.queryProps</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../modules/listtypes.html#fullfilledqueryprops" class="tsd-signature-type">fullfilledQueryProps</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:67</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.queryProps</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="../modules/listtypes.html#fullfilledqueryprops" class="tsd-signature-type">fullfilledQueryProps</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getalltemplateurl" class="tsd-anchor"></a>
					<h3>get<wbr>Alltemplate<wbr>Url</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Alltemplate<wbr>Url<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:357</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>得到所有模版</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>Promise<url></p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getdefaulttemplateurl" class="tsd-anchor"></a>
					<h3>get<wbr>Default<wbr>Template<wbr>Url</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Default<wbr>Template<wbr>Url<span class="tsd-signature-symbol">(</span>tabName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:350</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>得到某个标签页的模版</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagDefault value">Default value</span> tabName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &quot;&quot;</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>Promise<url></p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getfiltergroup" class="tsd-anchor"></a>
					<h3>get<wbr>Filter<wbr>Group</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Filter<wbr>Group<span class="tsd-signature-symbol">(</span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#filtergroup" class="tsd-signature-type">filterGroup</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:241</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>property: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>属性名</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#filtergroup" class="tsd-signature-type">filterGroup</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="getlistquery" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> get<wbr>List<wbr>Query</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">get<wbr>List<wbr>Query<span class="tsd-signature-symbol">(</span>listQueryParams<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.queryProps</span>, isMeta<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#iqueryresult" class="tsd-signature-type">IQueryResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:157</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>listQueryParams: <span class="tsd-signature-type">dto.ListTypes.queryProps</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> isMeta: <span class="tsd-signature-type">boolean</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#iqueryresult" class="tsd-signature-type">IQueryResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getpagecount" class="tsd-anchor"></a>
					<h3>get<wbr>Page<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Page<wbr>Count<span class="tsd-signature-symbol">(</span>listQueryParams<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ListTypes.queryProps</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>page_datas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:189</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> listQueryParams: <span class="tsd-signature-type">dto.ListTypes.queryProps</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>page_datas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getpagecountv2" class="tsd-anchor"></a>
					<h3>get<wbr>Page<wbr>Count<wbr>V2</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Page<wbr>Count<wbr>V2<span class="tsd-signature-symbol">(</span>listQueryParams<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ListTypes.queryPropsEasy</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>page_datas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:198</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> listQueryParams: <span class="tsd-signature-type">dto.ListTypes.queryPropsEasy</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>page_datas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getrowdetail" class="tsd-anchor"></a>
					<h3>get<wbr>Row<wbr>Detail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Row<wbr>Detail<span class="tsd-signature-symbol">(</span>keyFieldValue<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:255</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取行详情
									keyFieldValue: row的keyfield的值</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>keyFieldValue: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getworkflow" class="tsd-anchor"></a>
					<h3>get<wbr>Workflow</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Workflow<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.getWorkflow</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="workflow.html" class="tsd-signature-type">Workflow</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:41</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取工作流类</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.getWorkflow</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="workflow.html" class="tsd-signature-type">Workflow</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-protected">
					<a name="handlefilters" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagProtected">Protected</span> handle<wbr>Filters</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-protected">
						<li class="tsd-signature tsd-kind-icon">handle<wbr>Filters<span class="tsd-signature-symbol">(</span>keyValueFilter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.KeyValueFilters</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../globals.html#filter" class="tsd-signature-type">filter</a><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:365</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>传入简单的键值对数组，放回sdk所需的filters参数</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>keyValueFilter: <span class="tsd-signature-type">dto.ListTypes.KeyValueFilters</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="../globals.html#filter" class="tsd-signature-type">filter</a><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="ispageindex" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> is<wbr>Page<wbr>Index</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">is<wbr>Page<wbr>Index<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.queryProps</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">params</span><span class="tsd-signature-symbol"> is </span><span class="tsd-signature-type">dto.ListTypes.queryPropsPage</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:61</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.queryProps</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">params</span><span class="tsd-signature-symbol"> is </span><span class="tsd-signature-type">dto.ListTypes.queryPropsPage</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="ontransportmessage" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> on<wbr>Transport<wbr>Message</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">on<wbr>Transport<wbr>Message<span class="tsd-signature-symbol">(</span>msg<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.msg</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:91</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>msg: <span class="tsd-signature-type">dto.SSE.msg</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="postparamsforexcel" class="tsd-anchor"></a>
					<h3>post<wbr>Params<wbr>For<wbr>Excel</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">post<wbr>Params<wbr>For<wbr>Excel<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ListTypes.exportExcelParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:320</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>postParamsForExcel</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagDefault value">Default value</span> params: <span class="tsd-signature-type">dto.ListTypes.exportExcelParams</span><span class="tsd-signature-symbol"> = {}</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="query" class="tsd-anchor"></a>
					<h3>query</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<span class="tsd-signature-symbol">(</span>listQueryParams<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.queryProps</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#iqueryresult" class="tsd-signature-type">IQueryResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:149</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>查询list页面的基本数据
										返回值中pageData是页面基本数据
										getList是获取列表的方法。
										查询条件一定不变化时，可以使用getlist方法进行分页查询
									查询条件变化，就有重新调用本方法</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>listQueryParams: <span class="tsd-signature-type">dto.ListTypes.queryProps</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#iqueryresult" class="tsd-signature-type">IQueryResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="querymeta" class="tsd-anchor"></a>
					<h3>query<wbr>Meta</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<wbr>Meta<span class="tsd-signature-symbol">(</span>listQueryParams<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.queryProps</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#iqueryresult" class="tsd-signature-type">IQueryResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:153</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>listQueryParams: <span class="tsd-signature-type">dto.ListTypes.queryProps</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#iqueryresult" class="tsd-signature-type">IQueryResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="registeronchange" class="tsd-anchor"></a>
					<h3>register<wbr>OnChange</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">register<wbr>OnChange<span class="tsd-signature-symbol">(</span>cb<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.callBackOfModelUpdated</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:136</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>注册当前模型的数据变化时的回掉</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>cb: <span class="tsd-signature-type">dto.SSE.callBackOfModelUpdated</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="removefilters" class="tsd-anchor"></a>
					<h3>remove<wbr>Filters</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">remove<wbr>Filters<span class="tsd-signature-symbol">(</span>items<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.filter</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:376</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>items: <span class="tsd-signature-type">dto.filter</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="setcolumnsforpages" class="tsd-anchor"></a>
					<h3>set<wbr>Columns<wbr>For<wbr>Pages</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Columns<wbr>For<wbr>Pages<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.setColumnsForPagesParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:245</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.setColumnsForPagesParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="unanonymous" class="tsd-anchor"></a>
					<h3>un<wbr>Anonymous</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">un<wbr>Anonymous<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="anonymousmodel.html">AnonymousModel</a>.<a href="anonymousmodel.html#unanonymous">unAnonymous</a></p>
								<ul>
									<li>Defined in src/core/anonymous-api.ts:23</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updateaction" class="tsd-anchor"></a>
					<h3>update<wbr>Action</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Action<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.updateAction</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#updateactionrequestresult" class="tsd-signature-type">updateActionRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:120</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>更新action</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.updateAction</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#updateactionrequestresult" class="tsd-signature-type">updateActionRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updatefilterparam" class="tsd-anchor"></a>
					<h3>update<wbr>Filter<wbr>Param</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Filter<wbr>Param<span class="tsd-signature-symbol">(</span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, filters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.filter</span><span class="tsd-signature-symbol">[]</span>, item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#updatefilterparamrequestresult" class="tsd-signature-type">updateFilterParamRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list.ts:221</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>如果查询条件中有一项可以跟随另一项自动填写，则调用本方法</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>property: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>filters: <span class="tsd-signature-type">dto.filter</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
								<li>
									<h5>item_index: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>item_size: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#updatefilterparamrequestresult" class="tsd-signature-type">updateFilterParamRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class tsd-has-type-parameter">
						<a href="list.html" class="tsd-kind-icon">List</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class">
								<a href="list.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="list.html#_listquery" class="tsd-kind-icon">_list<wbr>Query</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-overwrite tsd-is-protected">
								<a href="list.html#api" class="tsd-kind-icon">api</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-inherited">
								<a href="list.html#isanonymous" class="tsd-kind-icon">is<wbr>Anonymous</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="list.html#lefttree" class="tsd-kind-icon">left<wbr>Tree</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="list.html#listqueryparams" class="tsd-kind-icon">list<wbr>Query<wbr>Params</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="list.html#metamodelname" class="tsd-kind-icon">meta<wbr>Model<wbr>Name</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="list.html#pagescolumns" class="tsd-kind-icon">pages<wbr>Columns</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="list.html#props" class="tsd-kind-icon">props</a>
							</li>
							<li class=" tsd-kind-get-signature tsd-parent-kind-class tsd-is-protected">
								<a href="list.html#modelname" class="tsd-kind-icon">model<wbr>Name</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="list.html#anonymous" class="tsd-kind-icon">anonymous</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="list.html#callbackonchange" class="tsd-kind-icon">callback<wbr>OnChange</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#exporttoexcel" class="tsd-kind-icon">export<wbr>ToExcel</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#exporttoexcelv2" class="tsd-kind-icon">export<wbr>ToExcel<wbr>V2</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#exporttoexcelv2forcsv" class="tsd-kind-icon">export<wbr>ToExcel<wbr>V2For<wbr>Csv</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#exporttoword" class="tsd-kind-icon">export<wbr>ToWord</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#fullfillparams" class="tsd-kind-icon">fullfill<wbr>Params</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#getalltemplateurl" class="tsd-kind-icon">get<wbr>Alltemplate<wbr>Url</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#getdefaulttemplateurl" class="tsd-kind-icon">get<wbr>Default<wbr>Template<wbr>Url</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#getfiltergroup" class="tsd-kind-icon">get<wbr>Filter<wbr>Group</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="list.html#getlistquery" class="tsd-kind-icon">get<wbr>List<wbr>Query</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#getpagecount" class="tsd-kind-icon">get<wbr>Page<wbr>Count</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#getpagecountv2" class="tsd-kind-icon">get<wbr>Page<wbr>Count<wbr>V2</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#getrowdetail" class="tsd-kind-icon">get<wbr>Row<wbr>Detail</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#getworkflow" class="tsd-kind-icon">get<wbr>Workflow</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-protected">
								<a href="list.html#handlefilters" class="tsd-kind-icon">handle<wbr>Filters</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="list.html#ispageindex" class="tsd-kind-icon">is<wbr>Page<wbr>Index</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="list.html#ontransportmessage" class="tsd-kind-icon">on<wbr>Transport<wbr>Message</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#postparamsforexcel" class="tsd-kind-icon">post<wbr>Params<wbr>For<wbr>Excel</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#query" class="tsd-kind-icon">query</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#querymeta" class="tsd-kind-icon">query<wbr>Meta</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#registeronchange" class="tsd-kind-icon">register<wbr>OnChange</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#removefilters" class="tsd-kind-icon">remove<wbr>Filters</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#setcolumnsforpages" class="tsd-kind-icon">set<wbr>Columns<wbr>For<wbr>Pages</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="list.html#unanonymous" class="tsd-kind-icon">un<wbr>Anonymous</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#updateaction" class="tsd-kind-icon">update<wbr>Action</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="list.html#updatefilterparam" class="tsd-kind-icon">update<wbr>Filter<wbr>Param</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>