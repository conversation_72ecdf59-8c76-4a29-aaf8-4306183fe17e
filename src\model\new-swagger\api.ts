import axios from "../../core/axios/index"
import type * as dto from "../../def/index"

export default class NewSwaggerApi {
    /**
     * 返回所有领域列表
     * @return
     */
    public domainList() {
        return axios.get<dto.NewSwaggerTypes.domainItem[]>(
            `/general/swagger/domain/list`
        )
    }

    /**
     * 返回api网关列表
     *
     * @return
     */
    public gatewayList() {
        return axios.get<dto.NewSwaggerTypes.gatewayList>(
            `/general/swagger/gateway/list`
        )
    }

    /**
     * 返回某个网关下api列表
     *
     * @param gwName
     * @return
     */
    public getGateway(gwName: string) {
        return axios.get<dto.NewSwaggerTypes.gateway>(
            `/general/swagger/gateway/${gwName}/api/list`
        )
    }

    /**
     * 返回领域下的模型列表
     *
     * @param domainName
     * @return
     */
    public getDomainModelList(domainName: string) {
        return axios.get<dto.NewSwaggerTypes.DomainModelList>(
            `/general/swagger/domain/${domainName}/model/list`
        )
    }
    /**
     * 返回领域服务列表
     *
     * @param domaiName
     * @return
     */
    public getDomainServiceList(domainName: string) {
        return axios.get<dto.NewSwaggerTypes.DomainServicList>(
            `/general/swagger/domain/${domainName}/service/list`
        )
    }

    public getModel(domainName: string, modelName: string) {
        return axios.get<dto.NewSwaggerTypes.DomainModel>(
            `/general/swagger/domain/${domainName}/model/${modelName}/info`
        )
    }
}
