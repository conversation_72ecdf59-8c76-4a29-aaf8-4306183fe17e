<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>ListEasy | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="listeasy.html">ListEasy</a>
				</li>
			</ul>
			<h1>Class ListEasy&lt;RowType&gt;</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-type-parameters">
				<h3>Type parameters</h3>
				<ul class="tsd-type-parameters">
					<li>
						<h4>RowType<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListRow</span> = <span class="tsd-signature-type">dto.ListRow</span></h4>
					</li>
				</ul>
			</section>
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<a href="list.html" class="tsd-signature-type">List</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span>
						<ul class="tsd-hierarchy">
							<li>
								<span class="target">ListEasy</span>
							</li>
						</ul>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section tsd-is-inherited">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="listeasy.html#_getlist" class="tsd-kind-icon">_get<wbr>List</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-overwrite tsd-is-inherited tsd-is-protected"><a href="listeasy.html#api" class="tsd-kind-icon">api</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="listeasy.html#filters" class="tsd-kind-icon">filters</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#isanonymous" class="tsd-kind-icon">is<wbr>Anonymous</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="listeasy.html#item_size" class="tsd-kind-icon">item_<wbr>size</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="listeasy.html#prefilter" class="tsd-kind-icon">prefilter</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class"><a href="listeasy.html#rawfilters" class="tsd-kind-icon">raw<wbr>Filters</a></li>
							</ul>
						</section>
						<section class="tsd-index-section tsd-is-inherited tsd-is-private-protected">
							<h3>Accessors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-get-signature tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="listeasy.html#modelname" class="tsd-kind-icon">model<wbr>Name</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listeasy.html#addfilter" class="tsd-kind-icon">add<wbr>Filter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listeasy.html#addprefilter" class="tsd-kind-icon">add<wbr>Prefilter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#anonymous" class="tsd-kind-icon">anonymous</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listeasy.html#clearfilter" class="tsd-kind-icon">clear<wbr>Filter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listeasy.html#clearprefilter" class="tsd-kind-icon">clear<wbr>Prefilter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listeasy.html#cleartreefilters" class="tsd-kind-icon">clear<wbr>Tree<wbr>Filters</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#exporttoexcel" class="tsd-kind-icon">export<wbr>ToExcel</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#exporttoexcelv2" class="tsd-kind-icon">export<wbr>ToExcel<wbr>V2</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#exporttoexcelv2forcsv" class="tsd-kind-icon">export<wbr>ToExcel<wbr>V2For<wbr>Csv</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#exporttoword" class="tsd-kind-icon">export<wbr>ToWord</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#fullfillparams" class="tsd-kind-icon">fullfill<wbr>Params</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#getalltemplateurl" class="tsd-kind-icon">get<wbr>Alltemplate<wbr>Url</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#getdefaulttemplateurl" class="tsd-kind-icon">get<wbr>Default<wbr>Template<wbr>Url</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#getfiltergroup" class="tsd-kind-icon">get<wbr>Filter<wbr>Group</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listeasy.html#getform" class="tsd-kind-icon">get<wbr>Form</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="listeasy.html#getitemindexbypage" class="tsd-kind-icon">get<wbr>Item<wbr>Index<wbr>ByPage</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="listeasy.html#getlist" class="tsd-kind-icon">get<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#getpagecount" class="tsd-kind-icon">get<wbr>Page<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#getpagecountv2" class="tsd-kind-icon">get<wbr>Page<wbr>Count<wbr>V2</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#getrowdetail" class="tsd-kind-icon">get<wbr>Row<wbr>Detail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#getworkflow" class="tsd-kind-icon">get<wbr>Workflow</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="listeasy.html#handlefilters" class="tsd-kind-icon">handle<wbr>Filters</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#postparamsforexcel" class="tsd-kind-icon">post<wbr>Params<wbr>For<wbr>Excel</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><a href="listeasy.html#query" class="tsd-kind-icon">query</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#querymeta" class="tsd-kind-icon">query<wbr>Meta</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listeasy.html#querytab" class="tsd-kind-icon">query<wbr>Tab</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listeasy.html#querytab2" class="tsd-kind-icon">query<wbr>Tab2</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#registeronchange" class="tsd-kind-icon">register<wbr>OnChange</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#removefilters" class="tsd-kind-icon">remove<wbr>Filters</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listeasy.html#search" class="tsd-kind-icon">search</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#setcolumnsforpages" class="tsd-kind-icon">set<wbr>Columns<wbr>For<wbr>Pages</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#unanonymous" class="tsd-kind-icon">un<wbr>Anonymous</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#updateaction" class="tsd-kind-icon">update<wbr>Action</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="listeasy.html#updatefilterparam" class="tsd-kind-icon">update<wbr>Filter<wbr>Param</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group tsd-is-inherited">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">new <wbr>List<wbr>Easy<span class="tsd-signature-symbol">(</span>props<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.constructor</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="listeasy.html" class="tsd-signature-type">ListEasy</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#constructor">constructor</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:27</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>props: <span class="tsd-signature-type">dto.ListTypes.constructor</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="listeasy.html" class="tsd-signature-type">ListEasy</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="_getlist" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _get<wbr>List</h3>
					<div class="tsd-signature tsd-kind-icon">_get<wbr>List<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.ListTypes.getSingleTabPageListFuncType</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">dto.ListTypes.getSpecificTabListFuncType</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list-easy.ts:81</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>保存要被override的父类getList</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-overwrite tsd-is-inherited tsd-is-protected">
					<a name="api" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagProtected">Protected</span> api</h3>
					<div class="tsd-signature tsd-kind-icon">api<span class="tsd-signature-symbol">:</span> <a href="listapi.html" class="tsd-signature-type">listApi</a></div>
					<aside class="tsd-sources">
						<p>Inherited from <a href="list.html">List</a>.<a href="list.html#api">api</a></p>
						<p>Overrides <a href="anonymousmodel.html">AnonymousModel</a>.<a href="anonymousmodel.html#api">api</a></p>
						<ul>
							<li>Defined in src/model/list/list.ts:20</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="filters" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> filters</h3>
					<div class="tsd-signature tsd-kind-icon">filters<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.ListTypes.KeyValueFilters</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list-easy.ts:18</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited">
					<a name="isanonymous" class="tsd-anchor"></a>
					<h3>is<wbr>Anonymous</h3>
					<div class="tsd-signature tsd-kind-icon">is<wbr>Anonymous<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
						<p>Inherited from <a href="anonymousmodel.html">AnonymousModel</a>.<a href="anonymousmodel.html#isanonymous">isAnonymous</a></p>
						<ul>
							<li>Defined in src/core/anonymous-api.ts:18</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="item_size" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> item_<wbr>size</h3>
					<div class="tsd-signature tsd-kind-icon">item_<wbr>size<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list-easy.ts:87</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>保存item_size以便计算item_index</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="prefilter" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> prefilter</h3>
					<div class="tsd-signature tsd-kind-icon">prefilter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list-easy.ts:20</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class">
					<a name="rawfilters" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> raw<wbr>Filters</h3>
					<div class="tsd-signature tsd-kind-icon">raw<wbr>Filters<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.metaFilter</span><span class="tsd-signature-symbol">[]</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list-easy.ts:19</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group tsd-is-inherited tsd-is-private-protected">
				<h2>Accessors</h2>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class tsd-is-inherited tsd-is-protected">
					<a name="modelname" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagProtected">Protected</span> model<wbr>Name</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class tsd-is-inherited tsd-is-protected">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> modelName<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#modelname">modelName</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:34</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addfilter" class="tsd-anchor"></a>
					<h3>add<wbr>Filter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Filter<span class="tsd-signature-symbol">(</span>filter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.KeyValueFilters</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-easy.ts:48</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加filter</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>filter: <span class="tsd-signature-type">dto.ListTypes.KeyValueFilters</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
							<p>返回当前实例，供链式调用</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addprefilter" class="tsd-anchor"></a>
					<h3>add<wbr>Prefilter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Prefilter<span class="tsd-signature-symbol">(</span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.PrefiltersObject</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-easy.ts:27</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加filter</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>prefilters: <span class="tsd-signature-type">dto.ListTypes.PrefiltersObject</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
							<p>返回当前实例，供链式调用</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="anonymous" class="tsd-anchor"></a>
					<h3>anonymous</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">anonymous<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="anonymousmodel.html">AnonymousModel</a>.<a href="anonymousmodel.html#anonymous">anonymous</a></p>
								<ul>
									<li>Defined in src/core/anonymous-api.ts:19</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearfilter" class="tsd-anchor"></a>
					<h3>clear<wbr>Filter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Filter<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-easy.ts:64</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>清空filter</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
							<p>返回当前实例，供链式调用</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearprefilter" class="tsd-anchor"></a>
					<h3>clear<wbr>Prefilter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Prefilter<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-easy.ts:38</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>清空filter</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
							<p>返回当前实例，供链式调用</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="cleartreefilters" class="tsd-anchor"></a>
					<h3>clear<wbr>Tree<wbr>Filters</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Tree<wbr>Filters<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-easy.ts:15</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="exporttoexcel" class="tsd-anchor"></a>
					<h3>export<wbr>ToExcel</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">export<wbr>ToExcel<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ListTypes.exportExcelParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">(Anonymous function)</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#exporttoexcel">exportToExcel</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:265</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>导出列表为excel</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagDefault value">Default value</span> params: <span class="tsd-signature-type">dto.ListTypes.exportExcelParams</span><span class="tsd-signature-symbol"> = {}</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">(Anonymous function)</span></h4>
							<p>返回一个开始导出的方法startDownload, 新方法有一个参数用来监听导出进度
							返回的方法是一个Promise，resolve时得到exlce下载链接</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="exporttoexcelv2" class="tsd-anchor"></a>
					<h3>export<wbr>ToExcel<wbr>V2</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">export<wbr>ToExcel<wbr>V2<span class="tsd-signature-symbol">(</span>eid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, headers<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>CurrentOrg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">(Anonymous function)</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#exporttoexcelv2">exportToExcelV2</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:290</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>exportToExcelV2</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eid: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> headers: <span class="tsd-signature-symbol">{ </span>CurrentOrg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> Current<wbr>Org<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">(Anonymous function)</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="exporttoexcelv2forcsv" class="tsd-anchor"></a>
					<h3>export<wbr>ToExcel<wbr>V2For<wbr>Csv</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">export<wbr>ToExcel<wbr>V2For<wbr>Csv<span class="tsd-signature-symbol">(</span>eid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, headers<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>CurrentOrg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">(Anonymous function)</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#exporttoexcelv2forcsv">exportToExcelV2ForCsv</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:307</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>导出为csv</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eid: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> headers: <span class="tsd-signature-symbol">{ </span>CurrentOrg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> Current<wbr>Org<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">(Anonymous function)</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="exporttoword" class="tsd-anchor"></a>
					<h3>export<wbr>ToWord</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">export<wbr>ToWord<span class="tsd-signature-symbol">(</span>eid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, headers<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>CurrentOrg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">(Anonymous function)</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#exporttoword">exportToWord</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:297</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eid: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> headers: <span class="tsd-signature-symbol">{ </span>CurrentOrg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> Current<wbr>Org<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> Entrance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">(Anonymous function)</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="fullfillparams" class="tsd-anchor"></a>
					<h3>fullfill<wbr>Params</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">fullfill<wbr>Params<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.queryProps</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../modules/listtypes.html#fullfilledqueryprops" class="tsd-signature-type">fullfilledQueryProps</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#fullfillparams">fullfillParams</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:67</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.queryProps</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="../modules/listtypes.html#fullfilledqueryprops" class="tsd-signature-type">fullfilledQueryProps</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="getalltemplateurl" class="tsd-anchor"></a>
					<h3>get<wbr>Alltemplate<wbr>Url</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Alltemplate<wbr>Url<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#getalltemplateurl">getAlltemplateUrl</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:357</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>得到所有模版</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>Promise<url></p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="getdefaulttemplateurl" class="tsd-anchor"></a>
					<h3>get<wbr>Default<wbr>Template<wbr>Url</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Default<wbr>Template<wbr>Url<span class="tsd-signature-symbol">(</span>tabName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#getdefaulttemplateurl">getDefaultTemplateUrl</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:350</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>得到某个标签页的模版</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagDefault value">Default value</span> tabName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &quot;&quot;</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>Promise<url></p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="getfiltergroup" class="tsd-anchor"></a>
					<h3>get<wbr>Filter<wbr>Group</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Filter<wbr>Group<span class="tsd-signature-symbol">(</span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#filtergroup" class="tsd-signature-type">filterGroup</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#getfiltergroup">getFilterGroup</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:241</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>property: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>属性名</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#filtergroup" class="tsd-signature-type">filterGroup</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getform" class="tsd-anchor"></a>
					<h3>get<wbr>Form</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Form<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="listform.html" class="tsd-signature-type">ListForm</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-easy.ts:125</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <a href="listform.html" class="tsd-signature-type">ListForm</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="getitemindexbypage" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> get<wbr>Item<wbr>Index<wbr>ByPage</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Item<wbr>Index<wbr>ByPage<span class="tsd-signature-symbol">(</span>pageIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-easy.ts:69</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>pageIndex: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="getlist" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> get<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">get<wbr>List<span class="tsd-signature-symbol">(</span>tabName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, pageIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.ListTypes.getListDataByTabRequestResult</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span><span class="tsd-signature-symbol">&gt;</span></li>
						<li class="tsd-signature tsd-kind-icon">get<wbr>List<span class="tsd-signature-symbol">(</span>pageIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.ListTypes.getListDataOfSinglePageRequestResult</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-easy.ts:89</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>tabName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>pageIndex: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.ListTypes.getListDataByTabRequestResult</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">null</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-easy.ts:93</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>pageIndex: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.ListTypes.getListDataOfSinglePageRequestResult</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="getpagecount" class="tsd-anchor"></a>
					<h3>get<wbr>Page<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Page<wbr>Count<span class="tsd-signature-symbol">(</span>listQueryParams<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ListTypes.queryProps</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>page_datas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#getpagecount">getPageCount</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:189</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> listQueryParams: <span class="tsd-signature-type">dto.ListTypes.queryProps</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>page_datas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="getpagecountv2" class="tsd-anchor"></a>
					<h3>get<wbr>Page<wbr>Count<wbr>V2</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Page<wbr>Count<wbr>V2<span class="tsd-signature-symbol">(</span>listQueryParams<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ListTypes.queryPropsEasy</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>page_datas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#getpagecountv2">getPageCountV2</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:198</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> listQueryParams: <span class="tsd-signature-type">dto.ListTypes.queryPropsEasy</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>page_datas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="getrowdetail" class="tsd-anchor"></a>
					<h3>get<wbr>Row<wbr>Detail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Row<wbr>Detail<span class="tsd-signature-symbol">(</span>keyFieldValue<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#getrowdetail">getRowDetail</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:255</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取行详情
									keyFieldValue: row的keyfield的值</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>keyFieldValue: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="getworkflow" class="tsd-anchor"></a>
					<h3>get<wbr>Workflow</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Workflow<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.getWorkflow</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="workflow.html" class="tsd-signature-type">Workflow</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#getworkflow">getWorkflow</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:41</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取工作流类</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.getWorkflow</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="workflow.html" class="tsd-signature-type">Workflow</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected">
					<a name="handlefilters" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagProtected">Protected</span> handle<wbr>Filters</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected">
						<li class="tsd-signature tsd-kind-icon">handle<wbr>Filters<span class="tsd-signature-symbol">(</span>keyValueFilter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.KeyValueFilters</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../globals.html#filter" class="tsd-signature-type">filter</a><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#handlefilters">handleFilters</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:365</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>传入简单的键值对数组，放回sdk所需的filters参数</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>keyValueFilter: <span class="tsd-signature-type">dto.ListTypes.KeyValueFilters</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="../globals.html#filter" class="tsd-signature-type">filter</a><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="postparamsforexcel" class="tsd-anchor"></a>
					<h3>post<wbr>Params<wbr>For<wbr>Excel</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">post<wbr>Params<wbr>For<wbr>Excel<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ListTypes.exportExcelParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#postparamsforexcel">postParamsForExcel</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:320</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>postParamsForExcel</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagDefault value">Default value</span> params: <span class="tsd-signature-type">dto.ListTypes.exportExcelParams</span><span class="tsd-signature-symbol"> = {}</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-overwrite">
					<a name="query" class="tsd-anchor"></a>
					<h3>query</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-overwrite">
						<li class="tsd-signature tsd-kind-icon">query<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.queryPropsEasy</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>getList<span class="tsd-signature-symbol">: </span><a href="listeasy.html#getlist" class="tsd-signature-type">getList</a><span class="tsd-signature-symbol">; </span>pageData<span class="tsd-signature-symbol">: </span><a href="../modules/listtypes.html#getlistdatarequestresult" class="tsd-signature-type">getListDataRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Overrides <a href="list.html">List</a>.<a href="list.html#query">query</a></p>
								<ul>
									<li>Defined in src/model/list/list-easy.ts:135</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.queryPropsEasy</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>getList<span class="tsd-signature-symbol">: </span><a href="listeasy.html#getlist" class="tsd-signature-type">getList</a><span class="tsd-signature-symbol">; </span>pageData<span class="tsd-signature-symbol">: </span><a href="../modules/listtypes.html#getlistdatarequestresult" class="tsd-signature-type">getListDataRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="querymeta" class="tsd-anchor"></a>
					<h3>query<wbr>Meta</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">query<wbr>Meta<span class="tsd-signature-symbol">(</span>listQueryParams<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.queryProps</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#iqueryresult" class="tsd-signature-type">IQueryResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#querymeta">queryMeta</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:153</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>listQueryParams: <span class="tsd-signature-type">dto.ListTypes.queryProps</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#iqueryresult" class="tsd-signature-type">IQueryResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="querytab" class="tsd-anchor"></a>
					<h3>query<wbr>Tab</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<wbr>Tab<span class="tsd-signature-symbol">(</span>tab<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, filters<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ListTypes.KeyValueFilters</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdatabytabrequestresult" class="tsd-signature-type">getListDataByTabRequestResult</a><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#listrow" class="tsd-signature-type">ListRow</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-easy.ts:156</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>指定查询tab</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>tab: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>index: <span class="tsd-signature-type">number</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>索引，从1开始</p>
									</div>
								</li>
								<li>
									<h5>size: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> filters: <span class="tsd-signature-type">dto.ListTypes.KeyValueFilters</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdatabytabrequestresult" class="tsd-signature-type">getListDataByTabRequestResult</a><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#listrow" class="tsd-signature-type">ListRow</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="querytab2" class="tsd-anchor"></a>
					<h3>query<wbr>Tab2</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<wbr>Tab2<span class="tsd-signature-symbol">(</span>tab<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">dto.ListTypes.queryPropsEasy</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdatabytabrequestresult" class="tsd-signature-type">getListDataByTabRequestResult</a><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#listrow" class="tsd-signature-type">ListRow</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-easy.ts:180</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>指定查询tab</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>tab: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>索引，从1开始</p>
									</div>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> params: <span class="tsd-signature-type">dto.ListTypes.queryPropsEasy</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdatabytabrequestresult" class="tsd-signature-type">getListDataByTabRequestResult</a><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#listrow" class="tsd-signature-type">ListRow</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="registeronchange" class="tsd-anchor"></a>
					<h3>register<wbr>OnChange</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">register<wbr>OnChange<span class="tsd-signature-symbol">(</span>cb<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.callBackOfModelUpdated</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#registeronchange">registerOnChange</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:136</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>注册当前模型的数据变化时的回掉</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>cb: <span class="tsd-signature-type">dto.SSE.callBackOfModelUpdated</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="removefilters" class="tsd-anchor"></a>
					<h3>remove<wbr>Filters</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">remove<wbr>Filters<span class="tsd-signature-symbol">(</span>items<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.filter</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#removefilters">removeFilters</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:376</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>items: <span class="tsd-signature-type">dto.filter</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="search" class="tsd-anchor"></a>
					<h3>search</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">search<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.queryPropsEasy</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdataofsinglepagerequestresult" class="tsd-signature-type">getListDataOfSinglePageRequestResult</a><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#listrow" class="tsd-signature-type">ListRow</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-easy.ts:196</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>此方法用来取代query方法，此方法效能更高</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.queryPropsEasy</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdataofsinglepagerequestresult" class="tsd-signature-type">getListDataOfSinglePageRequestResult</a><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#listrow" class="tsd-signature-type">ListRow</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="setcolumnsforpages" class="tsd-anchor"></a>
					<h3>set<wbr>Columns<wbr>For<wbr>Pages</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Columns<wbr>For<wbr>Pages<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.setColumnsForPagesParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#setcolumnsforpages">setColumnsForPages</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:245</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.setColumnsForPagesParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="unanonymous" class="tsd-anchor"></a>
					<h3>un<wbr>Anonymous</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">un<wbr>Anonymous<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="anonymousmodel.html">AnonymousModel</a>.<a href="anonymousmodel.html#unanonymous">unAnonymous</a></p>
								<ul>
									<li>Defined in src/core/anonymous-api.ts:23</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="updateaction" class="tsd-anchor"></a>
					<h3>update<wbr>Action</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Action<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.updateAction</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#updateactionrequestresult" class="tsd-signature-type">updateActionRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#updateaction">updateAction</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:120</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>更新action</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.updateAction</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#updateactionrequestresult" class="tsd-signature-type">updateActionRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="updatefilterparam" class="tsd-anchor"></a>
					<h3>update<wbr>Filter<wbr>Param</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Filter<wbr>Param<span class="tsd-signature-symbol">(</span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, filters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.filter</span><span class="tsd-signature-symbol">[]</span>, item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#updatefilterparamrequestresult" class="tsd-signature-type">updateFilterParamRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="list.html">List</a>.<a href="list.html#updatefilterparam">updateFilterParam</a></p>
								<ul>
									<li>Defined in src/model/list/list.ts:221</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>如果查询条件中有一项可以跟随另一项自动填写，则调用本方法</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>property: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>filters: <span class="tsd-signature-type">dto.filter</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
								<li>
									<h5>item_index: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>item_size: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#updatefilterparamrequestresult" class="tsd-signature-type">updateFilterParamRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class tsd-has-type-parameter">
						<a href="listeasy.html" class="tsd-kind-icon">List<wbr>Easy</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="listeasy.html#_getlist" class="tsd-kind-icon">_get<wbr>List</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-overwrite tsd-is-inherited tsd-is-protected">
								<a href="listeasy.html#api" class="tsd-kind-icon">api</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="listeasy.html#filters" class="tsd-kind-icon">filters</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#isanonymous" class="tsd-kind-icon">is<wbr>Anonymous</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="listeasy.html#item_size" class="tsd-kind-icon">item_<wbr>size</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="listeasy.html#prefilter" class="tsd-kind-icon">prefilter</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class">
								<a href="listeasy.html#rawfilters" class="tsd-kind-icon">raw<wbr>Filters</a>
							</li>
							<li class=" tsd-kind-get-signature tsd-parent-kind-class tsd-is-inherited tsd-is-protected">
								<a href="listeasy.html#modelname" class="tsd-kind-icon">model<wbr>Name</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listeasy.html#addfilter" class="tsd-kind-icon">add<wbr>Filter</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listeasy.html#addprefilter" class="tsd-kind-icon">add<wbr>Prefilter</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#anonymous" class="tsd-kind-icon">anonymous</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listeasy.html#clearfilter" class="tsd-kind-icon">clear<wbr>Filter</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listeasy.html#clearprefilter" class="tsd-kind-icon">clear<wbr>Prefilter</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listeasy.html#cleartreefilters" class="tsd-kind-icon">clear<wbr>Tree<wbr>Filters</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#exporttoexcel" class="tsd-kind-icon">export<wbr>ToExcel</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#exporttoexcelv2" class="tsd-kind-icon">export<wbr>ToExcel<wbr>V2</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#exporttoexcelv2forcsv" class="tsd-kind-icon">export<wbr>ToExcel<wbr>V2For<wbr>Csv</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#exporttoword" class="tsd-kind-icon">export<wbr>ToWord</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#fullfillparams" class="tsd-kind-icon">fullfill<wbr>Params</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#getalltemplateurl" class="tsd-kind-icon">get<wbr>Alltemplate<wbr>Url</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#getdefaulttemplateurl" class="tsd-kind-icon">get<wbr>Default<wbr>Template<wbr>Url</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#getfiltergroup" class="tsd-kind-icon">get<wbr>Filter<wbr>Group</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listeasy.html#getform" class="tsd-kind-icon">get<wbr>Form</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="listeasy.html#getitemindexbypage" class="tsd-kind-icon">get<wbr>Item<wbr>Index<wbr>ByPage</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="listeasy.html#getlist" class="tsd-kind-icon">get<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#getpagecount" class="tsd-kind-icon">get<wbr>Page<wbr>Count</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#getpagecountv2" class="tsd-kind-icon">get<wbr>Page<wbr>Count<wbr>V2</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#getrowdetail" class="tsd-kind-icon">get<wbr>Row<wbr>Detail</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#getworkflow" class="tsd-kind-icon">get<wbr>Workflow</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected">
								<a href="listeasy.html#handlefilters" class="tsd-kind-icon">handle<wbr>Filters</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#postparamsforexcel" class="tsd-kind-icon">post<wbr>Params<wbr>For<wbr>Excel</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-overwrite">
								<a href="listeasy.html#query" class="tsd-kind-icon">query</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#querymeta" class="tsd-kind-icon">query<wbr>Meta</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listeasy.html#querytab" class="tsd-kind-icon">query<wbr>Tab</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listeasy.html#querytab2" class="tsd-kind-icon">query<wbr>Tab2</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#registeronchange" class="tsd-kind-icon">register<wbr>OnChange</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#removefilters" class="tsd-kind-icon">remove<wbr>Filters</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listeasy.html#search" class="tsd-kind-icon">search</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#setcolumnsforpages" class="tsd-kind-icon">set<wbr>Columns<wbr>For<wbr>Pages</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#unanonymous" class="tsd-kind-icon">un<wbr>Anonymous</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#updateaction" class="tsd-kind-icon">update<wbr>Action</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="listeasy.html#updatefilterparam" class="tsd-kind-icon">update<wbr>Filter<wbr>Param</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>