<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>UserAgentType | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="useragenttype.html">UserAgentType</a>
				</li>
			</ul>
			<h1>Enumeration UserAgentType</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Enumeration members</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="useragenttype.html#andriod" class="tsd-kind-icon">Andriod</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="useragenttype.html#desktop" class="tsd-kind-icon">Desktop</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="useragenttype.html#desktopchrome" class="tsd-kind-icon">Desktop<wbr>Chrome</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="useragenttype.html#desktopedge" class="tsd-kind-icon">Desktop<wbr>Edge</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="useragenttype.html#desktopfirefox" class="tsd-kind-icon">Desktop<wbr>Firefox</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="useragenttype.html#desktopie" class="tsd-kind-icon">DesktopIE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="useragenttype.html#desktopothers" class="tsd-kind-icon">Desktop<wbr>Others</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="useragenttype.html#desktopsafari" class="tsd-kind-icon">Desktop<wbr>Safari</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="useragenttype.html#ipad" class="tsd-kind-icon">IPad</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="useragenttype.html#iphone" class="tsd-kind-icon">IPhone</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="useragenttype.html#mobile" class="tsd-kind-icon">Mobile</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="useragenttype.html#mobilephone" class="tsd-kind-icon">Mobile<wbr>Phone</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="useragenttype.html#none" class="tsd-kind-icon">None</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="useragenttype.html#tablet" class="tsd-kind-icon">Tablet</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Enumeration members</h2>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="andriod" class="tsd-anchor"></a>
					<h3>Andriod</h3>
					<div class="tsd-signature tsd-kind-icon">Andriod<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1 &lt;&lt; 10</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/helpers/ua.ts:29</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>安卓手机设备</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="desktop" class="tsd-anchor"></a>
					<h3>Desktop</h3>
					<div class="tsd-signature tsd-kind-icon">Desktop<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = DesktopOthers |DesktopSafari |DesktopChrome |DesktopFirefox |DesktopIE |DesktopEdge</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/helpers/ua.ts:17</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="desktopchrome" class="tsd-anchor"></a>
					<h3>Desktop<wbr>Chrome</h3>
					<div class="tsd-signature tsd-kind-icon">Desktop<wbr>Chrome<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1 &lt;&lt; 4</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/helpers/ua.ts:12</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="desktopedge" class="tsd-anchor"></a>
					<h3>Desktop<wbr>Edge</h3>
					<div class="tsd-signature tsd-kind-icon">Desktop<wbr>Edge<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1 &lt;&lt; 7</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/helpers/ua.ts:15</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="desktopfirefox" class="tsd-anchor"></a>
					<h3>Desktop<wbr>Firefox</h3>
					<div class="tsd-signature tsd-kind-icon">Desktop<wbr>Firefox<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1 &lt;&lt; 5</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/helpers/ua.ts:13</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="desktopie" class="tsd-anchor"></a>
					<h3>DesktopIE</h3>
					<div class="tsd-signature tsd-kind-icon">DesktopIE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1 &lt;&lt; 6</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/helpers/ua.ts:14</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="desktopothers" class="tsd-anchor"></a>
					<h3>Desktop<wbr>Others</h3>
					<div class="tsd-signature tsd-kind-icon">Desktop<wbr>Others<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1 &lt;&lt; 2</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/helpers/ua.ts:10</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="desktopsafari" class="tsd-anchor"></a>
					<h3>Desktop<wbr>Safari</h3>
					<div class="tsd-signature tsd-kind-icon">Desktop<wbr>Safari<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1 &lt;&lt; 3</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/helpers/ua.ts:11</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ipad" class="tsd-anchor"></a>
					<h3>IPad</h3>
					<div class="tsd-signature tsd-kind-icon">IPad<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1 &lt;&lt; 9</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/helpers/ua.ts:25</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="iphone" class="tsd-anchor"></a>
					<h3>IPhone</h3>
					<div class="tsd-signature tsd-kind-icon">IPhone<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1 &lt;&lt; 8</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/helpers/ua.ts:24</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="mobile" class="tsd-anchor"></a>
					<h3>Mobile</h3>
					<div class="tsd-signature tsd-kind-icon">Mobile<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = MobilePhone | Tablet | IPhone</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/helpers/ua.ts:39</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>所有移动设备，包含手机+平板</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="mobilephone" class="tsd-anchor"></a>
					<h3>Mobile<wbr>Phone</h3>
					<div class="tsd-signature tsd-kind-icon">Mobile<wbr>Phone<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = IPhone | Andriod</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/helpers/ua.ts:34</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>移动设备（不含包平板设备）</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="none" class="tsd-anchor"></a>
					<h3>None</h3>
					<div class="tsd-signature tsd-kind-icon">None<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 0</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/helpers/ua.ts:4</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="tablet" class="tsd-anchor"></a>
					<h3>Tablet</h3>
					<div class="tsd-signature tsd-kind-icon">Tablet<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1 &lt;&lt; 1</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/helpers/ua.ts:8</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>安卓平板设备</p>
						</div>
					</div>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-enum">
						<a href="useragenttype.html" class="tsd-kind-icon">User<wbr>Agent<wbr>Type</a>
						<ul>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="useragenttype.html#andriod" class="tsd-kind-icon">Andriod</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="useragenttype.html#desktop" class="tsd-kind-icon">Desktop</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="useragenttype.html#desktopchrome" class="tsd-kind-icon">Desktop<wbr>Chrome</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="useragenttype.html#desktopedge" class="tsd-kind-icon">Desktop<wbr>Edge</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="useragenttype.html#desktopfirefox" class="tsd-kind-icon">Desktop<wbr>Firefox</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="useragenttype.html#desktopie" class="tsd-kind-icon">DesktopIE</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="useragenttype.html#desktopothers" class="tsd-kind-icon">Desktop<wbr>Others</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="useragenttype.html#desktopsafari" class="tsd-kind-icon">Desktop<wbr>Safari</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="useragenttype.html#ipad" class="tsd-kind-icon">IPad</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="useragenttype.html#iphone" class="tsd-kind-icon">IPhone</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="useragenttype.html#mobile" class="tsd-kind-icon">Mobile</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="useragenttype.html#mobilephone" class="tsd-kind-icon">Mobile<wbr>Phone</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="useragenttype.html#none" class="tsd-kind-icon">None</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="useragenttype.html#tablet" class="tsd-kind-icon">Tablet</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>