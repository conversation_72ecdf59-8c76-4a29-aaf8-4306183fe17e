import axios from "../../core/axios/index"
import { global } from "../../core/global"
import { encodeParams4Parameters } from "../../core/tools/form"
import type * as dto from "../../def/index"

export default class ETLAPI {
    constructor(private id: dto.ETLTypes.id) {}

    public query() {
        return axios.get<unknown>(`general/etlobject/${this.id}/property`)
    }

    public getData(params: unknown) {
        return axios.get<dto.ETLTypes.getDataAPIResult>(
            `general/etlobject/${this.id}/source?${encodeParams4Parameters(
                params
            )}`
        )
    }

    public createTask(taskDate: string, params: unknown) {
        return axios.get<unknown>(
            `eneral/etlobject/${
                this.id
            }/${taskDate}/createTask?${encodeParams4Parameters(params)}`
        )
    }

    public createExecuteURL(params: unknown) {
        return `${global.baseUrl}general/etlobject/sse/${
            this.id
        }/execute?${encodeParams4Parameters(params)}`
    }
}
