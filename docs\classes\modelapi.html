<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>ModelApi | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="modelapi.html">ModelApi</a>
				</li>
			</ul>
			<h1>Class ModelApi</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">ModelApi</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="modelapi.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section tsd-is-private tsd-is-private-protected">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="modelapi.html#model_name" class="tsd-kind-icon">model_<wbr>name</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="modelapi.html#createchat" class="tsd-kind-icon">create<wbr>Chat</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="modelapi.html#getforwardurl" class="tsd-kind-icon">get<wbr>ForwardURL</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="modelapi.html#gethistoryversion" class="tsd-kind-icon">get<wbr>History<wbr>Version</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="modelapi.html#getmodelrequest" class="tsd-kind-icon">get<wbr>Model<wbr>Request</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="modelapi.html#getremarklist" class="tsd-kind-icon">get<wbr>Remark<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="modelapi.html#getsection" class="tsd-kind-icon">get<wbr>Section</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="modelapi.html#gettimebyidentifier" class="tsd-kind-icon">get<wbr>Time<wbr>ByIdentifier</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="modelapi.html#groupsearch" class="tsd-kind-icon">group<wbr>Search</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="modelapi.html#intentsearch" class="tsd-kind-icon">intent<wbr>Search</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="modelapi.html#jointsearch" class="tsd-kind-icon">joint<wbr>Search</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="modelapi.html#mappingfetch" class="tsd-kind-icon">mapping<wbr>Fetch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="modelapi.html#postmodelrequest" class="tsd-kind-icon">post<wbr>Model<wbr>Request</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="modelapi.html#supercascaderfetch" class="tsd-kind-icon">super<wbr>Cascader<wbr>Fetch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="modelapi.html#supercascadersearch" class="tsd-kind-icon">super<wbr>Cascader<wbr>Search</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="modelapi.html#toolbarenhancervalid" class="tsd-kind-icon">toolbar<wbr>Enhancer<wbr>Valid</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new <wbr>Model<wbr>Api<span class="tsd-signature-symbol">(</span>model_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="modelapi.html" class="tsd-signature-type">ModelApi</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/api.ts:7</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>model_name: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="modelapi.html" class="tsd-signature-type">ModelApi</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="model_name" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> model_<wbr>name</h3>
					<div class="tsd-signature tsd-kind-icon">model_<wbr>name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/model/api.ts:8</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="createchat" class="tsd-anchor"></a>
					<h3>create<wbr>Chat</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">create<wbr>Chat<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>autoJoin<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>detailId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/api.ts:109</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-symbol">{ </span>autoJoin<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>detailId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> auto<wbr>Join<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5>detail<wbr>Id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getforwardurl" class="tsd-anchor"></a>
					<h3>get<wbr>ForwardURL</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>ForwardURL<span class="tsd-signature-symbol">(</span>forwards<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#getforwardurlapiresult" class="tsd-signature-type">getForwardURLApiResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/api.ts:65</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>forwards: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> params: <span class="tsd-signature-type">unknown</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#getforwardurlapiresult" class="tsd-signature-type">getForwardURLApiResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="gethistoryversion" class="tsd-anchor"></a>
					<h3>get<wbr>History<wbr>Version</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>History<wbr>Version<span class="tsd-signature-symbol">(</span>objectId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span>, field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#historyversion" class="tsd-signature-type">HistoryVersion</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/api.ts:117</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>objectId: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>field: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#historyversion" class="tsd-signature-type">HistoryVersion</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="getmodelrequest" class="tsd-anchor"></a>
					<h3>get<wbr>Model<wbr>Request</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Model<wbr>Request&lt;T&gt;<span class="tsd-signature-symbol">(</span>apiName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, query<span class="tsd-signature-symbol">: </span><a href="../globals.html#queryobject" class="tsd-signature-type">QueryObject</a><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/api.ts:72</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>apiName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>query: <a href="../globals.html#queryobject" class="tsd-signature-type">QueryObject</a><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getremarklist" class="tsd-anchor"></a>
					<h3>get<wbr>Remark<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Remark<wbr>List<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>associateId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>pageIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>pageSize<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>pageIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>pageSize<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>rows<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/api.ts:92</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-symbol">{ </span>associateId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>pageIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>pageSize<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5>associate<wbr>Id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5>page<wbr>Index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5>page<wbr>Size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>pageIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>pageSize<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>rows<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getsection" class="tsd-anchor"></a>
					<h3>get<wbr>Section</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Section<span class="tsd-signature-symbol">(</span>sectionName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/detailtypes.html#section" class="tsd-signature-type">section</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/api.ts:158</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>sectionName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/detailtypes.html#section" class="tsd-signature-type">section</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="gettimebyidentifier" class="tsd-anchor"></a>
					<h3>get<wbr>Time<wbr>ByIdentifier</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Time<wbr>ByIdentifier<span class="tsd-signature-symbol">(</span>identifier<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/api.ts:86</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>identifier: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="groupsearch" class="tsd-anchor"></a>
					<h3>group<wbr>Search</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">group<wbr>Search<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ModelTypes.groupSearchApiParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#groupsearchapiresult" class="tsd-signature-type">groupSearchApiResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/api.ts:57</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ModelTypes.groupSearchApiParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#groupsearchapiresult" class="tsd-signature-type">groupSearchApiResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="intentsearch" class="tsd-anchor"></a>
					<h3>intent<wbr>Search</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">intent<wbr>Search<span class="tsd-signature-symbol">(</span>intentName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>form_params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#rowintent" class="tsd-signature-type">RowIntent</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/api.ts:47</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>intentName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>params: <span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>form_params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5>action<wbr>Id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5>form_<wbr>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#rowintent" class="tsd-signature-type">RowIntent</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="jointsearch" class="tsd-anchor"></a>
					<h3>joint<wbr>Search</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">joint<wbr>Search<span class="tsd-signature-symbol">(</span>joint_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ModelTypes.searchJointApiParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#searchjointapiresult" class="tsd-signature-type">searchJointApiResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/api.ts:37</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>joint_name: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ModelTypes.searchJointApiParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#searchjointapiresult" class="tsd-signature-type">searchJointApiResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="mappingfetch" class="tsd-anchor"></a>
					<h3>mapping<wbr>Fetch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">mapping<wbr>Fetch<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ModelTypes.mappingFetchAPIParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/api.ts:21</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ModelTypes.mappingFetchAPIParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="postmodelrequest" class="tsd-anchor"></a>
					<h3>post<wbr>Model<wbr>Request</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">post<wbr>Model<wbr>Request&lt;T&gt;<span class="tsd-signature-symbol">(</span>apiName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">FormData</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/api.ts:79</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>apiName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>params: <span class="tsd-signature-type">FormData</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="supercascaderfetch" class="tsd-anchor"></a>
					<h3>super<wbr>Cascader<wbr>Fetch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">super<wbr>Cascader<wbr>Fetch<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ModelTypes.SuperCascaderFetchApiParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#supercascaderfetchapiresult" class="tsd-signature-type">SuperCascaderFetchApiResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/api.ts:125</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ModelTypes.SuperCascaderFetchApiParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#supercascaderfetchapiresult" class="tsd-signature-type">SuperCascaderFetchApiResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="supercascadersearch" class="tsd-anchor"></a>
					<h3>super<wbr>Cascader<wbr>Search</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">super<wbr>Cascader<wbr>Search<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ModelTypes.SuperCascaderSearchApiParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#supercascadersearchapiresult" class="tsd-signature-type">SuperCascaderSearchApiResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/api.ts:142</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ModelTypes.SuperCascaderSearchApiParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/modeltypes.html#supercascadersearchapiresult" class="tsd-signature-type">SuperCascaderSearchApiResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="toolbarenhancervalid" class="tsd-anchor"></a>
					<h3>toolbar<wbr>Enhancer<wbr>Valid</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">toolbar<wbr>Enhancer<wbr>Valid&lt;T&gt;<span class="tsd-signature-symbol">(</span>behavior<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, jsonStr<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/model/api.ts:10</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>behavior: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>jsonStr: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="modelapi.html" class="tsd-kind-icon">Model<wbr>Api</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class">
								<a href="modelapi.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="modelapi.html#model_name" class="tsd-kind-icon">model_<wbr>name</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="modelapi.html#createchat" class="tsd-kind-icon">create<wbr>Chat</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="modelapi.html#getforwardurl" class="tsd-kind-icon">get<wbr>ForwardURL</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="modelapi.html#gethistoryversion" class="tsd-kind-icon">get<wbr>History<wbr>Version</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="modelapi.html#getmodelrequest" class="tsd-kind-icon">get<wbr>Model<wbr>Request</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="modelapi.html#getremarklist" class="tsd-kind-icon">get<wbr>Remark<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="modelapi.html#getsection" class="tsd-kind-icon">get<wbr>Section</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="modelapi.html#gettimebyidentifier" class="tsd-kind-icon">get<wbr>Time<wbr>ByIdentifier</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="modelapi.html#groupsearch" class="tsd-kind-icon">group<wbr>Search</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="modelapi.html#intentsearch" class="tsd-kind-icon">intent<wbr>Search</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="modelapi.html#jointsearch" class="tsd-kind-icon">joint<wbr>Search</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="modelapi.html#mappingfetch" class="tsd-kind-icon">mapping<wbr>Fetch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="modelapi.html#postmodelrequest" class="tsd-kind-icon">post<wbr>Model<wbr>Request</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="modelapi.html#supercascaderfetch" class="tsd-kind-icon">super<wbr>Cascader<wbr>Fetch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="modelapi.html#supercascadersearch" class="tsd-kind-icon">super<wbr>Cascader<wbr>Search</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="modelapi.html#toolbarenhancervalid" class="tsd-kind-icon">toolbar<wbr>Enhancer<wbr>Valid</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>