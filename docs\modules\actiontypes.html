<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>ActionTypes | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="actiontypes.html">ActionTypes</a>
				</li>
			</ul>
			<h1>Namespace ActionTypes</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Namespaces</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-namespace tsd-parent-kind-namespace"><a href="actiontypes.detailparametermanager.html" class="tsd-kind-icon">Detail<wbr>Parameter<wbr>Manager</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Enumerations</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-enum tsd-parent-kind-namespace"><a href="../enums/actiontypes.validatortrigger.html" class="tsd-kind-icon">Validator<wbr>Trigger</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Type aliases</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#bigactioncheckresp" class="tsd-kind-icon">Big<wbr>Action<wbr>Check<wbr>Resp</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#bigactiondetailquery" class="tsd-kind-icon">Big<wbr>Action<wbr>Detail<wbr>Query</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#bigactiondetailresult" class="tsd-kind-icon">Big<wbr>Action<wbr>Detail<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#bigactionschemalabel" class="tsd-kind-icon">Big<wbr>Action<wbr>Schema<wbr>Label</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#bigactiontaskdata" class="tsd-kind-icon">Big<wbr>Action<wbr>Task<wbr>Data</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#bigactiontaskdetail" class="tsd-kind-icon">Big<wbr>Action<wbr>Task<wbr>Detail</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#hintslistitem" class="tsd-kind-icon">Hints<wbr>List<wbr>Item</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#jointsearchparams" class="tsd-kind-icon">Joint<wbr>Search<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#mainhint" class="tsd-kind-icon">Main<wbr>Hint</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#mainvalidation" class="tsd-kind-icon">Main<wbr>Validation</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#taginfos" class="tsd-kind-icon">Tag<wbr>Infos</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#validationslistitem" class="tsd-kind-icon">Validations<wbr>List<wbr>Item</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#validatorinputerresult" class="tsd-kind-icon">Validator<wbr>Inputer<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#addinputs_parameterparams" class="tsd-kind-icon">add<wbr>Inputs_<wbr>parameter<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#constructortype" class="tsd-kind-icon">constructor<wbr>Type</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#detadetail" class="tsd-kind-icon">deta<wbr>Detail</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#details_parameters" class="tsd-kind-icon">details_<wbr>parameters</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#eacherrorrow" class="tsd-kind-icon">each<wbr>Error<wbr>Row</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#eachonerror" class="tsd-kind-icon">each<wbr>OnError</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#eachonprogress" class="tsd-kind-icon">each<wbr>OnProgress</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#eachssemessageresp" class="tsd-kind-icon">each<wbr>Sse<wbr>Message<wbr>Resp</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#envparameter" class="tsd-kind-icon">env<wbr>Parameter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#executebatchparams" class="tsd-kind-icon">execute<wbr>Batch<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#executeeachparams" class="tsd-kind-icon">execute<wbr>Each<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#executenowparams" class="tsd-kind-icon">execute<wbr>Now<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#executeparams" class="tsd-kind-icon">execute<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#getauthorslistrequestresult" class="tsd-kind-icon">get<wbr>Authors<wbr>List<wbr>Request<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#getdatasourcerequestparams" class="tsd-kind-icon">get<wbr>Data<wbr>Source<wbr>Request<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#getdatasourcerequestresult" class="tsd-kind-icon">get<wbr>Data<wbr>Source<wbr>Request<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#info" class="tsd-kind-icon">info</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#initialparams" class="tsd-kind-icon">initial<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#inputsparameter" class="tsd-kind-icon">inputs<wbr>Parameter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#inputs_parameters-3" class="tsd-kind-icon">inputs_<wbr>parameters</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#list_parameter" class="tsd-kind-icon">list_<wbr>parameter</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#rule" class="tsd-kind-icon">rule</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#selectedlist" class="tsd-kind-icon">selected<wbr>List</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#updatecontrolspropertiesrequestparams" class="tsd-kind-icon">update<wbr>Controls<wbr>Properties<wbr>Request<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#updatecontrolspropertiesrequestresult" class="tsd-kind-icon">update<wbr>Controls<wbr>Properties<wbr>Request<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#updateinitialparams" class="tsd-kind-icon">update<wbr>Initial<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#validatebatchparams" class="tsd-kind-icon">validate<wbr>Batch<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#validaterequestresult" class="tsd-kind-icon">validate<wbr>Request<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="actiontypes.html#validationsofdetail" class="tsd-kind-icon">validations<wbr>OfDetail</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Type aliases</h2>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="bigactioncheckresp" class="tsd-anchor"></a>
					<h3>Big<wbr>Action<wbr>Check<wbr>Resp</h3>
					<div class="tsd-signature tsd-kind-icon">Big<wbr>Action<wbr>Check<wbr>Resp<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>datasource<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>running<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>taskId<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1975</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>bigAction 任务当前的运行状态</p>
						</div>
					</div>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> datasource<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>当前 action 对于数据模型的数据源</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>running<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>true 存在运行中的任务</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> task<wbr>Id<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>运行中的任务id</p>
									</div>
								</div>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="bigactiondetailquery" class="tsd-anchor"></a>
					<h3>Big<wbr>Action<wbr>Detail<wbr>Query</h3>
					<div class="tsd-signature tsd-kind-icon">Big<wbr>Action<wbr>Detail<wbr>Query<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>pageIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>pageSize<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>taskId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1966</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>page<wbr>Index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>page<wbr>Size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>task<wbr>Id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="bigactiondetailresult" class="tsd-anchor"></a>
					<h3>Big<wbr>Action<wbr>Detail<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">Big<wbr>Action<wbr>Detail<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>dataList<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#bigactiontaskdata" class="tsd-signature-type">BigActionTaskData</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>detail<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#bigactiontaskdetail" class="tsd-signature-type">BigActionTaskDetail</a><span class="tsd-signature-symbol">; </span>labels<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#bigactionschemalabel" class="tsd-signature-type">BigActionSchemaLabel</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>pageIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>pageSize<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1957</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>data<wbr>List<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#bigactiontaskdata" class="tsd-signature-type">BigActionTaskData</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>detail<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#bigactiontaskdetail" class="tsd-signature-type">BigActionTaskDetail</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5>labels<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#bigactionschemalabel" class="tsd-signature-type">BigActionSchemaLabel</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>page<wbr>Index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>page<wbr>Size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="bigactionschemalabel" class="tsd-anchor"></a>
					<h3>Big<wbr>Action<wbr>Schema<wbr>Label</h3>
					<div class="tsd-signature tsd-kind-icon">Big<wbr>Action<wbr>Schema<wbr>Label<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1927</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="bigactiontaskdata" class="tsd-anchor"></a>
					<h3>Big<wbr>Action<wbr>Task<wbr>Data</h3>
					<div class="tsd-signature tsd-kind-icon">Big<wbr>Action<wbr>Task<wbr>Data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>errorMsg<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>taskId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1932</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>error<wbr>Msg<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>task<wbr>Id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="bigactiontaskdetail" class="tsd-anchor"></a>
					<h3>Big<wbr>Action<wbr>Task<wbr>Detail</h3>
					<div class="tsd-signature tsd-kind-icon">Big<wbr>Action<wbr>Task<wbr>Detail<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>actionLabel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>actionName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>beginTime<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>createTime<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>creatorName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fileName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>finish<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>finishTime<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>modelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>schemaLabel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>taskType<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1940</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>action<wbr>Label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>action<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>begin<wbr>Time<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>create<wbr>Time<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>creator<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>file<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>finish<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>finish<wbr>Time<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>model<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>schema<wbr>Label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>task<wbr>Type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="hintslistitem" class="tsd-anchor"></a>
					<h3>Hints<wbr>List<wbr>Item</h3>
					<div class="tsd-signature tsd-kind-icon">Hints<wbr>List<wbr>Item<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2104</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="jointsearchparams" class="tsd-anchor"></a>
					<h3>Joint<wbr>Search<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">Joint<wbr>Search<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>form_params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">; </span>keyword<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>pageSize<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>page_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">: </span><a href="../globals.html#prefilter" class="tsd-signature-type">prefilter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>search_field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>selected<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#selectedlist" class="tsd-signature-type">selectedList</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tagFilters<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2151</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> action<wbr>Id<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>form_<wbr>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>keyword<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> page<wbr>Size<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>page_<wbr>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>prefilters<span class="tsd-signature-symbol">: </span><a href="../globals.html#prefilter" class="tsd-signature-type">prefilter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>search_<wbr>field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>selected<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>selected_<wbr>list<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#selectedlist" class="tsd-signature-type">selectedList</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tag<wbr>Filters<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="mainhint" class="tsd-anchor"></a>
					<h3>Main<wbr>Hint</h3>
					<div class="tsd-signature tsd-kind-icon">Main<wbr>Hint<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>error<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>listIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>sender<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>suggestion<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2092</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>error<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>list<wbr>Index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>sender<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>suggestion<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="mainvalidation" class="tsd-anchor"></a>
					<h3>Main<wbr>Validation</h3>
					<div class="tsd-signature tsd-kind-icon">Main<wbr>Validation<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>error<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>listIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>sender<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>suggestion<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2098</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>error<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>list<wbr>Index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>sender<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>suggestion<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="taginfos" class="tsd-anchor"></a>
					<h3>Tag<wbr>Infos</h3>
					<div class="tsd-signature tsd-kind-icon">Tag<wbr>Infos<span class="tsd-signature-symbol">:</span> <a href="tagmanagertypes.html#taginfo" class="tsd-signature-type">TagInfo</a><span class="tsd-signature-symbol">[]</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2138</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="validationslistitem" class="tsd-anchor"></a>
					<h3>Validations<wbr>List<wbr>Item</h3>
					<div class="tsd-signature tsd-kind-icon">Validations<wbr>List<wbr>Item<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>error<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>listIndex<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2091</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>error<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>list<wbr>Index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="validatorinputerresult" class="tsd-anchor"></a>
					<h3>Validator<wbr>Inputer<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">Validator<wbr>Inputer<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>hintsOfList<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#hintslistitem" class="tsd-signature-type">HintsListItem</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>mainHints<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#mainhint" class="tsd-signature-type">MainHint</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>mainValidations<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="actiontypes.html#mainvalidation" class="tsd-signature-type">MainValidation</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>silent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>validationsOfDetails<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#validationsofdetail" class="tsd-signature-type">validationsOfDetail</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>validationsOfList<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#validationslistitem" class="tsd-signature-type">ValidationsListItem</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2106</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>hints<wbr>OfList<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#hintslistitem" class="tsd-signature-type">HintsListItem</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>main<wbr>Hints<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#mainhint" class="tsd-signature-type">MainHint</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>main<wbr>Validations<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="actiontypes.html#mainvalidation" class="tsd-signature-type">MainValidation</a><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>silent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>validations<wbr>OfDetails<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#validationsofdetail" class="tsd-signature-type">validationsOfDetail</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>validations<wbr>OfList<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#validationslistitem" class="tsd-signature-type">ValidationsListItem</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="addinputs_parameterparams" class="tsd-anchor"></a>
					<h3>add<wbr>Inputs_<wbr>parameter<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">add<wbr>Inputs_<wbr>parameter<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{}</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2001</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-index-signature">
								<h5><span class="tsd-signature-symbol">[</span>property: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="constructortype" class="tsd-anchor"></a>
					<h3>constructor<wbr>Type</h3>
					<div class="tsd-signature tsd-kind-icon">constructor<wbr>Type<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>action_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>model_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2021</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>action_<wbr>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>model_<wbr>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="detadetail" class="tsd-anchor"></a>
					<h3>deta<wbr>Detail</h3>
					<div class="tsd-signature tsd-kind-icon">deta<wbr>Detail<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>deleted<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>values<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>keyValue<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>rowData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2027</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>deleted<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>values<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>keyValue<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>rowData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="details_parameters" class="tsd-anchor"></a>
					<h3>details_<wbr>parameters</h3>
					<div class="tsd-signature tsd-kind-icon">details_<wbr>parameters<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>allowUserModifyRows<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>controls<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="actiontypes.html#inputsparameter" class="tsd-signature-type">inputsParameter</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>datas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>controls<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>keyValue<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>rowData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>keyField<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>rules<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>style<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"form"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"table"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"template"</span><span class="tsd-signature-symbol">; </span>template<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>visible<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2202</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>allow<wbr>User<wbr>Modify<wbr>Rows<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>controls<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="actiontypes.html#inputsparameter" class="tsd-signature-type">inputsParameter</a><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>datas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>controls<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>keyValue<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>rowData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>key<wbr>Field<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>rules<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter-index-signature">
										<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-symbol">{ </span>message<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>required<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>trigger<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h5>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>style<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"form"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"table"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"template"</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> template<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>visible<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="eacherrorrow" class="tsd-anchor"></a>
					<h3>each<wbr>Error<wbr>Row</h3>
					<div class="tsd-signature tsd-kind-icon">each<wbr>Error<wbr>Row<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>error<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2282</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>error<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="eachonerror" class="tsd-anchor"></a>
					<h3>each<wbr>OnError</h3>
					<div class="tsd-signature tsd-kind-icon">each<wbr>OnError<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>errorRow<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#eacherrorrow" class="tsd-signature-type">eachErrorRow</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2285</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>errorRow<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#eacherrorrow" class="tsd-signature-type">eachErrorRow</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>errorRow: <a href="actiontypes.html#eacherrorrow" class="tsd-signature-type">eachErrorRow</a><span class="tsd-signature-symbol">[]</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="eachonprogress" class="tsd-anchor"></a>
					<h3>each<wbr>OnProgress</h3>
					<div class="tsd-signature tsd-kind-icon">each<wbr>OnProgress<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, successCount<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2284</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, successCount<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>percent: <span class="tsd-signature-type">number</span></h5>
											</li>
											<li>
												<h5>successCount: <span class="tsd-signature-type">number</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="eachssemessageresp" class="tsd-anchor"></a>
					<h3>each<wbr>Sse<wbr>Message<wbr>Resp</h3>
					<div class="tsd-signature tsd-kind-icon">each<wbr>Sse<wbr>Message<wbr>Resp<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>current<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>forward<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>id<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>max<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>msg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2273</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> current<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> forward<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> id<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> max<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> msg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> type<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="envparameter" class="tsd-anchor"></a>
					<h3>env<wbr>Parameter</h3>
					<div class="tsd-signature tsd-kind-icon">env<wbr>Parameter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>result<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2198</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>result<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="executebatchparams" class="tsd-anchor"></a>
					<h3>execute<wbr>Batch<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">execute<wbr>Batch<wbr>Params<span class="tsd-signature-symbol">:</span> <a href="actiontypes.html#executeparams" class="tsd-signature-type">executeParams</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>batchSchema<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>list_parameters<span class="tsd-signature-symbol">?: </span><a href="actiontypes.html#list_parameter" class="tsd-signature-type">list_parameter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">?: </span><a href="actiontypes.html#selectedlist" class="tsd-signature-type">selectedList</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tagInfosForList<span class="tsd-signature-symbol">?: </span><a href="actiontypes.html#taginfos" class="tsd-signature-type">TagInfos</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2139</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="executeeachparams" class="tsd-anchor"></a>
					<h3>execute<wbr>Each<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">execute<wbr>Each<wbr>Params<span class="tsd-signature-symbol">:</span> <a href="actiontypes.html#executeparams" class="tsd-signature-type">executeParams</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>filters<span class="tsd-signature-symbol">: </span><a href="tools.html#filterstype" class="tsd-signature-type">FiltersType</a><span class="tsd-signature-symbol">; </span>tagFilters<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#tagfilter" class="tsd-signature-type">TagFilter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>top<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2145</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="executenowparams" class="tsd-anchor"></a>
					<h3>execute<wbr>Now<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">execute<wbr>Now<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>dataDetails<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="actiontypes.html#detadetail" class="tsd-signature-type">detaDetail</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>inputs_parameters<span class="tsd-signature-symbol">?: </span><a href="actiontypes.html#executenowparams.__type-21.inputs_parameters" class="tsd-signature-type">inputs_parameters</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>prefilters<span class="tsd-signature-symbol">?: </span><a href="actiontypes.html#executenowparams.__type-21.prefilters-1" class="tsd-signature-type">prefilters</a><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">?: </span><a href="actiontypes.html#selectedlist" class="tsd-signature-type">selectedList</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2056</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> data<wbr>Details<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="actiontypes.html#detadetail" class="tsd-signature-type">detaDetail</a><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> inputs_<wbr>parameters<span class="tsd-signature-symbol">?: </span><a href="actiontypes.html#executenowparams.__type-21.inputs_parameters" class="tsd-signature-type">inputs_parameters</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> prefilters<span class="tsd-signature-symbol">?: </span><a href="actiontypes.html#executenowparams.__type-21.prefilters-1" class="tsd-signature-type">prefilters</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> selected_<wbr>list<span class="tsd-signature-symbol">?: </span><a href="actiontypes.html#selectedlist" class="tsd-signature-type">selectedList</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="executeparams" class="tsd-anchor"></a>
					<h3>execute<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">execute<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>dataDetails<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="actiontypes.html#detadetail" class="tsd-signature-type">detaDetail</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>filters<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>inputs_parameters<span class="tsd-signature-symbol">?: </span><a href="actiontypes.html#executeparams.__type-22.inputs_parameters-1" class="tsd-signature-type">inputs_parameters</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tagInfos<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>behavior<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"insert"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"update"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"delete"</span><span class="tsd-signature-symbol">; </span>tags<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#taginfo" class="tsd-signature-type">TagInfo</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>workflowExecuteContext<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#workflowexecutecontext" class="tsd-signature-type">WorkflowExecuteContext</a><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2043</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> data<wbr>Details<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><a href="actiontypes.html#detadetail" class="tsd-signature-type">detaDetail</a><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> filters<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> inputs_<wbr>parameters<span class="tsd-signature-symbol">?: </span><a href="actiontypes.html#executeparams.__type-22.inputs_parameters-1" class="tsd-signature-type">inputs_parameters</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> tag<wbr>Infos<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>behavior<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"insert"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"update"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"delete"</span><span class="tsd-signature-symbol">; </span>tags<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#taginfo" class="tsd-signature-type">TagInfo</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> workflow<wbr>Execute<wbr>Context<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#workflowexecutecontext" class="tsd-signature-type">WorkflowExecuteContext</a></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>工作流的上下文参数</p>
									</div>
								</div>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="getauthorslistrequestresult" class="tsd-anchor"></a>
					<h3>get<wbr>Authors<wbr>List<wbr>Request<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">get<wbr>Authors<wbr>List<wbr>Request<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>is_superuser<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>login_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2127</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="getdatasourcerequestparams" class="tsd-anchor"></a>
					<h3>get<wbr>Data<wbr>Source<wbr>Request<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">get<wbr>Data<wbr>Source<wbr>Request<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>additionQuery<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">; </span>funcName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2063</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> addition<wbr>Query<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>func<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="getdatasourcerequestresult" class="tsd-anchor"></a>
					<h3>get<wbr>Data<wbr>Source<wbr>Request<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">get<wbr>Data<wbr>Source<wbr>Request<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">[]</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2115</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="info" class="tsd-anchor"></a>
					<h3>info</h3>
					<div class="tsd-signature tsd-kind-icon">info<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>actionId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>action_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>authed<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>back_image<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>behavior<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>canBatch<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>col_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>container<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>css<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>enabled<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>forward<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label_position<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label_width<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>need_footer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>need_title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>on<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>open_in_new_tab<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>details_parameters<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#info.__type-24.parameters.__type-25.details_parameters-1" class="tsd-signature-type">details_parameters</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>env_parameters<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#envparameter" class="tsd-signature-type">envParameter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>inputs_parameters<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#inputsparameter" class="tsd-signature-type">inputsParameter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>inputs_style<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"template"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"form"</span><span class="tsd-signature-symbol">; </span>inputs_template<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>prompt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>rules<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>secondaryConfirmMsg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>size_percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>subProjectName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>tagGroups<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>behaviors<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">"insert"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"update"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"delete"</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>tagGroups<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#taggroup-1" class="tsd-signature-type">TagGroup</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tags<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#taginfo" class="tsd-signature-type">TagInfo</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>uniplat_version<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2230</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-index-signature">
								<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>action<wbr>Id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>action_<wbr>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>authed<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>back_<wbr>image<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>behavior<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>can<wbr>Batch<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>col_<wbr>count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>container<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>css<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>enabled<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>forward<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>label_<wbr>position<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>label_<wbr>width<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>need_<wbr>footer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>need_<wbr>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>on<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>open_<wbr>in_<wbr>new_<wbr>tab<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>details_parameters<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#info.__type-24.parameters.__type-25.details_parameters-1" class="tsd-signature-type">details_parameters</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>env_parameters<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#envparameter" class="tsd-signature-type">envParameter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>inputs_parameters<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#inputsparameter" class="tsd-signature-type">inputsParameter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>inputs_style<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"template"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"form"</span><span class="tsd-signature-symbol">; </span>inputs_template<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter">
										<h5>details_<wbr>parameters<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#info.__type-24.parameters.__type-25.details_parameters-1" class="tsd-signature-type">details_parameters</a><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>env_<wbr>parameters<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#envparameter" class="tsd-signature-type">envParameter</a><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>inputs_<wbr>parameters<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#inputsparameter" class="tsd-signature-type">inputsParameter</a><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>inputs_<wbr>style<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"template"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"form"</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>inputs_<wbr>template<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">[]</span></h5>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>prompt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>rules<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter-index-signature">
										<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><a href="actiontypes.html#rule" class="tsd-signature-type">rule</a><span class="tsd-signature-symbol">[]</span></h5>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> secondary<wbr>Confirm<wbr>Msg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>size_<wbr>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>sub<wbr>Project<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tag<wbr>Groups<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>behaviors<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">"insert"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"update"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"delete"</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>tagGroups<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#taggroup-1" class="tsd-signature-type">TagGroup</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tags<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#taginfo" class="tsd-signature-type">TagInfo</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter">
										<h5>behaviors<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">"insert"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"update"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"delete"</span><span class="tsd-signature-symbol">&gt;</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>tag<wbr>Groups<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#taggroup-1" class="tsd-signature-type">TagGroup</a><span class="tsd-signature-symbol">[]</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>tags<span class="tsd-signature-symbol">: </span><a href="tagmanagertypes.html#taginfo" class="tsd-signature-type">TagInfo</a><span class="tsd-signature-symbol">[]</span></h5>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>uniplat_<wbr>version<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="initialparams" class="tsd-anchor"></a>
					<h3>initial<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">initial<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>prefilters<span class="tsd-signature-symbol">: </span><a href="../globals.html#prefilter" class="tsd-signature-type">prefilter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#selectedlist" class="tsd-signature-type">selectedList</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1990</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>prefilters<span class="tsd-signature-symbol">: </span><a href="../globals.html#prefilter" class="tsd-signature-type">prefilter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>selected_<wbr>list<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#selectedlist" class="tsd-signature-type">selectedList</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="inputsparameter" class="tsd-anchor"></a>
					<h3>inputs<wbr>Parameter</h3>
					<div class="tsd-signature tsd-kind-icon">inputs<wbr>Parameter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>clearable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>default_value<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>ext_properties<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span>filterable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label_width<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>model_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>placeholder<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>readonly<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>tip<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>treeModelName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>treeParentMode<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>validator<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>trigger<span class="tsd-signature-symbol">: </span><a href="../enums/actiontypes.validatortrigger.html" class="tsd-signature-type">ValidatorTrigger</a><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2178</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-index-signature">
								<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>clearable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> default_<wbr>value<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>ext_<wbr>properties<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>filterable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>label_<wbr>width<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>model_<wbr>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>placeholder<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>readonly<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tip<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> tree<wbr>Model<wbr>Name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>tree<wbr>Parent<wbr>Mode<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>validator<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>trigger<span class="tsd-signature-symbol">: </span><a href="../enums/actiontypes.validatortrigger.html" class="tsd-signature-type">ValidatorTrigger</a><span class="tsd-signature-symbol"> }</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter">
										<h5>trigger<span class="tsd-signature-symbol">: </span><a href="../enums/actiontypes.validatortrigger.html" class="tsd-signature-type">ValidatorTrigger</a></h5>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="inputs_parameters-3" class="tsd-anchor"></a>
					<h3>inputs_<wbr>parameters</h3>
					<div class="tsd-signature tsd-kind-icon">inputs_<wbr>parameters<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2039</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="list_parameter" class="tsd-anchor"></a>
					<h3>list_<wbr>parameter</h3>
					<div class="tsd-signature tsd-kind-icon">list_<wbr>parameter<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{}</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2135</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-index-signature">
								<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="rule" class="tsd-anchor"></a>
					<h3>rule</h3>
					<div class="tsd-signature tsd-kind-icon">rule<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>max<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>message<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>min<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>pattern<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>required<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>trigger<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2164</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> max<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>message<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> min<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> pattern<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> required<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>trigger<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> type<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="selectedlist" class="tsd-anchor"></a>
					<h3>selected<wbr>List</h3>
					<div class="tsd-signature tsd-kind-icon">selected<wbr>List<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>v<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2026</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>v<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="updatecontrolspropertiesrequestparams" class="tsd-anchor"></a>
					<h3>update<wbr>Controls<wbr>Properties<wbr>Request<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">update<wbr>Controls<wbr>Properties<wbr>Request<wbr>Params<span class="tsd-signature-symbol">:</span> <a href="actiontypes.html#executeparams" class="tsd-signature-type">executeParams</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2068</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="updatecontrolspropertiesrequestresult" class="tsd-anchor"></a>
					<h3>update<wbr>Controls<wbr>Properties<wbr>Request<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">update<wbr>Controls<wbr>Properties<wbr>Request<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>details<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>controls<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>datas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>controls<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>keyValue<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>rowData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>visible<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>masters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2069</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>details<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>controls<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>datas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Array</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>controls<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>keyValue<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>rowData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>visible<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>masters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="updateinitialparams" class="tsd-anchor"></a>
					<h3>update<wbr>Initial<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">update<wbr>Initial<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>prefilters<span class="tsd-signature-symbol">?: </span><a href="../globals.html#prefilter" class="tsd-signature-type">prefilter</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>selected_list<span class="tsd-signature-symbol">?: </span><a href="actiontypes.html#selectedlist" class="tsd-signature-type">selectedList</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>workflowExecuteContext<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#workflowexecutecontext" class="tsd-signature-type">WorkflowExecuteContext</a><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1995</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> prefilters<span class="tsd-signature-symbol">?: </span><a href="../globals.html#prefilter" class="tsd-signature-type">prefilter</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> selected_<wbr>list<span class="tsd-signature-symbol">?: </span><a href="actiontypes.html#selectedlist" class="tsd-signature-type">selectedList</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> workflow<wbr>Execute<wbr>Context<span class="tsd-signature-symbol">?: </span><a href="workflow2.html#workflowexecutecontext" class="tsd-signature-type">WorkflowExecuteContext</a></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="validatebatchparams" class="tsd-anchor"></a>
					<h3>validate<wbr>Batch<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">validate<wbr>Batch<wbr>Params<span class="tsd-signature-symbol">:</span> <a href="actiontypes.html#executebatchparams" class="tsd-signature-type">executeBatchParams</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2134</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="validaterequestresult" class="tsd-anchor"></a>
					<h3>validate<wbr>Request<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">validate<wbr>Request<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>hintsOfList<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#hintslistitem" class="tsd-signature-type">HintsListItem</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>mainHints<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#mainhint" class="tsd-signature-type">MainHint</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>mainValidations<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#mainvalidation" class="tsd-signature-type">MainValidation</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>silent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>validationsOfDetails<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#validationsofdetail" class="tsd-signature-type">validationsOfDetail</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>validationsOfList<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#validationslistitem" class="tsd-signature-type">ValidationsListItem</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2118</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>hints<wbr>OfList<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#hintslistitem" class="tsd-signature-type">HintsListItem</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>main<wbr>Hints<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#mainhint" class="tsd-signature-type">MainHint</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>main<wbr>Validations<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#mainvalidation" class="tsd-signature-type">MainValidation</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>silent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>validations<wbr>OfDetails<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#validationsofdetail" class="tsd-signature-type">validationsOfDetail</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>validations<wbr>OfList<span class="tsd-signature-symbol">: </span><a href="actiontypes.html#validationslistitem" class="tsd-signature-type">ValidationsListItem</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="validationsofdetail" class="tsd-anchor"></a>
					<h3>validations<wbr>OfDetail</h3>
					<div class="tsd-signature tsd-kind-icon">validations<wbr>OfDetail<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:2105</li>
						</ul>
					</aside>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
					<li class="current tsd-kind-namespace">
						<a href="actiontypes.html">Action<wbr>Types</a>
						<ul>
							<li class=" tsd-kind-namespace tsd-parent-kind-namespace">
								<a href="actiontypes.detailparametermanager.html">Detail<wbr>Parameter<wbr>Manager</a>
							</li>
						</ul>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
					<li class=" tsd-kind-enum tsd-parent-kind-namespace">
						<a href="../enums/actiontypes.validatortrigger.html" class="tsd-kind-icon">Validator<wbr>Trigger</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#bigactioncheckresp" class="tsd-kind-icon">Big<wbr>Action<wbr>Check<wbr>Resp</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#bigactiondetailquery" class="tsd-kind-icon">Big<wbr>Action<wbr>Detail<wbr>Query</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#bigactiondetailresult" class="tsd-kind-icon">Big<wbr>Action<wbr>Detail<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#bigactionschemalabel" class="tsd-kind-icon">Big<wbr>Action<wbr>Schema<wbr>Label</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#bigactiontaskdata" class="tsd-kind-icon">Big<wbr>Action<wbr>Task<wbr>Data</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#bigactiontaskdetail" class="tsd-kind-icon">Big<wbr>Action<wbr>Task<wbr>Detail</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#hintslistitem" class="tsd-kind-icon">Hints<wbr>List<wbr>Item</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#jointsearchparams" class="tsd-kind-icon">Joint<wbr>Search<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#mainhint" class="tsd-kind-icon">Main<wbr>Hint</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#mainvalidation" class="tsd-kind-icon">Main<wbr>Validation</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#taginfos" class="tsd-kind-icon">Tag<wbr>Infos</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#validationslistitem" class="tsd-kind-icon">Validations<wbr>List<wbr>Item</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#validatorinputerresult" class="tsd-kind-icon">Validator<wbr>Inputer<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#addinputs_parameterparams" class="tsd-kind-icon">add<wbr>Inputs_<wbr>parameter<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#constructortype" class="tsd-kind-icon">constructor<wbr>Type</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#detadetail" class="tsd-kind-icon">deta<wbr>Detail</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#details_parameters" class="tsd-kind-icon">details_<wbr>parameters</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#eacherrorrow" class="tsd-kind-icon">each<wbr>Error<wbr>Row</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#eachonerror" class="tsd-kind-icon">each<wbr>OnError</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#eachonprogress" class="tsd-kind-icon">each<wbr>OnProgress</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#eachssemessageresp" class="tsd-kind-icon">each<wbr>Sse<wbr>Message<wbr>Resp</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#envparameter" class="tsd-kind-icon">env<wbr>Parameter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#executebatchparams" class="tsd-kind-icon">execute<wbr>Batch<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#executeeachparams" class="tsd-kind-icon">execute<wbr>Each<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#executenowparams" class="tsd-kind-icon">execute<wbr>Now<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#executeparams" class="tsd-kind-icon">execute<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#getauthorslistrequestresult" class="tsd-kind-icon">get<wbr>Authors<wbr>List<wbr>Request<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#getdatasourcerequestparams" class="tsd-kind-icon">get<wbr>Data<wbr>Source<wbr>Request<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#getdatasourcerequestresult" class="tsd-kind-icon">get<wbr>Data<wbr>Source<wbr>Request<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#info" class="tsd-kind-icon">info</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#initialparams" class="tsd-kind-icon">initial<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#inputsparameter" class="tsd-kind-icon">inputs<wbr>Parameter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#inputs_parameters-3" class="tsd-kind-icon">inputs_<wbr>parameters</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#list_parameter" class="tsd-kind-icon">list_<wbr>parameter</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#rule" class="tsd-kind-icon">rule</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#selectedlist" class="tsd-kind-icon">selected<wbr>List</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#updatecontrolspropertiesrequestparams" class="tsd-kind-icon">update<wbr>Controls<wbr>Properties<wbr>Request<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#updatecontrolspropertiesrequestresult" class="tsd-kind-icon">update<wbr>Controls<wbr>Properties<wbr>Request<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#updateinitialparams" class="tsd-kind-icon">update<wbr>Initial<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#validatebatchparams" class="tsd-kind-icon">validate<wbr>Batch<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#validaterequestresult" class="tsd-kind-icon">validate<wbr>Request<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="actiontypes.html#validationsofdetail" class="tsd-kind-icon">validations<wbr>OfDetail</a>
					</li>
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>