import ChatApi from "./api"

type ChatApiType = InstanceType<typeof ChatApi>
export class Chat {
    private api: ChatApi
    constructor(model_name: string, id: number | string, orgID: string) {
        this.api = new ChatApi(model_name, id, orgID)
    }
    public createChat(autoJoin?: boolean, title?: string) {
        return this.api.createChat(autoJoin, title)
    }

    /**
     * 添加会话成员
     * @param uids 成员ids
     * @param msgId 添加成员的消息可见范围，默认是加入前消息不可见，= 0 表示加入前消息不可见， = 1 表示加入前均可见， < 0 表示加入前N条消息可见
     */
    public addMember(params: Parameters<ChatApiType["addMember"]>[0], msg = 0) {
        return this.api.addMember(params, msg)
    }

    public removeMember(params: Parameters<ChatApiType["removeMember"]>[0]) {
        return this.api.removeMember(params)
    }

    public sendMsg<T>(type: string, msg: T) {
        return this.api.sendMsg<T>(type, msg)
    }

    public startChat<T>() {
        return this.api.startChat<T>()
    }

    public finishChat<T>() {
        return this.api.finishChat<T>()
    }

    public addCs<T>(params: Parameters<ChatApiType["addCs"]>[0]) {
        return this.api.addCs<T>(params)
    }

    public removeCs<T>(params: Parameters<ChatApiType["removeCs"]>[0]) {
        return this.api.removeCs<T>(params)
    }

    public userExitChat<T>() {
        return this.api.userExitChat<T>()
    }

    public csExitChat<T>() {
        return this.api.csExitChat<T>()
    }
}
