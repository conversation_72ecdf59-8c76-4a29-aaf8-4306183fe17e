const k = 1024
const m = 1024 * k
const g = 1024 * m

export function formatFileSize(size: number) {
    if (size === undefined || size === null) {
        return ""
    }
    if (size < k) {
        return size + "B"
    }
    if (size < m) {
        return Number((size / k).toFixed(2)) + "KB"
    }
    if (size < g) {
        return Number((size / m).toFixed(2)) + "MB"
    }
    return Number((size / g).toFixed(2)) + "GB"
}
