import { events } from "../../core/events"
import { exportToExcelForList } from "../../core/tools/sse"
import type * as dto from "../../def/index"

import Workflow2Api from "./api"
import {
    ChangeMasterRequest,
    CreateProcessRequest,
    EditProcessRequest,
    TaskDefaultDealerRequest,
    WorkflowBatchRequest,
} from "./model"

enum OperationType {
    START_PROCESS,
    CHANGE_STATE,
    CREATE_TASK,
    FINISH_TASK,
    CANCEL_TASK,
    EDIT_TASK,
    CHANGE_TASK,
    ADD_REMARK,
    ROLLBACK_TASK,
    ROLLBACK_STATE,
    CHANGE_MANAGER,
    CHANGE_HANDLER,
    CHANGE_TASK_DATE,
    RESTART_PROCESS,
}

export type CheckEditParam = {
    processName: string
    state?: string
    task?: string
}
export class Workflow2 {
    private api: Workflow2Api

    private modelName: string

    private metaModelName: string

    private callbackOnChange: dto.SSE.callBackOfModelUpdated = () => null

    private operationTypeArray = [
        "新建流程",
        "变更状态",
        "创建任务",
        "完成任务",
        "取消任务",
        "编辑任务",
        "更换任务",
        "添加备注",
        "撤回任务",
        "撤回状态",
        "更换负责人",
        "更换处理人",
        "更换任务时间",
        "重启流程",
    ]

    constructor(modelName: string) {
        this.api = new Workflow2Api(modelName)
        this.modelName = modelName
    }

    /**
     * 工作流批量处理
     */
    public workflowBatchRequest(request: WorkflowBatchRequest) {
        return this.api.workflowBatchRequest(request)
    }
    /*
     * 查询当前处理人的接口-从工作流的表里查询
     */
    public queryWorkflowDealerList2() {
        return this.api.queryWorkflowDealerList2()
    }

    /**
     * 获取任务的默认处理人
     */
    public getTaskDefaultDealer(request: TaskDefaultDealerRequest) {
        return this.api.getTaskDefaultDealer(request)
    }

    public changeMaster(request: ChangeMasterRequest) {
        return this.api.changeMaster(request)
    }

    private onTransportMessage = (msg: dto.SSE.msg) => {
        const models = msg.dataUpdates.filter(
            (x) => x.model === this.metaModelName || this.modelName
        )

        if (models.length === 0) return
        this.callbackOnChange && this.callbackOnChange(msg.createByMyself)
    }

    /**
     * 查询当前处理人的接口
     * @param param 供搜索用，目前暂时可以不用传递，传 null 即可
     */
    public queryWorkflowDealerList(param: string) {
        return this.api.queryWorkflowDealerList(param)
    }

    /**
     * @deprecated 请使用detail3
     */
    public detail(processId: number, detailName: string) {
        return this.api.detail(processId, detailName)
    }

    /**
     * @deprecated 请使用detail3
     */
    public detail2(processId: number, detailName: string) {
        return this.api.detail2(processId, detailName)
    }

    public detail3(processId: number, detailName: string) {
        return this.api.detail3(processId, detailName)
    }

    /**
     * 保存流程图
     */
    public saveProcessDef2(request: EditProcessRequest) {
        return this.api.saveProcessDef2(request)
    }

    /**
     * 创建流程图
     */
    public createProcessDef2(request: CreateProcessRequest) {
        return this.api.createProcessDef2(request)
    }

    /**
     * 流程定义信息
     */
    public async processDefDetail(processName: string) {
        return this.api.processDefDetail(processName)
    }

    /**
     * 请求撤回操作
     * @param processId 流程ID
     * @returns true 成功
     */
    public rollbackProcess(processId: number) {
        return this.api.rollbackProcess(processId)
    }

    /**
     * 注册当前模型的数据变化时的回掉
     * @param CallBackOfModelUpdated
     */
    public registerOnChange(cb: dto.SSE.callBackOfModelUpdated) {
        this.callbackOnChange = cb
        return events.addTransportMessageListener(this.onTransportMessage)
    }

    public queryProcessByAssociateId(associateId: any) {
        return this.api.queryProcessByAssociateId(associateId)
    }

    public updateProcessDesc(id: number, desc: string) {
        return this.api.updateProcessDesc(id, desc)
    }

    public updateSummary(id: number, summary: string) {
        return this.api.updateSummary(id, summary)
    }

    public updateOnline(online: boolean, processName: string) {
        return this.api.updateOnline(online, processName)
    }

    public checkStateEditable(param: CheckEditParam) {
        return this.api.checkStateEditable(param)
    }

    public checkTaskEditable(param: CheckEditParam) {
        return this.api.checkTaskEditable(param)
    }

    /*
     *
     * 列出所有的 id 关联的流程数据
     */
    public listProcessInstancePanel(associateId: number) {
        return this.api.listProcessInstancePanel(associateId)
    }
    /**
     * 列出模型所有的 actions
     */
    public listModelActions() {
        return this.api.listModelActions()
    }
    /**
     * 列出所有流程的名称
     */
    public listAllProcessName() {
        return this.api.listAllProcessName()
    }
    /**
     * 列出所有流程的名称
     */
    public listAllProcessNameWithRight() {
        return this.api.listAllProcessNameWithRight()
    }
    /**
     * 解析操作内容
     * @param operation 操作内容
     */
    public resolveOperationContent(
        operation: dto.workflow2.ProcessOperation
    ): string {
        const type = operation.type
        const beforeOperation = this.getStr(operation.beforeOperation)
        const afterOperation = this.getStr(operation.afterOperation)
        const remark = this.getStr(operation.remark)
        if (OperationType.START_PROCESS === type) {
            return `发起了流程`
        } else if (OperationType.CHANGE_STATE === type) {
            return `将状态从"${beforeOperation}"更换到"${afterOperation}"`
        } else if (OperationType.CREATE_TASK === type) {
            return `创建任务:${afterOperation}`
        } else if (OperationType.FINISH_TASK === type) {
            return `完成任务:${beforeOperation}`
        } else if (OperationType.CANCEL_TASK === type) {
            return `取消任务:${beforeOperation}`
        } else if (OperationType.EDIT_TASK === type) {
            return `编辑前:${beforeOperation},编辑后:${afterOperation}`
        } else if (OperationType.CHANGE_TASK === type) {
            return `将任务从"${beforeOperation}"更换到"${afterOperation}"`
        } else if (OperationType.ADD_REMARK === type) {
            return `添加备注:${remark}`
        } else if (OperationType.ROLLBACK_TASK === type) {
            return `恢复上一任务:${afterOperation}`
        } else if (OperationType.ROLLBACK_STATE === type) {
            return `恢复上一状态:${afterOperation}`
        } else if (OperationType.RESTART_PROCESS === type) {
            return `${beforeOperation}<br />${afterOperation}`
        }

        return ""
    }

    private getStr(content: string) {
        if (content === undefined || content === "" || content === null) {
            return "无"
        }
        return content
    }
    /**
     * 列出所有的操作类型
     */
    public getAllOperationType() {
        return this.operationTypeArray
    }
    /**
     * 列出所有的流程
     */
    public listAllProcess() {
        return this.api.listAllProcessDef()
    }
    /**
     * 获取数字对应的操作类型
     * @param index 操作类型的数字类型
     */
    public resolveOperationType(index: number) {
        const operationType = this.operationTypeArray[index]
        if (this.operationTypeArray === undefined) {
            return ""
        }
        return operationType
    }

    public getAssociateId<T>(param: dto.workflow2.ListQueryParam) {
        return this.api.getAssociateId<T>(param)
    }

    /**
     * 添加评论
     */
    public addRemark(param: dto.workflow2.AddRemarkParam) {
        return this.api.addRemark(param)
    }
    /**
     * 查询处理人列表
     */
    public queryDealerList<T>(processName: string, state: string) {
        return this.api.queryDealerList<T>(processName, state)
    }
    /**
     * 更换流程状态批量
     */
    public changeProcessStateBatch(
        param: dto.workflow2.BatchParam<dto.workflow2.ChangeProcessStateParam>
    ) {
        return this.api.changeProcessStateBatch(param)
    }
    /**
     * 新建任务批量
     */
    public createTaskBatch(
        param: dto.workflow2.BatchParam<dto.workflow2.UpdateTaskParam>
    ) {
        return this.api.createTaskBatch(param)
    }
    /**
     * 编辑任务 批量
     */
    public editTaskBatch(
        param: dto.workflow2.BatchParam<dto.workflow2.UpdateTaskParam>
    ) {
        return this.api.editTaskBatch(param)
    }
    /**
     * 更换任务 批量
     */
    public changeTaskBatch(
        param: dto.workflow2.BatchParam<dto.workflow2.ChangeTaskParam>
    ) {
        return this.api.changeTaskBatch(param)
    }
    /**
     * 取消任务 批量
     */
    public cancelTaskBatch(
        param: dto.workflow2.BatchParam<dto.workflow2.CancelTaskParam>
    ) {
        return this.api.cancelTaskBatch(param)
    }
    /**
     * 完成任务 批量
     */
    public finishTaskBatch(
        param: dto.workflow2.BatchParam<dto.workflow2.FinishTaskParam>
    ) {
        return this.api.finishTaskBatch(param)
    }
    /**
     * 批量启动工作流
     */
    public startProcessBatch(
        param: dto.workflow2.BatchParam<dto.workflow2.StartProcessParam>
    ) {
        return this.api.startProcessBatch(param)
    }
    /**
     * 批量启动流程前的检查操作
     * @param processName 流程名称
     * @param ids 关联id数组，逗号分隔
     */
    public startProcessCheck(processName: string, ids: string) {
        return this.api.startProcessCheck(processName, ids)
    }
    /**
     * 查询存在流程（运行中）的数量
     * @param processName 流程名称
     * @param ids 关联id数组，逗号分隔
     */
    public queryProcessExistCount(processName: string, ids: string) {
        return this.api.queryProcessExistCount(processName, ids)
    }
    /**
     * 查询所有流程，携带统计信息
     */
    public queryProcessWithCount(param: dto.workflow2.ListQueryParam) {
        return this.api.queryProcessWithCount(param)
    }
    /**
     * 流程实例的所有评论
     */
    public listRemark(processId: number) {
        return this.api.listRemark(processId)
    }
    /**
     * 流程实例的所有工作记录
     */
    public listTaskRecord(processId: number) {
        return this.api.listFinishTask(processId)
    }
    /**
     * 流程实例的所有操作记录
     */
    public listOperation(
        processId: number,
        param: dto.workflow2.listOperationParam
    ) {
        return this.api.listOperation(processId, param)
    }

    /**
     * 查询所有流程实例
     */
    public async listProcessInstance(param: dto.workflow2.ListQueryParam) {
        const result = await this.api.list(param)
        this.metaModelName = result.meta.modelName
        return result
    }
    /**
     * 获取流程信息
     * @param processName 流程名称
     */
    public getProcessInfo(processName: string) {
        return this.api.getProcessInfo(processName)
    }
    public startProcess(param: dto.workflow2.StartProcessParam) {
        return this.api.startProcess(param)
    }
    /**
     * 更换流程状态
     */
    public changeProcessState(param: dto.workflow2.ChangeProcessStateParam) {
        return this.api.changeProcessState(param)
    }
    /**
     * 新建任务
     */
    public createTask(param: dto.workflow2.UpdateTaskParam) {
        return this.api.createTask(param)
    }
    /**
     * 编辑任务
     */
    public editTask(param: dto.workflow2.UpdateTaskParam) {
        return this.api.editTask(param)
    }
    /**
     * 更换任务
     */
    public changeTask(param: dto.workflow2.ChangeTaskParam) {
        return this.api.changeTask(param)
    }
    /**
     * 取消任务
     */
    public cancelTask(param: dto.workflow2.CancelTaskParam) {
        return this.api.cancelTask(param)
    }
    /**
     * 完成任务
     */
    public finishTask(param: dto.workflow2.FinishTaskParam) {
        return this.api.finishTask(param)
    }
    /**
     * 新增流程
     * @param processDef 流程定义
     */
    public createProcessDef(processDef: dto.workflow2.ProcessEditParam) {
        return this.api.createProcess(processDef)
    }
    /**
     * 编辑流程
     * @param processDef 流程定义
     */
    public editProcessDef(processDef: dto.workflow2.ProcessEditParam) {
        return this.api.editProcess(processDef)
    }

    public homeTodoListCount(datasource: string) {
        return this.api.homeTodoListCount(datasource)
    }

    public homeMasterListCount(datasource: string) {
        return this.api.homeMasterListCount(datasource)
    }

    public datasouceList() {
        return this.api.datasouceList()
    }

    public homeCount(datasource: string) {
        return this.api.homeCount(datasource)
    }

    public exportToExcel(params: dto.workflow2.exportExcelParams) {
        return exportToExcelForList(this.api.createExportUrl(params))
    }

    public getDefaultTemplateUrl(list_name: string, page_name?: string) {
        return this.api.getDefaultTemplateUrl(list_name, page_name)
    }
    /**
     * 工作流状态
     */
    public updateStateFilterParam(processName: string) {
        return this.api.updateStateFilterParam(processName)
    }
    /**
     * 工作流任务
     */
    public updateTaskFilterParam(processName: string) {
        return this.api.updateTaskFilterParam(processName)
    }
}
