import type * as dto from "../../def/index"

import ConfigCenterAPI from "./api"
export class ConfigCenter {
    private api: ConfigCenterAPI
    constructor() {
        this.api = new ConfigCenterAPI()
    }

    public query() {
        return this.api.query()
    }
    public revalidateModels(dataId: string) {
        return this.api.revalidateModels(dataId)
    }
    public rebuildFromTemplate(dataId: string) {
        return this.api.rebuildFromTemplate(dataId)
    }
    public reload() {
        return this.api.reload()
    }
    public createTable(params: dto.ConfigCenterTypes.CreateTableAPIParams) {
        return this.api.createTable(params)
    }
    public createTableSQLScript(
        params: dto.ConfigCenterTypes.createTableSQLScriptAPIParams
    ) {
        return this.api.createTableSQLScript(params)
    }
    public createSchemaSQLScript(
        params: dto.ConfigCenterTypes.createSchemaSQLScript
    ) {
        return this.api.createSchemaSQLScript(params)
    }
    public createDataModel(params: dto.ConfigCenterTypes.createDataModel) {
        return this.api.createDataModel(params)
    }
    public jsonParseETL(dataId: string) {
        return this.api.jsonParseETL(dataId)
    }

    public depoly(dataId: string) {
        return this.api.deploy(dataId)
    }
}
