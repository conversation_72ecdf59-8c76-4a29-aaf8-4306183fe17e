import {
    DateGroupTypeEnum,
    dateGroupTypeMap,
    FieldItem,
    HeaderItem,
    summaryMap,
    SummaryTypeEnum,
} from "./CubeModel"

export class CubeBuilder {
    static getHeaders(
        headers: HeaderItem[],
        fields: { [key: string]: FieldItem },
        path?: string,
        propertyPath?: string
    ) {
        headers.forEach((header) => {
            let isValueExist = true
            if (!header.value) {
                isValueExist = false
                if (header.property === "SUMMARY") {
                    header.value = {
                        value: "SUMMARY",
                        display: "∑值",
                    }
                } else {
                    const property = header.property.split("__")
                    const kindText =
                        summaryMap.get(property[1] as SummaryTypeEnum) ||
                        dateGroupTypeMap.get(property[1] as DateGroupTypeEnum)
                    const v = fields[property[0]]
                    const value = v
                        ? fields[property[0]].label +
                          (property[1] ? `(${kindText})` : "")
                        : ""
                    header.value = {
                        value,
                        display: value,
                    }
                }
            }
            if (path) {
                header.path =
                    path +
                    `\u0005\u0005` +
                    (isValueExist ? header.value.value : header.property)
                header.propertyPath =
                    propertyPath + `\u0005\u0005` + header.property
            } else {
                header.path = isValueExist
                    ? (
                          (header.value.value ?? header.value.display) ||
                          ""
                      ).toString()
                    : header.property
                header.propertyPath = header.property
            }
            if (header.columns && header.columns.length > 0) {
                this.getHeaders(
                    header.columns,
                    fields,
                    header.path,
                    header.propertyPath
                )
            }
        })
        return headers
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    static getTableData(data: any[], fields: { [key: string]: FieldItem }) {
        data.forEach((item) => {
            if (item["SUMMARY"]) {
                const property = item["SUMMARY"].split("__")
                const value =
                    fields[property[0]].label +
                    (property[1]
                        ? `(${summaryMap.get(property[1] as SummaryTypeEnum)})`
                        : "")
                item["SUMMARY"] = {
                    value,
                    display: value,
                }
            }
        })
        return data
    }

    static getFirstColumn(headers: HeaderItem[]) {
        const header = headers[0]
        if (header.columns && header.columns.length > 0) {
            return this.getFirstColumn(header.columns)
        }
        return header
    }
}
