<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>DetailParameterManager | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="detailparametermanager.html">DetailParameterManager</a>
				</li>
			</ul>
			<h1>Class DetailParameterManager</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">DetailParameterManager</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="detailparametermanager.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="detailparametermanager.html#detail" class="tsd-kind-icon">detail</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class"><a href="detailparametermanager.html#name" class="tsd-kind-icon">name</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="detailparametermanager.html#result" class="tsd-kind-icon">result</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="detailparametermanager.html#updator" class="tsd-kind-icon">updator</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="detailparametermanager.html#add" class="tsd-kind-icon">add</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="detailparametermanager.html#delete" class="tsd-kind-icon">delete</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="detailparametermanager.html#deletebypropertyvalue" class="tsd-kind-icon">delete<wbr>ByProperty<wbr>Value</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="detailparametermanager.html#done" class="tsd-kind-icon">done</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="detailparametermanager.html#edit" class="tsd-kind-icon">edit</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="detailparametermanager.html#getinputeddata" class="tsd-kind-icon">get<wbr>Inputed<wbr>Data</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="detailparametermanager.html#handlerowdataparam" class="tsd-kind-icon">handle<wbr>Row<wbr>Data<wbr>Param</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="detailparametermanager.html#uniqby" class="tsd-kind-icon">uniq<wbr>By</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="detailparametermanager.html#uniqbykeyvalue" class="tsd-kind-icon">uniq<wbr>ByKey<wbr>Value</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="detailparametermanager.html#update" class="tsd-kind-icon">update</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new <wbr>Detail<wbr>Parameter<wbr>Manager<span class="tsd-signature-symbol">(</span>detail<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.details_parameters</span>, updator<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span>result<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.DetailParameterManager.result</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="detailparametermanager.html" class="tsd-signature-type">DetailParameterManager</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:30</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>detail: <span class="tsd-signature-type">dto.ActionTypes.details_parameters</span></h5>
								</li>
								<li>
									<h5>updator: <span class="tsd-signature-symbol">(</span>result<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.DetailParameterManager.result</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>result<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.DetailParameterManager.result</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>result: <span class="tsd-signature-type">dto.ActionTypes.DetailParameterManager.result</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="detailparametermanager.html" class="tsd-signature-type">DetailParameterManager</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="detail" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> detail</h3>
					<div class="tsd-signature tsd-kind-icon">detail<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.ActionTypes.details_parameters</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/action.ts:32</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class">
					<a name="name" class="tsd-anchor"></a>
					<h3>name</h3>
					<div class="tsd-signature tsd-kind-icon">name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/action.ts:30</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>detail parameter页签名</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="result" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> result</h3>
					<div class="tsd-signature tsd-kind-icon">result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.ActionTypes.DetailParameterManager.result</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/action.ts:26</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="updator" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> updator</h3>
					<div class="tsd-signature tsd-kind-icon">updator<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>result<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.DetailParameterManager.result</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/action/action.ts:33</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-class">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>result<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.DetailParameterManager.result</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>result: <span class="tsd-signature-type">dto.ActionTypes.DetailParameterManager.result</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="add" class="tsd-anchor"></a>
					<h3>add</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<span class="tsd-signature-symbol">(</span>rawRowData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.DetailParameterManager.addParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:163</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加表单数据</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>rawRowData: <span class="tsd-signature-type">dto.ActionTypes.DetailParameterManager.addParam</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="delete" class="tsd-anchor"></a>
					<h3>delete</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">delete<span class="tsd-signature-symbol">(</span>keyValue<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:113</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>删除指定数据</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>keyValue: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="deletebypropertyvalue" class="tsd-anchor"></a>
					<h3>delete<wbr>ByProperty<wbr>Value</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">delete<wbr>ByProperty<wbr>Value<span class="tsd-signature-symbol">(</span>proerty<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:124</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>删除指定数据</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>proerty: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>value: <span class="tsd-signature-type">unknown</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="done" class="tsd-anchor"></a>
					<h3>done</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">done<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="detailparametermanager.html#result" class="tsd-signature-type">result</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:192</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>编辑完成</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <a href="detailparametermanager.html#result" class="tsd-signature-type">result</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="edit" class="tsd-anchor"></a>
					<h3>edit</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">edit<span class="tsd-signature-symbol">(</span>keyValue<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, rawRowData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.DetailParameterManager.addParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:146</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>更新指定数据</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>keyValue: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>rawRowData: <span class="tsd-signature-type">dto.ActionTypes.DetailParameterManager.addParam</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getinputeddata" class="tsd-anchor"></a>
					<h3>get<wbr>Inputed<wbr>Data</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Inputed<wbr>Data<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>keyValue<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>rowData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>del<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span>edit<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:92</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取默认数据以供编辑</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{ </span>keyValue<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>rowData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>del<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span>edit<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="handlerowdataparam" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> handle<wbr>Row<wbr>Data<wbr>Param</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">handle<wbr>Row<wbr>Data<wbr>Param<span class="tsd-signature-symbol">(</span>rowData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.DetailParameterManager.addParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ActionTypes.DetailParameterManager.rowData</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:61</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>rowData: <span class="tsd-signature-type">dto.ActionTypes.DetailParameterManager.addParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">dto.ActionTypes.DetailParameterManager.rowData</span><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="uniqby" class="tsd-anchor"></a>
					<h3>uniq<wbr>By</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">uniq<wbr>By<span class="tsd-signature-symbol">(</span>proerty<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:182</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>proerty: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="uniqbykeyvalue" class="tsd-anchor"></a>
					<h3>uniq<wbr>ByKey<wbr>Value</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">uniq<wbr>ByKey<wbr>Value<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:170</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="update" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> update</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">update<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/action/action.ts:73</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="detailparametermanager.html" class="tsd-kind-icon">Detail<wbr>Parameter<wbr>Manager</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class">
								<a href="detailparametermanager.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="detailparametermanager.html#detail" class="tsd-kind-icon">detail</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class">
								<a href="detailparametermanager.html#name" class="tsd-kind-icon">name</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="detailparametermanager.html#result" class="tsd-kind-icon">result</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="detailparametermanager.html#updator" class="tsd-kind-icon">updator</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="detailparametermanager.html#add" class="tsd-kind-icon">add</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="detailparametermanager.html#delete" class="tsd-kind-icon">delete</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="detailparametermanager.html#deletebypropertyvalue" class="tsd-kind-icon">delete<wbr>ByProperty<wbr>Value</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="detailparametermanager.html#done" class="tsd-kind-icon">done</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="detailparametermanager.html#edit" class="tsd-kind-icon">edit</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="detailparametermanager.html#getinputeddata" class="tsd-kind-icon">get<wbr>Inputed<wbr>Data</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="detailparametermanager.html#handlerowdataparam" class="tsd-kind-icon">handle<wbr>Row<wbr>Data<wbr>Param</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="detailparametermanager.html#uniqby" class="tsd-kind-icon">uniq<wbr>By</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="detailparametermanager.html#uniqbykeyvalue" class="tsd-kind-icon">uniq<wbr>ByKey<wbr>Value</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="detailparametermanager.html#update" class="tsd-kind-icon">update</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>