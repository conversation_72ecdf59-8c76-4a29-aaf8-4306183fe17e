<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>MediaController | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="mediacontroller.html">MediaController</a>
				</li>
			</ul>
			<h1>Class MediaController</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">MediaController</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="mediacontroller.html#base64tofile" class="tsd-kind-icon">base64to<wbr>File</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="mediacontroller.html#blobtouint8array" class="tsd-kind-icon">blob<wbr>ToUint8<wbr>Array</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="mediacontroller.html#buildthumbnail" class="tsd-kind-icon">build<wbr>Thumbnail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="mediacontroller.html#choosefile" class="tsd-kind-icon">choose<wbr>File</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="mediacontroller.html#choosefiles" class="tsd-kind-icon">choose<wbr>Files</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="mediacontroller.html#dataurltoblob" class="tsd-kind-icon">dataURLto<wbr>Blob</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="mediacontroller.html#downloadfile" class="tsd-kind-icon">download<wbr>File</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="mediacontroller.html#getbase64type" class="tsd-kind-icon">get<wbr>Base64<wbr>Type</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="mediacontroller.html#uploadfile" class="tsd-kind-icon">upload<wbr>File</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="mediacontroller.html#uploadfileforuniapp" class="tsd-kind-icon">upload<wbr>File<wbr>For<wbr>Uni<wbr>App</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="mediacontroller.html#uploadfileforwxapp" class="tsd-kind-icon">upload<wbr>File<wbr>For<wbr>WxApp</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="mediacontroller.html#uploadfileothers" class="tsd-kind-icon">upload<wbr>File<wbr>Others</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="mediacontroller.html#uploadfilev2" class="tsd-kind-icon">upload<wbr>File<wbr>V2</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="mediacontroller.html#wxupload" class="tsd-kind-icon">wx<wbr>Upload</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="base64tofile" class="tsd-anchor"></a>
					<h3>base64to<wbr>File</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">base64to<wbr>File<span class="tsd-signature-symbol">(</span>base64Str<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, fileType<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">File</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/media/index.ts:276</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>base64Str: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>fileType: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">File</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="blobtouint8array" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> blob<wbr>ToUint8<wbr>Array</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">blob<wbr>ToUint8<wbr>Array<span class="tsd-signature-symbol">(</span>blob<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Blob</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">Uint8Array</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/media/index.ts:50</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>blob: <span class="tsd-signature-type">Blob</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">Uint8Array</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="buildthumbnail" class="tsd-anchor"></a>
					<h3>build<wbr>Thumbnail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">build<wbr>Thumbnail<span class="tsd-signature-symbol">(</span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, w<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span>, h<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span>, view<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/media/index.ts:352</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>url: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> w: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> h: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> view: <span class="tsd-signature-type">boolean</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="choosefile" class="tsd-anchor"></a>
					<h3>choose<wbr>File</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">choose<wbr>File<span class="tsd-signature-symbol">(</span>type<span class="tsd-signature-symbol">?: </span><a href="../enums/uploadtype.html" class="tsd-signature-type">UploadType</a>, size<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">File</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/media/index.ts:394</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>选择文件上传，适用于Web，H5</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagDefault value">Default value</span> type: <a href="../enums/uploadtype.html" class="tsd-signature-type">UploadType</a><span class="tsd-signature-symbol"> = UploadType.Default</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> size: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">File</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="choosefiles" class="tsd-anchor"></a>
					<h3>choose<wbr>Files</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">choose<wbr>Files<span class="tsd-signature-symbol">(</span>type<span class="tsd-signature-symbol">?: </span><a href="../enums/uploadtype.html" class="tsd-signature-type">UploadType</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">File</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/media/index.ts:435</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagDefault value">Default value</span> type: <a href="../enums/uploadtype.html" class="tsd-signature-type">UploadType</a><span class="tsd-signature-symbol"> = UploadType.Default</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">File</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="dataurltoblob" class="tsd-anchor"></a>
					<h3>dataURLto<wbr>Blob</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">dataURLto<wbr>Blob<span class="tsd-signature-symbol">(</span>dataurl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Uint8Array</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/media/index.ts:287</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>dataurl: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Uint8Array</span><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="downloadfile" class="tsd-anchor"></a>
					<h3>download<wbr>File</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">download<wbr>File<span class="tsd-signature-symbol">(</span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ArrayBuffer</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/media/index.ts:333</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>url: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ArrayBuffer</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getbase64type" class="tsd-anchor"></a>
					<h3>get<wbr>Base64<wbr>Type</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Base64<wbr>Type<span class="tsd-signature-symbol">(</span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"data:text/plain;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:application/msword;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:application/vnd.ms-excel;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:application/pdf;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:application/vnd.openxmlformats-officedocument.presentationml.presentation;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:application/vnd.ms-powerpoint;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:image/png;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:image/jpeg;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:image/gif;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:image/svg+xml;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:image/x-icon;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:image/bmp;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:application/octet-stream;base64,"</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/media/index.ts:298</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>type: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">"data:text/plain;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:application/msword;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:application/vnd.ms-excel;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:application/pdf;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:application/vnd.openxmlformats-officedocument.presentationml.presentation;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:application/vnd.ms-powerpoint;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:image/png;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:image/jpeg;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:image/gif;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:image/svg+xml;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:image/x-icon;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:image/bmp;base64,"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"data:application/octet-stream;base64,"</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="uploadfile" class="tsd-anchor"></a>
					<h3>upload<wbr>File</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">upload<wbr>File<span class="tsd-signature-symbol">(</span>file<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">File</span>, config<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>getCancelSource<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>cancel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Canceler</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">; </span>onUploadProgress<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/media/index.ts:62</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>file: <span class="tsd-signature-type">File</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> config: <span class="tsd-signature-symbol">{ </span>getCancelSource<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>cancel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Canceler</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">; </span>onUploadProgress<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> get<wbr>Cancel<wbr>Source<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>cancel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Canceler</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
											<ul class="tsd-parameters">
												<li class="tsd-parameter-signature">
													<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-variable">
														<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>cancel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Canceler</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
													</ul>
													<ul class="tsd-descriptions">
														<li class="tsd-description">
															<h4 class="tsd-parameters-title">Parameters</h4>
															<ul class="tsd-parameters">
																<li>
																	<h5>cancel: <span class="tsd-signature-type">Canceler</span></h5>
																</li>
															</ul>
															<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
														</li>
													</ul>
												</li>
											</ul>
										</li>
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> on<wbr>Upload<wbr>Progress<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
											<ul class="tsd-parameters">
												<li class="tsd-parameter-signature">
													<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-variable">
														<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
													</ul>
													<ul class="tsd-descriptions">
														<li class="tsd-description">
															<h4 class="tsd-parameters-title">Parameters</h4>
															<ul class="tsd-parameters">
																<li>
																	<h5>e: <span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></h5>
																	<ul class="tsd-parameters">
																		<li class="tsd-parameter">
																			<h5>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
																		</li>
																		<li class="tsd-parameter">
																			<h5>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
																		</li>
																		<li class="tsd-parameter">
																			<h5>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
																		</li>
																	</ul>
																</li>
															</ul>
															<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
														</li>
													</ul>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="uploadfileforuniapp" class="tsd-anchor"></a>
					<h3>upload<wbr>File<wbr>For<wbr>Uni<wbr>App</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">upload<wbr>File<wbr>For<wbr>Uni<wbr>App<span class="tsd-signature-symbol">(</span>file<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>base64<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>tempFile<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">ChooseFile</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">ChooseImageSuccessCallbackResultFile</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">ChooseFileSuccessCallbackResultFile</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">File</span>, getTask<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">UploadTask</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>fileName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fullUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/media/index.ts:203</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>file: <span class="tsd-signature-symbol">{ </span>base64<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>tempFile<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">ChooseFile</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">ChooseImageSuccessCallbackResultFile</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">ChooseFileSuccessCallbackResultFile</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">File</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>tempFile可以为base64字符串，无前缀，此时type为必填,base64为true</p>
									</div>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> getTask: <span class="tsd-signature-symbol">(</span>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">UploadTask</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">UploadTask</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>task: <span class="tsd-signature-type">UploadTask</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>fileName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fullUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="uploadfileforwxapp" class="tsd-anchor"></a>
					<h3>upload<wbr>File<wbr>For<wbr>WxApp</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">upload<wbr>File<wbr>For<wbr>WxApp<span class="tsd-signature-symbol">(</span>fileName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, path<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">File</span>, size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, buffer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Uint8Array</span>, getTask<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">UploadTask</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span>, modelName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>fileName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fullUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/media/index.ts:161</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>fileName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>path: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">File</span></h5>
								</li>
								<li>
									<h5>size: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>buffer: <span class="tsd-signature-type">Uint8Array</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> getTask: <span class="tsd-signature-symbol">(</span>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">UploadTask</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">UploadTask</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>task: <span class="tsd-signature-type">UploadTask</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> modelName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>fileName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fullUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="uploadfileothers" class="tsd-anchor"></a>
					<h3>upload<wbr>File<wbr>Others</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">upload<wbr>File<wbr>Others<span class="tsd-signature-symbol">(</span>file<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">File</span>, modelName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>code<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>uri<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/media/index.ts:373</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>file: <span class="tsd-signature-type">File</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> modelName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>code<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>uri<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="uploadfilev2" class="tsd-anchor"></a>
					<h3>upload<wbr>File<wbr>V2</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">upload<wbr>File<wbr>V2<span class="tsd-signature-symbol">(</span>file<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">File</span>, config<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>getCancelSource<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>cancel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Canceler</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">; </span>onUploadProgress<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol"> }</span>, modelName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>code<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fileName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fullUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/media/index.ts:94</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>file: <span class="tsd-signature-type">File</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> config: <span class="tsd-signature-symbol">{ </span>getCancelSource<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>cancel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Canceler</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">; </span>onUploadProgress<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> get<wbr>Cancel<wbr>Source<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>cancel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Canceler</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
											<ul class="tsd-parameters">
												<li class="tsd-parameter-signature">
													<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-variable">
														<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>cancel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Canceler</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
													</ul>
													<ul class="tsd-descriptions">
														<li class="tsd-description">
															<h4 class="tsd-parameters-title">Parameters</h4>
															<ul class="tsd-parameters">
																<li>
																	<h5>cancel: <span class="tsd-signature-type">Canceler</span></h5>
																</li>
															</ul>
															<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
														</li>
													</ul>
												</li>
											</ul>
										</li>
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> on<wbr>Upload<wbr>Progress<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
											<ul class="tsd-parameters">
												<li class="tsd-parameter-signature">
													<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-variable">
														<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
													</ul>
													<ul class="tsd-descriptions">
														<li class="tsd-description">
															<h4 class="tsd-parameters-title">Parameters</h4>
															<ul class="tsd-parameters">
																<li>
																	<h5>e: <span class="tsd-signature-symbol">{ </span>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></h5>
																	<ul class="tsd-parameters">
																		<li class="tsd-parameter">
																			<h5>loaded<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
																		</li>
																		<li class="tsd-parameter">
																			<h5>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
																		</li>
																		<li class="tsd-parameter">
																			<h5>total<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
																		</li>
																	</ul>
																</li>
															</ul>
															<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
														</li>
													</ul>
												</li>
											</ul>
										</li>
									</ul>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> modelName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>code<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fileName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fullUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="wxupload" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> wx<wbr>Upload</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">wx<wbr>Upload<span class="tsd-signature-symbol">(</span>fileName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, file<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">File</span>, md5<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, getTask<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">UploadTask</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span>, modelName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>code<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/media/index.ts:10</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>fileName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>file: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">File</span></h5>
								</li>
								<li>
									<h5>md5: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> getTask: <span class="tsd-signature-symbol">(</span>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">UploadTask</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>task<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">UploadTask</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>task: <span class="tsd-signature-type">UploadTask</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> modelName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>code<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="mediacontroller.html" class="tsd-kind-icon">Media<wbr>Controller</a>
						<ul>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="mediacontroller.html#base64tofile" class="tsd-kind-icon">base64to<wbr>File</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="mediacontroller.html#blobtouint8array" class="tsd-kind-icon">blob<wbr>ToUint8<wbr>Array</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="mediacontroller.html#buildthumbnail" class="tsd-kind-icon">build<wbr>Thumbnail</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="mediacontroller.html#choosefile" class="tsd-kind-icon">choose<wbr>File</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="mediacontroller.html#choosefiles" class="tsd-kind-icon">choose<wbr>Files</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="mediacontroller.html#dataurltoblob" class="tsd-kind-icon">dataURLto<wbr>Blob</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="mediacontroller.html#downloadfile" class="tsd-kind-icon">download<wbr>File</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="mediacontroller.html#getbase64type" class="tsd-kind-icon">get<wbr>Base64<wbr>Type</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="mediacontroller.html#uploadfile" class="tsd-kind-icon">upload<wbr>File</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="mediacontroller.html#uploadfileforuniapp" class="tsd-kind-icon">upload<wbr>File<wbr>For<wbr>Uni<wbr>App</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="mediacontroller.html#uploadfileforwxapp" class="tsd-kind-icon">upload<wbr>File<wbr>For<wbr>WxApp</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="mediacontroller.html#uploadfileothers" class="tsd-kind-icon">upload<wbr>File<wbr>Others</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="mediacontroller.html#uploadfilev2" class="tsd-kind-icon">upload<wbr>File<wbr>V2</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="mediacontroller.html#wxupload" class="tsd-kind-icon">wx<wbr>Upload</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>