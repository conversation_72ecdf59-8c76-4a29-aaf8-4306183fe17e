<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Sse | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="sse.html">Sse</a>
				</li>
			</ul>
			<h1>Class Sse</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">Sse</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="sse.html#bigactionmessagelisteners" class="tsd-kind-icon">big<wbr>Action<wbr>Message<wbr>Listeners</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="sse.html#broadcast" class="tsd-kind-icon">broadcast</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class"><a href="sse.html#connectivity" class="tsd-kind-icon">connectivity</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="sse.html#connectivityobserver" class="tsd-kind-icon">connectivity<wbr>Observer</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="sse.html#eventsource" class="tsd-kind-icon">event<wbr>Source</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="sse.html#lastcheck" class="tsd-kind-icon">last<wbr>Check</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="sse.html#menudatachangedmessagelistener" class="tsd-kind-icon">menu<wbr>Data<wbr>Changed<wbr>Message<wbr>Listener</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="sse.html#notifymessagelistener" class="tsd-kind-icon">notify<wbr>Message<wbr>Listener</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="sse.html#sse-1" class="tsd-kind-icon">sse</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="sse.html#tokeninvalid" class="tsd-kind-icon">token<wbr>Invalid</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="sse.html#transportmessagelisteners" class="tsd-kind-icon">transport<wbr>Message<wbr>Listeners</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="sse.html#usedsid" class="tsd-kind-icon">used<wbr>Sid</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="sse.html#useduid" class="tsd-kind-icon">used<wbr>Uid</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="sse.html#addbigactionmessagelistener" class="tsd-kind-icon">add<wbr>Big<wbr>Action<wbr>Message<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="sse.html#addconnectivityobserver" class="tsd-kind-icon">add<wbr>Connectivity<wbr>Observer</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="sse.html#addssemenudatachangedmessagelistener" class="tsd-kind-icon">add<wbr>Sse<wbr>Menu<wbr>Data<wbr>Changed<wbr>Message<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="sse.html#addssenotifymessagelistener" class="tsd-kind-icon">add<wbr>Sse<wbr>Notify<wbr>Message<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="sse.html#addtransportmessagelistener" class="tsd-kind-icon">add<wbr>Transport<wbr>Message<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="sse.html#broadcastconnectivity" class="tsd-kind-icon">broadcast<wbr>Connectivity</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="sse.html#checkconnectivity" class="tsd-kind-icon">check<wbr>Connectivity</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="sse.html#close" class="tsd-kind-icon">close</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="sse.html#getsessionid" class="tsd-kind-icon">get<wbr>Session<wbr>Id</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="sse.html#getuuid" class="tsd-kind-icon">getUUID</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="sse.html#initbroadcastchannel" class="tsd-kind-icon">init<wbr>Broadcast<wbr>Channel</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="sse.html#initeventsource" class="tsd-kind-icon">init<wbr>Event<wbr>Source</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="sse.html#randomid" class="tsd-kind-icon">random<wbr>Id</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="sse.html#registermenuonbadgechanged" class="tsd-kind-icon">register<wbr>Menu<wbr>OnBadge<wbr>Changed</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="sse.html#registermodels" class="tsd-kind-icon">register<wbr>Models</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="sse.html#unregistermenus" class="tsd-kind-icon">unregister<wbr>Menus</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="sse.html#unregistermodels" class="tsd-kind-icon">unregister<wbr>Models</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="bigactionmessagelisteners" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> big<wbr>Action<wbr>Message<wbr>Listeners</h3>
					<div class="tsd-signature tsd-kind-icon">big<wbr>Action<wbr>Message<wbr>Listeners<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.SSE.bigActionUpdateListener</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/sse/sse.ts:59</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="broadcast" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> broadcast</h3>
					<div class="tsd-signature tsd-kind-icon">broadcast<span class="tsd-signature-symbol">:</span> <a href="broadcast.html" class="tsd-signature-type">Broadcast</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/sse/sse.ts:66</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class">
					<a name="connectivity" class="tsd-anchor"></a>
					<h3>connectivity</h3>
					<div class="tsd-signature tsd-kind-icon">connectivity<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/sse/sse.ts:64</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="connectivityobserver" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> connectivity<wbr>Observer</h3>
					<div class="tsd-signature tsd-kind-icon">connectivity<wbr>Observer<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.SSE.ConnectivityObserver</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/sse/sse.ts:63</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="eventsource" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> event<wbr>Source</h3>
					<div class="tsd-signature tsd-kind-icon">event<wbr>Source<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">EventSourcePolyfill</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/sse/sse.ts:57</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="lastcheck" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> last<wbr>Check</h3>
					<div class="tsd-signature tsd-kind-icon">last<wbr>Check<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = 0</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/sse/sse.ts:70</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="menudatachangedmessagelistener" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> menu<wbr>Data<wbr>Changed<wbr>Message<wbr>Listener</h3>
					<div class="tsd-signature tsd-kind-icon">menu<wbr>Data<wbr>Changed<wbr>Message<wbr>Listener<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.SSE.MenuDataChangedListener</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/sse/sse.ts:61</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="notifymessagelistener" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> notify<wbr>Message<wbr>Listener</h3>
					<div class="tsd-signature tsd-kind-icon">notify<wbr>Message<wbr>Listener<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.SSE.notifyListener</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/sse/sse.ts:60</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="sse-1" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagReadonly">Readonly</span> sse</h3>
					<div class="tsd-signature tsd-kind-icon">sse<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = global.sse</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/sse/chat-sse.ts:6</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="tokeninvalid" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> token<wbr>Invalid</h3>
					<div class="tsd-signature tsd-kind-icon">token<wbr>Invalid<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/sse/sse.ts:56</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="transportmessagelisteners" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> transport<wbr>Message<wbr>Listeners</h3>
					<div class="tsd-signature tsd-kind-icon">transport<wbr>Message<wbr>Listeners<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.SSE.transportMessageListener</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/sse/sse.ts:58</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="usedsid" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> used<wbr>Sid</h3>
					<div class="tsd-signature tsd-kind-icon">used<wbr>Sid<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &quot;&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/sse/sse.ts:69</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="useduid" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> used<wbr>Uid</h3>
					<div class="tsd-signature tsd-kind-icon">used<wbr>Uid<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &quot;&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/core/sse/sse.ts:68</li>
							<li>Defined in src/core/sse/chat-sse.ts:5</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addbigactionmessagelistener" class="tsd-anchor"></a>
					<h3>add<wbr>Big<wbr>Action<wbr>Message<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Big<wbr>Action<wbr>Message<wbr>Listener<span class="tsd-signature-symbol">(</span>callback<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.bigActionUpdateListener</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.removeSSEListener</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/sse.ts:215</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>callback: <span class="tsd-signature-type">dto.SSE.bigActionUpdateListener</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">dto.SSE.removeSSEListener</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addconnectivityobserver" class="tsd-anchor"></a>
					<h3>add<wbr>Connectivity<wbr>Observer</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Connectivity<wbr>Observer<span class="tsd-signature-symbol">(</span>callback<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.ConnectivityObserver</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">(Anonymous function)</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/sse.ts:270</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>callback: <span class="tsd-signature-type">dto.SSE.ConnectivityObserver</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">(Anonymous function)</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addssemenudatachangedmessagelistener" class="tsd-anchor"></a>
					<h3>add<wbr>Sse<wbr>Menu<wbr>Data<wbr>Changed<wbr>Message<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Sse<wbr>Menu<wbr>Data<wbr>Changed<wbr>Message<wbr>Listener<span class="tsd-signature-symbol">(</span>callback<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.MenuDataChangedListener</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.removeSSEListener</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/sse.ts:256</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>callback: <span class="tsd-signature-type">dto.SSE.MenuDataChangedListener</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">dto.SSE.removeSSEListener</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addssenotifymessagelistener" class="tsd-anchor"></a>
					<h3>add<wbr>Sse<wbr>Notify<wbr>Message<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Sse<wbr>Notify<wbr>Message<wbr>Listener<span class="tsd-signature-symbol">(</span>callback<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.notifyListener</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.removeSSEListener</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/sse.ts:243</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>callback: <span class="tsd-signature-type">dto.SSE.notifyListener</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">dto.SSE.removeSSEListener</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addtransportmessagelistener" class="tsd-anchor"></a>
					<h3>add<wbr>Transport<wbr>Message<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Transport<wbr>Message<wbr>Listener<span class="tsd-signature-symbol">(</span>callback<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.transportMessageListener</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.removeSSEListener</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/sse.ts:229</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>callback: <span class="tsd-signature-type">dto.SSE.transportMessageListener</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">dto.SSE.removeSSEListener</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="broadcastconnectivity" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> broadcast<wbr>Connectivity</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">broadcast<wbr>Connectivity<span class="tsd-signature-symbol">(</span>status<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/sse.ts:76</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>status: <span class="tsd-signature-type">boolean</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="checkconnectivity" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> check<wbr>Connectivity</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">check<wbr>Connectivity<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/sse.ts:103</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="close" class="tsd-anchor"></a>
					<h3>close</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">close<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/sse.ts:207</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getsessionid" class="tsd-anchor"></a>
					<h3>get<wbr>Session<wbr>Id</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Session<wbr>Id<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/sse.ts:96</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getuuid" class="tsd-anchor"></a>
					<h3>getUUID</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">getUUID<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/sse.ts:89</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="initbroadcastchannel" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr>Broadcast<wbr>Channel</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">init<wbr>Broadcast<wbr>Channel<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/sse.ts:180</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="initeventsource" class="tsd-anchor"></a>
					<h3>init<wbr>Event<wbr>Source</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">init<wbr>Event<wbr>Source<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/sse.ts:115</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="randomid" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> random<wbr>Id</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">random<wbr>Id<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/sse.ts:72</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="registermenuonbadgechanged" class="tsd-anchor"></a>
					<h3>register<wbr>Menu<wbr>OnBadge<wbr>Changed</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">register<wbr>Menu<wbr>OnBadge<wbr>Changed&lt;T&gt;<span class="tsd-signature-symbol">(</span>models<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span>, project<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">"entrance"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"communication"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"todo"</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/sse.ts:288</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>models: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
								<li>
									<h5>project: <span class="tsd-signature-type">"entrance"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"communication"</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">"todo"</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="registermodels" class="tsd-anchor"></a>
					<h3>register<wbr>Models</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">register<wbr>Models&lt;T&gt;<span class="tsd-signature-symbol">(</span>models<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/sse.ts:275</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>models: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="unregistermenus" class="tsd-anchor"></a>
					<h3>unregister<wbr>Menus</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">unregister<wbr>Menus&lt;T&gt;<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/chat-sse.ts:51</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="unregistermodels" class="tsd-anchor"></a>
					<h3>unregister<wbr>Models</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">unregister<wbr>Models&lt;T&gt;<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/core/sse/chat-sse.ts:32</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="sse.html" class="tsd-kind-icon">Sse</a>
						<ul>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="sse.html#bigactionmessagelisteners" class="tsd-kind-icon">big<wbr>Action<wbr>Message<wbr>Listeners</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="sse.html#broadcast" class="tsd-kind-icon">broadcast</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class">
								<a href="sse.html#connectivity" class="tsd-kind-icon">connectivity</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="sse.html#connectivityobserver" class="tsd-kind-icon">connectivity<wbr>Observer</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="sse.html#eventsource" class="tsd-kind-icon">event<wbr>Source</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="sse.html#lastcheck" class="tsd-kind-icon">last<wbr>Check</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="sse.html#menudatachangedmessagelistener" class="tsd-kind-icon">menu<wbr>Data<wbr>Changed<wbr>Message<wbr>Listener</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="sse.html#notifymessagelistener" class="tsd-kind-icon">notify<wbr>Message<wbr>Listener</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="sse.html#sse-1" class="tsd-kind-icon">sse</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="sse.html#tokeninvalid" class="tsd-kind-icon">token<wbr>Invalid</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="sse.html#transportmessagelisteners" class="tsd-kind-icon">transport<wbr>Message<wbr>Listeners</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="sse.html#usedsid" class="tsd-kind-icon">used<wbr>Sid</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="sse.html#useduid" class="tsd-kind-icon">used<wbr>Uid</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="sse.html#addbigactionmessagelistener" class="tsd-kind-icon">add<wbr>Big<wbr>Action<wbr>Message<wbr>Listener</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="sse.html#addconnectivityobserver" class="tsd-kind-icon">add<wbr>Connectivity<wbr>Observer</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="sse.html#addssemenudatachangedmessagelistener" class="tsd-kind-icon">add<wbr>Sse<wbr>Menu<wbr>Data<wbr>Changed<wbr>Message<wbr>Listener</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="sse.html#addssenotifymessagelistener" class="tsd-kind-icon">add<wbr>Sse<wbr>Notify<wbr>Message<wbr>Listener</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="sse.html#addtransportmessagelistener" class="tsd-kind-icon">add<wbr>Transport<wbr>Message<wbr>Listener</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="sse.html#broadcastconnectivity" class="tsd-kind-icon">broadcast<wbr>Connectivity</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="sse.html#checkconnectivity" class="tsd-kind-icon">check<wbr>Connectivity</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="sse.html#close" class="tsd-kind-icon">close</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="sse.html#getsessionid" class="tsd-kind-icon">get<wbr>Session<wbr>Id</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="sse.html#getuuid" class="tsd-kind-icon">getUUID</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="sse.html#initbroadcastchannel" class="tsd-kind-icon">init<wbr>Broadcast<wbr>Channel</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="sse.html#initeventsource" class="tsd-kind-icon">init<wbr>Event<wbr>Source</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="sse.html#randomid" class="tsd-kind-icon">random<wbr>Id</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="sse.html#registermenuonbadgechanged" class="tsd-kind-icon">register<wbr>Menu<wbr>OnBadge<wbr>Changed</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="sse.html#registermodels" class="tsd-kind-icon">register<wbr>Models</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="sse.html#unregistermenus" class="tsd-kind-icon">unregister<wbr>Menus</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="sse.html#unregistermodels" class="tsd-kind-icon">unregister<wbr>Models</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>