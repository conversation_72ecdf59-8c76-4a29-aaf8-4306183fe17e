<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>DateGroupTypeEnum | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="dategrouptypeenum.html">DateGroupTypeEnum</a>
				</li>
			</ul>
			<h1>Enumeration DateGroupTypeEnum</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Enumeration members</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="dategrouptypeenum.html#day" class="tsd-kind-icon">Day</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="dategrouptypeenum.html#day2" class="tsd-kind-icon">Day2</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="dategrouptypeenum.html#month" class="tsd-kind-icon">Month</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="dategrouptypeenum.html#month2" class="tsd-kind-icon">Month2</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="dategrouptypeenum.html#week" class="tsd-kind-icon">Week</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="dategrouptypeenum.html#year" class="tsd-kind-icon">Year</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Enumeration members</h2>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="day" class="tsd-anchor"></a>
					<h3>Day</h3>
					<div class="tsd-signature tsd-kind-icon">Day<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;day&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/CubeModel.ts:61</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="day2" class="tsd-anchor"></a>
					<h3>Day2</h3>
					<div class="tsd-signature tsd-kind-icon">Day2<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;day2&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/CubeModel.ts:62</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="month" class="tsd-anchor"></a>
					<h3>Month</h3>
					<div class="tsd-signature tsd-kind-icon">Month<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;month&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/CubeModel.ts:64</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="month2" class="tsd-anchor"></a>
					<h3>Month2</h3>
					<div class="tsd-signature tsd-kind-icon">Month2<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;month2&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/CubeModel.ts:65</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="week" class="tsd-anchor"></a>
					<h3>Week</h3>
					<div class="tsd-signature tsd-kind-icon">Week<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;week&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/CubeModel.ts:63</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="year" class="tsd-anchor"></a>
					<h3>Year</h3>
					<div class="tsd-signature tsd-kind-icon">Year<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;year&quot;</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/CubeModel.ts:66</li>
						</ul>
					</aside>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-enum">
						<a href="dategrouptypeenum.html" class="tsd-kind-icon">Date<wbr>Group<wbr>Type<wbr>Enum</a>
						<ul>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="dategrouptypeenum.html#day" class="tsd-kind-icon">Day</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="dategrouptypeenum.html#day2" class="tsd-kind-icon">Day2</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="dategrouptypeenum.html#month" class="tsd-kind-icon">Month</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="dategrouptypeenum.html#month2" class="tsd-kind-icon">Month2</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="dategrouptypeenum.html#week" class="tsd-kind-icon">Week</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="dategrouptypeenum.html#year" class="tsd-kind-icon">Year</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>