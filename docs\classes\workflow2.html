<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Workflow2 | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="workflow2.html">Workflow2</a>
				</li>
			</ul>
			<h1>Class Workflow2</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">Workflow2</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="workflow2.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section tsd-is-private tsd-is-private-protected">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="workflow2.html#api" class="tsd-kind-icon">api</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="workflow2.html#metamodelname" class="tsd-kind-icon">meta<wbr>Model<wbr>Name</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="workflow2.html#modelname" class="tsd-kind-icon">model<wbr>Name</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="workflow2.html#operationtypearray" class="tsd-kind-icon">operation<wbr>Type<wbr>Array</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#addremark" class="tsd-kind-icon">add<wbr>Remark</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="workflow2.html#callbackonchange" class="tsd-kind-icon">callback<wbr>OnChange</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#canceltask" class="tsd-kind-icon">cancel<wbr>Task</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#canceltaskbatch" class="tsd-kind-icon">cancel<wbr>Task<wbr>Batch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#changemaster" class="tsd-kind-icon">change<wbr>Master</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#changeprocessstate" class="tsd-kind-icon">change<wbr>Process<wbr>State</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#changeprocessstatebatch" class="tsd-kind-icon">change<wbr>Process<wbr>State<wbr>Batch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#changetask" class="tsd-kind-icon">change<wbr>Task</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#changetaskbatch" class="tsd-kind-icon">change<wbr>Task<wbr>Batch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#checkstateeditable" class="tsd-kind-icon">check<wbr>State<wbr>Editable</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#checktaskeditable" class="tsd-kind-icon">check<wbr>Task<wbr>Editable</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#createprocessdef" class="tsd-kind-icon">create<wbr>Process<wbr>Def</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#createprocessdef2" class="tsd-kind-icon">create<wbr>Process<wbr>Def2</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#createtask" class="tsd-kind-icon">create<wbr>Task</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#createtaskbatch" class="tsd-kind-icon">create<wbr>Task<wbr>Batch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#datasoucelist" class="tsd-kind-icon">datasouce<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#detail" class="tsd-kind-icon">detail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#detail2" class="tsd-kind-icon">detail2</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#editprocessdef" class="tsd-kind-icon">edit<wbr>Process<wbr>Def</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#edittask" class="tsd-kind-icon">edit<wbr>Task</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#edittaskbatch" class="tsd-kind-icon">edit<wbr>Task<wbr>Batch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#exporttoexcel" class="tsd-kind-icon">export<wbr>ToExcel</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#finishtask" class="tsd-kind-icon">finish<wbr>Task</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#finishtaskbatch" class="tsd-kind-icon">finish<wbr>Task<wbr>Batch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#getalloperationtype" class="tsd-kind-icon">get<wbr>All<wbr>Operation<wbr>Type</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="workflow2.html#getassociateid" class="tsd-kind-icon">get<wbr>Associate<wbr>Id</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#getdefaulttemplateurl" class="tsd-kind-icon">get<wbr>Default<wbr>Template<wbr>Url</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#getprocessinfo" class="tsd-kind-icon">get<wbr>Process<wbr>Info</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="workflow2.html#getstr" class="tsd-kind-icon">get<wbr>Str</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#gettaskdefaultdealer" class="tsd-kind-icon">get<wbr>Task<wbr>Default<wbr>Dealer</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#homecount" class="tsd-kind-icon">home<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#homemasterlistcount" class="tsd-kind-icon">home<wbr>Master<wbr>List<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#hometodolistcount" class="tsd-kind-icon">home<wbr>Todo<wbr>List<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#listallprocess" class="tsd-kind-icon">list<wbr>All<wbr>Process</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#listallprocessname" class="tsd-kind-icon">list<wbr>All<wbr>Process<wbr>Name</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#listallprocessnamewithright" class="tsd-kind-icon">list<wbr>All<wbr>Process<wbr>Name<wbr>With<wbr>Right</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#listmodelactions" class="tsd-kind-icon">list<wbr>Model<wbr>Actions</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#listoperation" class="tsd-kind-icon">list<wbr>Operation</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#listprocessinstance" class="tsd-kind-icon">list<wbr>Process<wbr>Instance</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#listprocessinstancepanel" class="tsd-kind-icon">list<wbr>Process<wbr>Instance<wbr>Panel</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#listremark" class="tsd-kind-icon">list<wbr>Remark</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#listtaskrecord" class="tsd-kind-icon">list<wbr>Task<wbr>Record</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="workflow2.html#ontransportmessage" class="tsd-kind-icon">on<wbr>Transport<wbr>Message</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#processdefdetail" class="tsd-kind-icon">process<wbr>Def<wbr>Detail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="workflow2.html#querydealerlist" class="tsd-kind-icon">query<wbr>Dealer<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#queryprocessbyassociateid" class="tsd-kind-icon">query<wbr>Process<wbr>ByAssociate<wbr>Id</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#queryprocessexistcount" class="tsd-kind-icon">query<wbr>Process<wbr>Exist<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#queryprocesswithcount" class="tsd-kind-icon">query<wbr>Process<wbr>With<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#queryworkflowdealerlist" class="tsd-kind-icon">query<wbr>Workflow<wbr>Dealer<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#queryworkflowdealerlist2" class="tsd-kind-icon">query<wbr>Workflow<wbr>Dealer<wbr>List2</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#registeronchange" class="tsd-kind-icon">register<wbr>OnChange</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#resolveoperationcontent" class="tsd-kind-icon">resolve<wbr>Operation<wbr>Content</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#resolveoperationtype" class="tsd-kind-icon">resolve<wbr>Operation<wbr>Type</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#rollbackprocess" class="tsd-kind-icon">rollback<wbr>Process</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#saveprocessdef2" class="tsd-kind-icon">save<wbr>Process<wbr>Def2</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#startprocess" class="tsd-kind-icon">start<wbr>Process</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#startprocessbatch" class="tsd-kind-icon">start<wbr>Process<wbr>Batch</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#startprocesscheck" class="tsd-kind-icon">start<wbr>Process<wbr>Check</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#updateonline" class="tsd-kind-icon">update<wbr>Online</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#updateprocessdesc" class="tsd-kind-icon">update<wbr>Process<wbr>Desc</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#updatestatefilterparam" class="tsd-kind-icon">update<wbr>State<wbr>Filter<wbr>Param</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#updatetaskfilterparam" class="tsd-kind-icon">update<wbr>Task<wbr>Filter<wbr>Param</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="workflow2.html#workflowbatchrequest" class="tsd-kind-icon">workflow<wbr>Batch<wbr>Request</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new <wbr>Workflow2<span class="tsd-signature-symbol">(</span>modelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="workflow2.html" class="tsd-signature-type">Workflow2</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:60</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>modelName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="workflow2.html" class="tsd-signature-type">Workflow2</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="api" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> api</h3>
					<div class="tsd-signature tsd-kind-icon">api<span class="tsd-signature-symbol">:</span> <a href="workflow2api.html" class="tsd-signature-type">Workflow2Api</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:37</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="metamodelname" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> meta<wbr>Model<wbr>Name</h3>
					<div class="tsd-signature tsd-kind-icon">meta<wbr>Model<wbr>Name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:41</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="modelname" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> model<wbr>Name</h3>
					<div class="tsd-signature tsd-kind-icon">model<wbr>Name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:39</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="operationtypearray" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> operation<wbr>Type<wbr>Array</h3>
					<div class="tsd-signature tsd-kind-icon">operation<wbr>Type<wbr>Array<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = [&quot;新建流程&quot;,&quot;变更状态&quot;,&quot;创建任务&quot;,&quot;完成任务&quot;,&quot;取消任务&quot;,&quot;编辑任务&quot;,&quot;更换任务&quot;,&quot;添加备注&quot;,&quot;撤回任务&quot;,&quot;撤回状态&quot;,&quot;更换负责人&quot;,&quot;更换处理人&quot;,&quot;更换任务时间&quot;,&quot;重启流程&quot;,]</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/workflow2/workflow2.ts:45</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addremark" class="tsd-anchor"></a>
					<h3>add<wbr>Remark</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Remark<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.AddRemarkParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:279</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>添加评论</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.AddRemarkParam</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="callbackonchange" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> callback<wbr>OnChange</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">callback<wbr>OnChange<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:43</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="canceltask" class="tsd-anchor"></a>
					<h3>cancel<wbr>Task</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">cancel<wbr>Task<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.CancelTaskParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:487</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>取消任务</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.CancelTaskParam</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>参数</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="canceltaskbatch" class="tsd-anchor"></a>
					<h3>cancel<wbr>Task<wbr>Batch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">cancel<wbr>Task<wbr>Batch<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.CancelTaskParam</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.BatchResult</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:330</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>取消任务 批量</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.CancelTaskParam</span><span class="tsd-signature-symbol">&gt;</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>参数</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.BatchResult</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="changemaster" class="tsd-anchor"></a>
					<h3>change<wbr>Master</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">change<wbr>Master<span class="tsd-signature-symbol">(</span>request<span class="tsd-signature-symbol">: </span><a href="../globals.html#changemasterrequest" class="tsd-signature-type">ChangeMasterRequest</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:87</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>request: <a href="../globals.html#changemasterrequest" class="tsd-signature-type">ChangeMasterRequest</a></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="changeprocessstate" class="tsd-anchor"></a>
					<h3>change<wbr>Process<wbr>State</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">change<wbr>Process<wbr>State<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.ChangeProcessStateParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:451</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>更换流程状态</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.ChangeProcessStateParam</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>参数</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="changeprocessstatebatch" class="tsd-anchor"></a>
					<h3>change<wbr>Process<wbr>State<wbr>Batch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">change<wbr>Process<wbr>State<wbr>Batch<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ChangeProcessStateParam</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.BatchResult</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:294</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>更换流程状态批量</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ChangeProcessStateParam</span><span class="tsd-signature-symbol">&gt;</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>参数</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.BatchResult</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="changetask" class="tsd-anchor"></a>
					<h3>change<wbr>Task</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">change<wbr>Task<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.ChangeTaskParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:478</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>更换任务</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.ChangeTaskParam</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>参数</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="changetaskbatch" class="tsd-anchor"></a>
					<h3>change<wbr>Task<wbr>Batch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">change<wbr>Task<wbr>Batch<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ChangeTaskParam</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.BatchResult</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:321</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>更换任务 批量</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ChangeTaskParam</span><span class="tsd-signature-symbol">&gt;</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>参数</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.BatchResult</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="checkstateeditable" class="tsd-anchor"></a>
					<h3>check<wbr>State<wbr>Editable</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">check<wbr>State<wbr>Editable<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><a href="../globals.html#checkeditparam" class="tsd-signature-type">CheckEditParam</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:167</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <a href="../globals.html#checkeditparam" class="tsd-signature-type">CheckEditParam</a></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="checktaskeditable" class="tsd-anchor"></a>
					<h3>check<wbr>Task<wbr>Editable</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">check<wbr>Task<wbr>Editable<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><a href="../globals.html#checkeditparam" class="tsd-signature-type">CheckEditParam</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:171</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <a href="../globals.html#checkeditparam" class="tsd-signature-type">CheckEditParam</a></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="createprocessdef" class="tsd-anchor"></a>
					<h3>create<wbr>Process<wbr>Def</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">create<wbr>Process<wbr>Def<span class="tsd-signature-symbol">(</span>processDef<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.ProcessEditParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:505</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>新增流程</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processDef: <span class="tsd-signature-type">dto.workflow2.ProcessEditParam</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>流程定义</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="createprocessdef2" class="tsd-anchor"></a>
					<h3>create<wbr>Process<wbr>Def2</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">create<wbr>Process<wbr>Def2<span class="tsd-signature-symbol">(</span>request<span class="tsd-signature-symbol">: </span><a href="../globals.html#createprocessrequest" class="tsd-signature-type">CreateProcessRequest</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:126</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>创建流程图</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>request: <a href="../globals.html#createprocessrequest" class="tsd-signature-type">CreateProcessRequest</a></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="createtask" class="tsd-anchor"></a>
					<h3>create<wbr>Task</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">create<wbr>Task<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.UpdateTaskParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:460</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>新建任务</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.UpdateTaskParam</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>参数</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="createtaskbatch" class="tsd-anchor"></a>
					<h3>create<wbr>Task<wbr>Batch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">create<wbr>Task<wbr>Batch<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.UpdateTaskParam</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.BatchResult</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:303</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>新建任务批量</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.UpdateTaskParam</span><span class="tsd-signature-symbol">&gt;</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>参数</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.BatchResult</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="datasoucelist" class="tsd-anchor"></a>
					<h3>datasouce<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">datasouce<wbr>List<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#datasourcewithcount" class="tsd-signature-type">DatasourceWithCount</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:528</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#datasourcewithcount" class="tsd-signature-type">DatasourceWithCount</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="detail" class="tsd-anchor"></a>
					<h3>detail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">detail<span class="tsd-signature-symbol">(</span>processId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, detailName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ProcessDetail</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:416</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>流程实例详情信息</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processId: <span class="tsd-signature-type">number</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>id</p>
									</div>
								</li>
								<li>
									<h5>detailName: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>详情的名称</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ProcessDetail</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="detail2" class="tsd-anchor"></a>
					<h3>detail2</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">detail2<span class="tsd-signature-symbol">(</span>processId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, detailName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#processdetail2" class="tsd-signature-type">ProcessDetail2</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:112</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>新详情接口</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processId: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>detailName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#processdetail2" class="tsd-signature-type">ProcessDetail2</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="editprocessdef" class="tsd-anchor"></a>
					<h3>edit<wbr>Process<wbr>Def</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">edit<wbr>Process<wbr>Def<span class="tsd-signature-symbol">(</span>processDef<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.ProcessEditParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:514</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>编辑流程</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processDef: <span class="tsd-signature-type">dto.workflow2.ProcessEditParam</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>流程定义</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="edittask" class="tsd-anchor"></a>
					<h3>edit<wbr>Task</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">edit<wbr>Task<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.UpdateTaskParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:469</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>编辑任务</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.UpdateTaskParam</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>参数</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="edittaskbatch" class="tsd-anchor"></a>
					<h3>edit<wbr>Task<wbr>Batch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">edit<wbr>Task<wbr>Batch<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.UpdateTaskParam</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.BatchResult</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:312</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>编辑任务 批量</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.UpdateTaskParam</span><span class="tsd-signature-symbol">&gt;</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>参数</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.BatchResult</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="exporttoexcel" class="tsd-anchor"></a>
					<h3>export<wbr>ToExcel</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">export<wbr>ToExcel<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.exportExcelParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">(Anonymous function)</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:536</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.workflow2.exportExcelParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">(Anonymous function)</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="finishtask" class="tsd-anchor"></a>
					<h3>finish<wbr>Task</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">finish<wbr>Task<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.FinishTaskParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:496</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>完成任务</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.FinishTaskParam</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>参数</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="finishtaskbatch" class="tsd-anchor"></a>
					<h3>finish<wbr>Task<wbr>Batch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">finish<wbr>Task<wbr>Batch<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.FinishTaskParam</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.BatchResult</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:339</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>完成任务 批量</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.FinishTaskParam</span><span class="tsd-signature-symbol">&gt;</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>参数</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.BatchResult</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getalloperationtype" class="tsd-anchor"></a>
					<h3>get<wbr>All<wbr>Operation<wbr>Type</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>All<wbr>Operation<wbr>Type<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:249</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>列出所有的操作类型</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="getassociateid" class="tsd-anchor"></a>
					<h3>get<wbr>Associate<wbr>Id</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Associate<wbr>Id&lt;T&gt;<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.ListQueryParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:271</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.ListQueryParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getdefaulttemplateurl" class="tsd-anchor"></a>
					<h3>get<wbr>Default<wbr>Template<wbr>Url</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Default<wbr>Template<wbr>Url<span class="tsd-signature-symbol">(</span>list_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, page_name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:540</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>list_name: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> page_name: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getprocessinfo" class="tsd-anchor"></a>
					<h3>get<wbr>Process<wbr>Info</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Process<wbr>Info<span class="tsd-signature-symbol">(</span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ProcessInfo</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:437</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取流程信息</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>流程名称</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ProcessInfo</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="getstr" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> get<wbr>Str</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Str<span class="tsd-signature-symbol">(</span>content<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:240</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>content: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="gettaskdefaultdealer" class="tsd-anchor"></a>
					<h3>get<wbr>Task<wbr>Default<wbr>Dealer</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Task<wbr>Default<wbr>Dealer<span class="tsd-signature-symbol">(</span>request<span class="tsd-signature-symbol">: </span><a href="../globals.html#taskdefaultdealerrequest" class="tsd-signature-type">TaskDefaultDealerRequest</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#workflowuser" class="tsd-signature-type">WorkflowUser</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:83</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取任务的默认处理人</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>request: <a href="../globals.html#taskdefaultdealerrequest" class="tsd-signature-type">TaskDefaultDealerRequest</a></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#workflowuser" class="tsd-signature-type">WorkflowUser</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="homecount" class="tsd-anchor"></a>
					<h3>home<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">home<wbr>Count<span class="tsd-signature-symbol">(</span>datasource<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#homeworkflowcount" class="tsd-signature-type">HomeWorkflowCount</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:532</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>datasource: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#homeworkflowcount" class="tsd-signature-type">HomeWorkflowCount</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="homemasterlistcount" class="tsd-anchor"></a>
					<h3>home<wbr>Master<wbr>List<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">home<wbr>Master<wbr>List<wbr>Count<span class="tsd-signature-symbol">(</span>datasource<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:524</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>datasource: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="hometodolistcount" class="tsd-anchor"></a>
					<h3>home<wbr>Todo<wbr>List<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">home<wbr>Todo<wbr>List<wbr>Count<span class="tsd-signature-symbol">(</span>datasource<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:520</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>datasource: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listallprocess" class="tsd-anchor"></a>
					<h3>list<wbr>All<wbr>Process</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>All<wbr>Process<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ProcessInfo</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:255</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>列出所有的流程</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ProcessInfo</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listallprocessname" class="tsd-anchor"></a>
					<h3>list<wbr>All<wbr>Process<wbr>Name</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>All<wbr>Process<wbr>Name<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:193</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>列出所有流程的名称</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listallprocessnamewithright" class="tsd-anchor"></a>
					<h3>list<wbr>All<wbr>Process<wbr>Name<wbr>With<wbr>Right</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>All<wbr>Process<wbr>Name<wbr>With<wbr>Right<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:199</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>列出所有流程的名称</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listmodelactions" class="tsd-anchor"></a>
					<h3>list<wbr>Model<wbr>Actions</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>Model<wbr>Actions<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ActionInfo</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:187</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>列出模型所有的 actions</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ActionInfo</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listoperation" class="tsd-anchor"></a>
					<h3>list<wbr>Operation</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>Operation<span class="tsd-signature-symbol">(</span>processId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.listOperationParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ProcessOperation</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:405</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>流程实例的所有操作记录</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processId: <span class="tsd-signature-type">number</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>id</p>
									</div>
								</li>
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.listOperationParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ProcessOperation</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listprocessinstance" class="tsd-anchor"></a>
					<h3>list<wbr>Process<wbr>Instance</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>Process<wbr>Instance<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.ListQueryParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ListResult</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:426</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>查询所有流程实例</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.ListQueryParam</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>参数</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ListResult</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listprocessinstancepanel" class="tsd-anchor"></a>
					<h3>list<wbr>Process<wbr>Instance<wbr>Panel</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>Process<wbr>Instance<wbr>Panel<span class="tsd-signature-symbol">(</span>associateId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ProcessInstancePanel</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:179</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>associateId: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ProcessInstancePanel</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listremark" class="tsd-anchor"></a>
					<h3>list<wbr>Remark</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>Remark<span class="tsd-signature-symbol">(</span>processId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.Remark</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:387</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>流程实例的所有评论</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processId: <span class="tsd-signature-type">number</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.Remark</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="listtaskrecord" class="tsd-anchor"></a>
					<h3>list<wbr>Task<wbr>Record</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">list<wbr>Task<wbr>Record<span class="tsd-signature-symbol">(</span>processId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.WorkRecord</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:396</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>流程实例的所有工作记录</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processId: <span class="tsd-signature-type">number</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>id</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.WorkRecord</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="ontransportmessage" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> on<wbr>Transport<wbr>Message</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">on<wbr>Transport<wbr>Message<span class="tsd-signature-symbol">(</span>msg<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.msg</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:91</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>msg: <span class="tsd-signature-type">dto.SSE.msg</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="processdefdetail" class="tsd-anchor"></a>
					<h3>process<wbr>Def<wbr>Detail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">process<wbr>Def<wbr>Detail<span class="tsd-signature-symbol">(</span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#processconfiginfo" class="tsd-signature-type">ProcessConfigInfo</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:133</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>流程定义信息</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#processconfiginfo" class="tsd-signature-type">ProcessConfigInfo</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="querydealerlist" class="tsd-anchor"></a>
					<h3>query<wbr>Dealer<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">query<wbr>Dealer<wbr>List&lt;T&gt;<span class="tsd-signature-symbol">(</span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, state<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:287</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>查询处理人列表</p>
								</div>
							</div>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>state: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="queryprocessbyassociateid" class="tsd-anchor"></a>
					<h3>query<wbr>Process<wbr>ByAssociate<wbr>Id</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<wbr>Process<wbr>ByAssociate<wbr>Id<span class="tsd-signature-symbol">(</span>associateId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#associateprocessinfo" class="tsd-signature-type">AssociateProcessInfo</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:155</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>associateId: <span class="tsd-signature-type">any</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/workflow2.html#associateprocessinfo" class="tsd-signature-type">AssociateProcessInfo</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="queryprocessexistcount" class="tsd-anchor"></a>
					<h3>query<wbr>Process<wbr>Exist<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<wbr>Process<wbr>Exist<wbr>Count<span class="tsd-signature-symbol">(</span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:369</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>查询存在流程（运行中）的数量</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>流程名称</p>
									</div>
								</li>
								<li>
									<h5>ids: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>关联id数组，逗号分隔</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="queryprocesswithcount" class="tsd-anchor"></a>
					<h3>query<wbr>Process<wbr>With<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<wbr>Process<wbr>With<wbr>Count<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.ListQueryParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ProcessWithCount</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:378</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>查询所有流程，携带统计信息</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.ListQueryParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.ProcessWithCount</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="queryworkflowdealerlist" class="tsd-anchor"></a>
					<h3>query<wbr>Workflow<wbr>Dealer<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<wbr>Workflow<wbr>Dealer<wbr>List<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#workflowuser" class="tsd-signature-type">WorkflowUser</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:105</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>查询当前处理人的接口</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>供搜索用，目前暂时可以不用传递，传 null 即可</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#workflowuser" class="tsd-signature-type">WorkflowUser</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="queryworkflowdealerlist2" class="tsd-anchor"></a>
					<h3>query<wbr>Workflow<wbr>Dealer<wbr>List2</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<wbr>Workflow<wbr>Dealer<wbr>List2<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#workflowuser" class="tsd-signature-type">WorkflowUser</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:76</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#workflowuser" class="tsd-signature-type">WorkflowUser</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="registeronchange" class="tsd-anchor"></a>
					<h3>register<wbr>OnChange</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">register<wbr>OnChange<span class="tsd-signature-symbol">(</span>cb<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.SSE.callBackOfModelUpdated</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:150</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>注册当前模型的数据变化时的回掉</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>cb: <span class="tsd-signature-type">dto.SSE.callBackOfModelUpdated</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/sse.html#removesselistener" class="tsd-signature-type">removeSSEListener</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="resolveoperationcontent" class="tsd-anchor"></a>
					<h3>resolve<wbr>Operation<wbr>Content</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">resolve<wbr>Operation<wbr>Content<span class="tsd-signature-symbol">(</span>operation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.ProcessOperation</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:206</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>解析操作内容</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>operation: <span class="tsd-signature-type">dto.workflow2.ProcessOperation</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>操作内容</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="resolveoperationtype" class="tsd-anchor"></a>
					<h3>resolve<wbr>Operation<wbr>Type</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">resolve<wbr>Operation<wbr>Type<span class="tsd-signature-symbol">(</span>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:262</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>获取数字对应的操作类型</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>index: <span class="tsd-signature-type">number</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>操作类型的数字类型</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="rollbackprocess" class="tsd-anchor"></a>
					<h3>rollback<wbr>Process</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">rollback<wbr>Process<span class="tsd-signature-symbol">(</span>processId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:142</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>请求撤回操作</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processId: <span class="tsd-signature-type">number</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>流程ID</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>true 成功</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="saveprocessdef2" class="tsd-anchor"></a>
					<h3>save<wbr>Process<wbr>Def2</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">save<wbr>Process<wbr>Def2<span class="tsd-signature-symbol">(</span>request<span class="tsd-signature-symbol">: </span><a href="../globals.html#editprocessrequest" class="tsd-signature-type">EditProcessRequest</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:119</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>保存流程图</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>request: <a href="../globals.html#editprocessrequest" class="tsd-signature-type">EditProcessRequest</a></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="startprocess" class="tsd-anchor"></a>
					<h3>start<wbr>Process</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">start<wbr>Process<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.StartProcessParam</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:442</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.StartProcessParam</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="startprocessbatch" class="tsd-anchor"></a>
					<h3>start<wbr>Process<wbr>Batch</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">start<wbr>Process<wbr>Batch<span class="tsd-signature-symbol">(</span>param<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.StartProcessParam</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.BatchResult</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:348</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>批量启动工作流</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>param: <span class="tsd-signature-type">dto.workflow2.BatchParam</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.StartProcessParam</span><span class="tsd-signature-symbol">&gt;</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>批量参数</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.BatchResult</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="startprocesscheck" class="tsd-anchor"></a>
					<h3>start<wbr>Process<wbr>Check</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">start<wbr>Process<wbr>Check<span class="tsd-signature-symbol">(</span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, ids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.StartCheckResult</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:358</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>批量启动流程前的检查操作</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>流程名称</p>
									</div>
								</li>
								<li>
									<h5>ids: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>关联id数组，逗号分隔</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">dto.workflow2.StartCheckResult</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updateonline" class="tsd-anchor"></a>
					<h3>update<wbr>Online</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Online<span class="tsd-signature-symbol">(</span>online<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span>, processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:163</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>online: <span class="tsd-signature-type">boolean</span></h5>
								</li>
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updateprocessdesc" class="tsd-anchor"></a>
					<h3>update<wbr>Process<wbr>Desc</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Process<wbr>Desc<span class="tsd-signature-symbol">(</span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, desc<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:159</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>id: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>desc: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updatestatefilterparam" class="tsd-anchor"></a>
					<h3>update<wbr>State<wbr>Filter<wbr>Param</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>State<wbr>Filter<wbr>Param<span class="tsd-signature-symbol">(</span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:546</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>工作流状态</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updatetaskfilterparam" class="tsd-anchor"></a>
					<h3>update<wbr>Task<wbr>Filter<wbr>Param</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Task<wbr>Filter<wbr>Param<span class="tsd-signature-symbol">(</span>processName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:554</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>工作流任务</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>processName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="workflowbatchrequest" class="tsd-anchor"></a>
					<h3>workflow<wbr>Batch<wbr>Request</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">workflow<wbr>Batch<wbr>Request<span class="tsd-signature-symbol">(</span>request<span class="tsd-signature-symbol">: </span><a href="../globals.html#workflowbatchrequest" class="tsd-signature-type">WorkflowBatchRequest</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#workflowuser" class="tsd-signature-type">WorkflowUser</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/workflow2/workflow2.ts:70</li>
								</ul>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>工作流批量处理</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>request: <a href="../globals.html#workflowbatchrequest" class="tsd-signature-type">WorkflowBatchRequest</a></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#workflowuser" class="tsd-signature-type">WorkflowUser</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="workflow2.html" class="tsd-kind-icon">Workflow2</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class">
								<a href="workflow2.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="workflow2.html#api" class="tsd-kind-icon">api</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="workflow2.html#metamodelname" class="tsd-kind-icon">meta<wbr>Model<wbr>Name</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="workflow2.html#modelname" class="tsd-kind-icon">model<wbr>Name</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="workflow2.html#operationtypearray" class="tsd-kind-icon">operation<wbr>Type<wbr>Array</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#addremark" class="tsd-kind-icon">add<wbr>Remark</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="workflow2.html#callbackonchange" class="tsd-kind-icon">callback<wbr>OnChange</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#canceltask" class="tsd-kind-icon">cancel<wbr>Task</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#canceltaskbatch" class="tsd-kind-icon">cancel<wbr>Task<wbr>Batch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#changemaster" class="tsd-kind-icon">change<wbr>Master</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#changeprocessstate" class="tsd-kind-icon">change<wbr>Process<wbr>State</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#changeprocessstatebatch" class="tsd-kind-icon">change<wbr>Process<wbr>State<wbr>Batch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#changetask" class="tsd-kind-icon">change<wbr>Task</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#changetaskbatch" class="tsd-kind-icon">change<wbr>Task<wbr>Batch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#checkstateeditable" class="tsd-kind-icon">check<wbr>State<wbr>Editable</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#checktaskeditable" class="tsd-kind-icon">check<wbr>Task<wbr>Editable</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#createprocessdef" class="tsd-kind-icon">create<wbr>Process<wbr>Def</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#createprocessdef2" class="tsd-kind-icon">create<wbr>Process<wbr>Def2</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#createtask" class="tsd-kind-icon">create<wbr>Task</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#createtaskbatch" class="tsd-kind-icon">create<wbr>Task<wbr>Batch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#datasoucelist" class="tsd-kind-icon">datasouce<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#detail" class="tsd-kind-icon">detail</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#detail2" class="tsd-kind-icon">detail2</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#editprocessdef" class="tsd-kind-icon">edit<wbr>Process<wbr>Def</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#edittask" class="tsd-kind-icon">edit<wbr>Task</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#edittaskbatch" class="tsd-kind-icon">edit<wbr>Task<wbr>Batch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#exporttoexcel" class="tsd-kind-icon">export<wbr>ToExcel</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#finishtask" class="tsd-kind-icon">finish<wbr>Task</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#finishtaskbatch" class="tsd-kind-icon">finish<wbr>Task<wbr>Batch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#getalloperationtype" class="tsd-kind-icon">get<wbr>All<wbr>Operation<wbr>Type</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="workflow2.html#getassociateid" class="tsd-kind-icon">get<wbr>Associate<wbr>Id</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#getdefaulttemplateurl" class="tsd-kind-icon">get<wbr>Default<wbr>Template<wbr>Url</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#getprocessinfo" class="tsd-kind-icon">get<wbr>Process<wbr>Info</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="workflow2.html#getstr" class="tsd-kind-icon">get<wbr>Str</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#gettaskdefaultdealer" class="tsd-kind-icon">get<wbr>Task<wbr>Default<wbr>Dealer</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#homecount" class="tsd-kind-icon">home<wbr>Count</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#homemasterlistcount" class="tsd-kind-icon">home<wbr>Master<wbr>List<wbr>Count</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#hometodolistcount" class="tsd-kind-icon">home<wbr>Todo<wbr>List<wbr>Count</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#listallprocess" class="tsd-kind-icon">list<wbr>All<wbr>Process</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#listallprocessname" class="tsd-kind-icon">list<wbr>All<wbr>Process<wbr>Name</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#listallprocessnamewithright" class="tsd-kind-icon">list<wbr>All<wbr>Process<wbr>Name<wbr>With<wbr>Right</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#listmodelactions" class="tsd-kind-icon">list<wbr>Model<wbr>Actions</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#listoperation" class="tsd-kind-icon">list<wbr>Operation</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#listprocessinstance" class="tsd-kind-icon">list<wbr>Process<wbr>Instance</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#listprocessinstancepanel" class="tsd-kind-icon">list<wbr>Process<wbr>Instance<wbr>Panel</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#listremark" class="tsd-kind-icon">list<wbr>Remark</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#listtaskrecord" class="tsd-kind-icon">list<wbr>Task<wbr>Record</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="workflow2.html#ontransportmessage" class="tsd-kind-icon">on<wbr>Transport<wbr>Message</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#processdefdetail" class="tsd-kind-icon">process<wbr>Def<wbr>Detail</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="workflow2.html#querydealerlist" class="tsd-kind-icon">query<wbr>Dealer<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#queryprocessbyassociateid" class="tsd-kind-icon">query<wbr>Process<wbr>ByAssociate<wbr>Id</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#queryprocessexistcount" class="tsd-kind-icon">query<wbr>Process<wbr>Exist<wbr>Count</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#queryprocesswithcount" class="tsd-kind-icon">query<wbr>Process<wbr>With<wbr>Count</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#queryworkflowdealerlist" class="tsd-kind-icon">query<wbr>Workflow<wbr>Dealer<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#queryworkflowdealerlist2" class="tsd-kind-icon">query<wbr>Workflow<wbr>Dealer<wbr>List2</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#registeronchange" class="tsd-kind-icon">register<wbr>OnChange</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#resolveoperationcontent" class="tsd-kind-icon">resolve<wbr>Operation<wbr>Content</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#resolveoperationtype" class="tsd-kind-icon">resolve<wbr>Operation<wbr>Type</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#rollbackprocess" class="tsd-kind-icon">rollback<wbr>Process</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#saveprocessdef2" class="tsd-kind-icon">save<wbr>Process<wbr>Def2</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#startprocess" class="tsd-kind-icon">start<wbr>Process</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#startprocessbatch" class="tsd-kind-icon">start<wbr>Process<wbr>Batch</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#startprocesscheck" class="tsd-kind-icon">start<wbr>Process<wbr>Check</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#updateonline" class="tsd-kind-icon">update<wbr>Online</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#updateprocessdesc" class="tsd-kind-icon">update<wbr>Process<wbr>Desc</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#updatestatefilterparam" class="tsd-kind-icon">update<wbr>State<wbr>Filter<wbr>Param</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#updatetaskfilterparam" class="tsd-kind-icon">update<wbr>Task<wbr>Filter<wbr>Param</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="workflow2.html#workflowbatchrequest" class="tsd-kind-icon">workflow<wbr>Batch<wbr>Request</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>