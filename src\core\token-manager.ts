import { configurationApi, refreshToken } from "../model/index/api"

import { events } from "./events"
import { global } from "./global"
import { getTokenExp } from "./token-checker"

class JwtTokenChecker {
    private expireTime?: number
    private interval = 0
    private started = false
    private checkTimer: NodeJS.Timeout

    public start() {
        const token = global.jwtToken
        if (!token) {
            return console.log("没登录没token")
        }
        console.log("开始检查token了")
        this.expireTime = getTokenExp(token)
        console.log(`过期时间：${this.expireTime}`)
        if (this.started) {
            return
        }
        this.startCheck()
    }
    public stop() {
        this.started = false
        this.checkTimer && clearTimeout(this.checkTimer)
    }

    private async refreshToken() {
        try {
            const ts = "last-refresh-ts"
            const tsv = global.getItem(ts)
            if (tsv) {
                if (
                    new Date().valueOf() - +tsv <
                    global.refreshInterval + 1000
                ) {
                    return console.log(`current token is already refreshed`)
                }
            }
            global.setItem(ts, new Date().valueOf() + "")
            const { jwt } = await refreshToken()
            global.jwtToken = jwt
            events.callTokenChanged(jwt)
            this.expireTime = getTokenExp(jwt)
            const xidCache = global.getXidTokens()
            // refresh xid token when personal token changed
            await Promise.all(
                xidCache.map((i) =>
                    configurationApi.changeTokenWithXid(i.xid, true)
                )
            ).then((r) => {
                for (let i = 0; i < xidCache.length; i++) {
                    global.setXidToken(xidCache[i].xid, r[i])
                }
            })
        } catch (error) {
            if (this.expireTime < new Date().valueOf()) {
                this.stop()
                events.callTokenExpiring(400)
            }
        }
    }

    private async startCheck() {
        if (!this.expireTime) {
            return
        }
        this.started = true
        const now = new Date().getTime()
        console.log(`now ${now}ms`)
        const timeToExpire = this.expireTime - now
        console.log(
            `token will expire in ${timeToExpire}ms,this.expireTime:${this.expireTime},now:${now}`
        )
        if (timeToExpire < 0) {
            this.stop()
            global.clearJWTToken()
            return events.callTokenExpiring(403)
        }
        console.log(`global.refreshInterval:${global.refreshInterval}`)
        if (global.refreshInterval > 0) {
            this.interval = global.refreshInterval
            console.log({
                interval: this.interval,
                timeToExpire: timeToExpire,
                flog: timeToExpire < global.refreshInterval,
            })
            if (timeToExpire < global.refreshInterval) {
                // 如果剩余过期时间小于刷新间隔，则设置第一次刷新间隔为剩余过期时间 - 1min
                const oneMinute = 60 * 1000
                this.interval = timeToExpire - oneMinute
                // 剩余时间小于一分钟的话，设置为立即刷新
                if (this.interval <= oneMinute) {
                    this.interval = 0
                }
            }
            console.log("token will expire in", this.interval, "ms")
            this.checkTimer = setTimeout(async () => {
                // 每次定制任务执行前检查一次
                this.expireTime = getTokenExp(global.jwtToken)
                await this.refreshToken()
                this.startCheck()
            }, this.interval)
        }
    }
}

export const tokenChecker = new JwtTokenChecker()
