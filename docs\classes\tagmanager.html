<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>TagManager | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="tagmanager.html">TagManager</a>
				</li>
			</ul>
			<h1>Class TagManager</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<a href="anonymousmodel.html" class="tsd-signature-type">AnonymousModel</a>
						<ul class="tsd-hierarchy">
							<li>
								<span class="target">TagManager</span>
							</li>
						</ul>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="tagmanager.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-overwrite tsd-is-protected"><a href="tagmanager.html#api" class="tsd-kind-icon">api</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="tagmanager.html#isanonymous" class="tsd-kind-icon">is<wbr>Anonymous</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="tagmanager.html#anonymous" class="tsd-kind-icon">anonymous</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="tagmanager.html#gettaggroups" class="tsd-kind-icon">get<wbr>Tag<wbr>Groups</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="tagmanager.html#query" class="tsd-kind-icon">query</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="tagmanager.html#unanonymous" class="tsd-kind-icon">un<wbr>Anonymous</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new <wbr>Tag<wbr>Manager<span class="tsd-signature-symbol">(</span>model_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="tagmanager.html" class="tsd-signature-type">TagManager</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/tag-manager/tag-manager.ts:5</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>model_name: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="tagmanager.html" class="tsd-signature-type">TagManager</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-overwrite tsd-is-protected">
					<a name="api" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagProtected">Protected</span> api</h3>
					<div class="tsd-signature tsd-kind-icon">api<span class="tsd-signature-symbol">:</span> <a href="tagmanagerapi.html" class="tsd-signature-type">TagManagerApi</a></div>
					<aside class="tsd-sources">
						<p>Overrides <a href="anonymousmodel.html">AnonymousModel</a>.<a href="anonymousmodel.html#api">api</a></p>
						<ul>
							<li>Defined in src/model/tag-manager/tag-manager.ts:5</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited">
					<a name="isanonymous" class="tsd-anchor"></a>
					<h3>is<wbr>Anonymous</h3>
					<div class="tsd-signature tsd-kind-icon">is<wbr>Anonymous<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
						<p>Inherited from <a href="anonymousmodel.html">AnonymousModel</a>.<a href="anonymousmodel.html#isanonymous">isAnonymous</a></p>
						<ul>
							<li>Defined in src/core/anonymous-api.ts:18</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="anonymous" class="tsd-anchor"></a>
					<h3>anonymous</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">anonymous<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="anonymousmodel.html">AnonymousModel</a>.<a href="anonymousmodel.html#anonymous">anonymous</a></p>
								<ul>
									<li>Defined in src/core/anonymous-api.ts:19</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="gettaggroups" class="tsd-anchor"></a>
					<h3>get<wbr>Tag<wbr>Groups</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Tag<wbr>Groups<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/tagmanagertypes.html#taggroup-1" class="tsd-signature-type">TagGroup</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/tag-manager/tag-manager.ts:11</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/tagmanagertypes.html#taggroup-1" class="tsd-signature-type">TagGroup</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="query" class="tsd-anchor"></a>
					<h3>query</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<span class="tsd-signature-symbol">(</span>keyValue<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/tagmanagertypes.html#queryapiresult" class="tsd-signature-type">queryApiResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/tag-manager/tag-manager.ts:15</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>keyValue: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/tagmanagertypes.html#queryapiresult" class="tsd-signature-type">queryApiResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
					<a name="unanonymous" class="tsd-anchor"></a>
					<h3>un<wbr>Anonymous</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
						<li class="tsd-signature tsd-kind-icon">un<wbr>Anonymous<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from <a href="anonymousmodel.html">AnonymousModel</a>.<a href="anonymousmodel.html#unanonymous">unAnonymous</a></p>
								<ul>
									<li>Defined in src/core/anonymous-api.ts:23</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="tagmanager.html" class="tsd-kind-icon">Tag<wbr>Manager</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class">
								<a href="tagmanager.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-overwrite tsd-is-protected">
								<a href="tagmanager.html#api" class="tsd-kind-icon">api</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-inherited">
								<a href="tagmanager.html#isanonymous" class="tsd-kind-icon">is<wbr>Anonymous</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="tagmanager.html#anonymous" class="tsd-kind-icon">anonymous</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="tagmanager.html#gettaggroups" class="tsd-kind-icon">get<wbr>Tag<wbr>Groups</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="tagmanager.html#query" class="tsd-kind-icon">query</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-inherited">
								<a href="tagmanager.html#unanonymous" class="tsd-kind-icon">un<wbr>Anonymous</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>