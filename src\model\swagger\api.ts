import axios from "../../core/axios/index"
import type * as dto from "../../def/index"
import { Method } from 'axios'

export default class SwaggerAPi {
    public query() {
        return axios.get<dto.SwaggerTypes.queryResult>(`general/interfaces/all`)
    }

    public request(
        params: Omit<dto.SwaggerTypes.requestParams, "label"> & {
            url: string
        }
    ) {
        return axios.request({
            method: params.methodName as Method,
            url: params.url,
        })
    }
}
