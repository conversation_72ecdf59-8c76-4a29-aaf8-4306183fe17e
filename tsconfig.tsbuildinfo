{"program": {"fileNames": ["./node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./src/def/index.ts", "./src/core/local-storage.ts", "./src/core/axios/index.ts", "./src/model/index/api.ts", "./src/core/sse/api.ts", "./src/core/broadcast-channel.ts", "./src/core/sse/sse.ts", "./src/model/index/sse-invoker.ts", "./src/core/events.ts", "./src/core/token-manager.ts", "./src/core/global.ts", "./src/core/tools.ts", "./src/model/index/extend.ts", "./src/model/dashboard/api.ts", "./src/model/dashboard/dashboard.ts", "./src/model/ui-config/api.ts", "./src/model/ui-config/ui-config.ts", "./src/model/etl/api.ts", "./src/model/etl/etl.ts", "./src/model/scene/api.ts", "./src/model/scene/scene.ts", "./src/model/groovy-editor/api.ts", "./src/model/groovy-editor/groovy-editor.ts", "./src/model/model-validator/api.ts", "./src/model/model-validator/model-validator.ts", "./src/model/config-center/api.ts", "./src/model/config-center/config-center.ts", "./src/model/swagger/api.ts", "./src/model/swagger/swagger.ts", "./src/model/password-box/api.ts", "./src/model/password-box/password-box.ts", "./src/model/global-search/api.ts", "./src/model/global-search/global-search.ts", "./src/model/index-search/api.ts", "./src/model/index-search/index-search.ts", "./src/model/role/role-api.ts", "./src/core/anonymous.ts", "./src/model/list/api.ts", "./src/model/list/workflow-api.ts", "./src/model/list/workflow.ts", "./src/model/list/list-query.ts", "./src/model/list/list.ts", "./src/model/action/api.ts", "./src/model/action/action.ts", "./src/model/role/role.ts", "./src/model/gateway/api.ts", "./src/model/gateway/gateway.ts", "./src/model/domain-service/api.ts", "./src/model/domain-service/domain-service.ts", "./src/core/axios/axios.ts", "./src/model/login/api.ts", "./src/model/login/passport.ts", "./src/model/new-swagger/api.ts", "./src/model/new-swagger/new-swagger.ts", "./src/model/login/auth.ts", "./src/model/login/o-auth.ts", "./src/model/login/bind-account.ts", "./src/model/list/list-easy.ts", "./src/model/list/list-hard.ts", "./src/model/tree/api.ts", "./src/model/tree/tree.ts", "./src/model/detail/api.ts", "./src/model/detail/detail.ts", "./src/model/pivot-table/api.ts", "./src/model/pivot-table/pivot-table.ts", "./src/model/tag-manager/api.ts", "./src/model/tag-manager/tag-manager.ts", "./src/model/model-schema-manager/api.ts", "./src/model/model-schema-manager/model-schema-manager.ts", "./src/model/template-manager/api.ts", "./src/model/template-manager/template-manager.ts", "./src/model/model/api.ts", "./src/model/model/joint-search-manager.ts", "./src/model/workflow2/api.ts", "./src/model/workflow2/workflow2.ts", "./src/model/model/model.ts", "./src/model/index/index.ts", "./src/index.ts", "./src/core/type-mapper.ts", "./src/types/example.d.ts"], "fileInfos": [{"version": "ac3a8c4040e183ce38da69d23b96939112457d1936198e6542672b7963cf0fce", "affectsGlobalScope": true}, "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", {"version": "1dad4fe1561d99dfd6709127608b99a76e5c2643626c800434f99c48038567ee", "affectsGlobalScope": true}, {"version": "cce43d02223f8049864f8568bed322c39424013275cd3bcc3f51492d8b546cb3", "affectsGlobalScope": true}, {"version": "43fb1d932e4966a39a41b464a12a81899d9ae5f2c829063f5571b6b87e6d2f9c", "affectsGlobalScope": true}, {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, {"version": "8dff1b4c2df638fcd976cbb0e636f23ab5968e836cd45084cc31d47d1ab19c49", "affectsGlobalScope": true}, {"version": "2bb4b3927299434052b37851a47bf5c39764f2ba88a888a107b32262e9292b7c", "affectsGlobalScope": true}, {"version": "810627a82ac06fb5166da5ada4159c4ec11978dfbb0805fe804c86406dab8357", "affectsGlobalScope": true}, {"version": "62d80405c46c3f4c527ee657ae9d43fda65a0bf582292429aea1e69144a522a6", "affectsGlobalScope": true}, {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, {"version": "8f4c9f651c8294a2eb1cbd12581fe25bfb901ab1d474bb93cd38c7e2f4be7a30", "affectsGlobalScope": true}, {"version": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "affectsGlobalScope": true}, {"version": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "affectsGlobalScope": true}, {"version": "7ce9f0bde3307ca1f944119f6365f2d776d281a393b576a18a2f2893a2d75c98", "affectsGlobalScope": true}, {"version": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "affectsGlobalScope": true}, {"version": "12a310447c5d23c7d0d5ca2af606e3bd08afda69100166730ab92c62999ebb9d", "affectsGlobalScope": true}, {"version": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "affectsGlobalScope": true}, "e4083f017f6caa61d2031de06d92e9fff243214033619d809a380d17ef8c18ea", "77e535d170bbb82171d7ab4d49dbcc08258194addfca09dff0eebb884d637fb7", "f4007f9a7d93be83e1f7298d7d933c59c43d85e04989d9eee445f0c1624d2993", "d7e3e36af89d2882411d87f5b72130d6282b6e08331268c09b06a05418a39ed4", "07bc2505555b393ec353fa0d6abe5883622a6240262a159090ef767f8c4eed8b", "29f0c53df614f257f78f4eedabcdc984cbc81ff68fdee35903046da15ea5da53", "0c611c043e2d472ea98ee6866e06708da6cdfae56b9b95d86318e97e24e08988", "8990fb4174218d174659a6c7a5381484e93892622671c612f9ee2be95e6dfe70", "4ffffbb5f78c1a0fc3aa914a15e50a01fafcca9542535cb27fc4a9b3293e8673", "508a90dc702e459512730b08c9fcde1c18525e060086e84c858718e4a5303137", "3023d4aec0b72ae9ed611ab2b9d1bbb3c1c4f87173aabd67cd16c664f8408032", "4356087b60ea30bf23f1ce2dfd37d60695422f6aebd244a9c578355e614ff832", "2e0becca31fa4db45b0afc07f6dde8cb3ab980a8762fa9ba0d3874d00f0232bb", "6b25f2eb19dcc17221c5debf2785d5bf7b6d18c87a7d9a2e2240a1a1c6931269", "c0d178fc8e1d30c5d2d1990624546d869c3d7aa45e1beedd033128c52b6f7743", "e1a1c366a8f484763025e480a97364d415e4d1b65b54f9b0b32ed85b444d961c", "d0a68a083ca8f0f9fa5ab61fe635d3747fffe12f954c533d5fb783d08cfcc4be", "59d7ee5e22c03345b8f8cb52b9a81acb5739bcf497e99b86a3932a3aaa9d128e", "9cbbc87cf6ee136b7947a848f0ddc2a51710a61fa87e2237e912ae3a061cb7ed", "27e40e30390dedcab8099560be27926e855317ecc4a0c08e159285f75c0a5121", "4eecb3454355263308080d29342dde54de3682a3f1b870d558056acb407c7776", "15de8c02330c799425542dcfc5e1e93caf158471accce7906b573ac2a8088630", "95209947ca1f9282f401a6f59748b8c991735e0e3f25fa8b1f731f288c107477", "45267299cce1d0e0dd1fd775a2ec60aad868420332f40a55efbb2cc936bbf540", "458c643e61f237f0f4cc0577725ea669ad0e2e599a5454f71af447de0170f97d", "3dd8ba5b14c6b795c2a9c1ffa6e2fc772544410aa63339ec8748f91f814abdf6", "a2ae067b8e746ac02d50ad09806c5c13b58593403c0f9fa32931047687a45023", "60a797355f23dd0b1a600f708cdbb2bba09e61e5c1203882f0a6a2ed84a661e6", "b7f77f89b0464ba94baf9e60d404bbebb6ba1f049e2eae2fbe408fea060acb6a", "4412b45ffea32b268d9c33f00968b016b42c86cb762d7d39bbe90fe85861d3eb", "7a058eb2ba682b045bc5fd94432c1b075458bacdea53c271e7d5d48e33c996b6", "83837571d1a4bed797c63801538bce86412ed46d4472d414d9785d878e0d686d", "0bbb20b9fb23ec3435ad0f0e61750b12e85ab192c4c65bbdc8633773756c5956", "52a96951332954985ad73f2d943b73c903b575c58393c1bc6e9595ad7bcca61d", "52dae317a610453f536269d5d421a4b77bb245456e45998f22f824885d9184a2", "8d5b7660fc5d045550f96a797de5b487a257a8f5a6deb2353c25a8aac4604320", "7cccd895251d7ec7b39de89e77f6745e198d81275624f38c6d39a7c6f40b0093", "b13f7241ffadf7e726da9ffe3a9fabb5d8b6a651d33117dc56e5c80b255f2708", "d6f6d59b4e41ab63f5d69a37f0b952ad26ee410789489c2d0d242d2b6e76a41b", "73aac61cda55ce4d8991e5eeef498f00345fe97d920b619e6ba42c750d092783", "aee47c6d69b4506929a2ce71e3a6e08412be07de34ad8255d9ae105ba363e00b", "c30236489b00c08e6fe5ea2b47683e0a57ade65b1d933825d2ea72783248c0a4", "6d29909be7b2db26ddd7956d4949c99f620eb70cbbfd814967978fde444a7e66", "159ae75df0762e94968e8afde335f216a9e163c5d1252bc826940064e5ee681b", "3e18fa824cc0651fef8709f74bb9718a1855b22924117b6b55222c79b848fc06", "54fa1fe4ac682b8f49538479b68f9b0113aacf733acc9430ba2a9d6df5b89fb1", "8fb003a9921783b125712d41482bb3fa1457d2dcc107c793967cae177b878941", "877ebf3929b581d266291c183cd695541db5bc69ff64d55599ad7bfc26518aa7", "9e334a37e0bce1b41966064d6bb5fd29afc374b376dac5c3d75c3a2b26a3f105", "899fec633b0da831ce84475835db137a73e2961ffaa12434fb9665795b2a3332", "bd94cecb7f65c79c9e3bcaeb2159b8fbf928cd579e4b65c80cad9854347c8c95", "9625da0594f0ff92c04296cd219199bb201368139f0b8618171ed60345135f80", "11a80f994d2aa8537014003f65a5869f309ee01766effb2600820affcf8e30c7", "a7738073098c6af7f55d523f04cc3534d58d49323637cac501d5d1f904c870e8", "3a5df1d76a430a3c004768847b12dac0c3abf55574eda9b19795a2dd918d7633", "14cdb86a78a9c1d9476ea92a999c51f9b47af87057d604872e2ead23b12b43bd", "5bfcae703f0d456f321f8798084cece041f2cc610ccfff060e822b005505de9b", "b7d7ed97dca9d5fcdb864edd681a3bb97ef435be9c35fd1592f583d9186e0e52", "13cefa67337d9a2762b002fc83c968edf2372a489643da97c1c5f36c3751c960", "5b378ef117750865aad23092aa4598fcc82f46d37bb050ca3d3df18dfaff8649", "0320afeff09a8245cd520467f1fc52c2d03f9565cb6a2bc3bf39214d90971605", "794c2cac01ee10995fc3fdd1c09c2ecb8c73e20bd04a8652fb543bc36532a1fc", "05fefeef47ce8924cbde66d18691ee384f62fa53a4b417ffe3059f8f9f9b226a", "8929040251515829fe07c14590b22efacf45f31ee48a02c7a89bcc0c2eeb50c5", "85874c0228961e9430484dc48a922e1f09c44ab0afce5a3f551e1ac3095abc09", "f47d569866d68e7bed06d3309dcb120f13dc60aca03a238b96e4cd114fe203a8", "a454737eda69f178814d47f0f71ab32cb6584f8b15580cdd3a134abcadd7d3b0", "fd76dc99e028af21c94d992cb35620ba4847dc4e0797985d6fe04b6bd63a1e86", "f152cc12c955e19ef73d7c73748ed72f94be85aa98a50f6a4aa0daea8312c34b", "d8232f0dd95fb8f9bfe6b6f156ad7bac6471e328b8f126131ec02543ff5d60f2", "8c3016c73582b2979367754194e43eeae6a13877303461d4fd9e424905b2ae0a", "d40283bbd1b0ba87129e999241042b9d44f40e278a7da5e0b67a39c3fb58a0bb", "48b4b03e64a87b45ae70002e068b2345ff6de2777fc8446df494381a9093ea45", "81794b6a5bbcdf9d7ab22ec0c75961bb74156ff5c459d14b1555d73c0bb67019", "f72318b1f07195abc819c2e3ce5bb57877ea6d6eb4b374707dad59afc805e5ab", "61e08a39e04a7cdc1272e8bb60c61174a22157023b6043befd36b1ff29d3c76b", "d7559b2391d50fcd8d319214f2a928e256d2f67a7d70489696ea71a9df055616", "a65706c19a40f6cbf102275c8fc998ac784cb65c6a782a976180030460e498b4", "d7f7106e93ac204a8fb9cbacd8f395fcf41bfe49cf139807e65ab47283bcdb44", "c97e4cc567b446faa06e7e831c26d1f3dccec011c2c04c17af2b0292c6927f3f"], "options": {"declaration": true, "esModuleInterop": true, "inlineSourceMap": true, "module": 1, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "./build", "rootDir": "./src", "target": 1}, "fileIdsList": [[31], [29, 31], [21], [21, 27, 28], [21, 22, 30], [31, 100], [21, 22, 25, 26, 29, 31, 32], [22, 24, 29, 32], [21, 22, 31, 100], [32, 33, 97], [21, 31, 32, 63], [21, 23, 31, 32, 57], [21, 23, 32], [21, 29, 46], [21, 23], [21, 29, 34], [21, 29, 32, 82], [21, 68], [21, 23, 31, 32], [21, 29, 31, 32, 38], [21, 23, 31, 57], [21, 66], [21, 23, 31], [21, 52], [21, 29, 42], [21, 54], [21, 24, 26, 28, 29, 30, 31, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 65, 67, 69, 70, 72, 74, 75, 76, 77, 96], [27], [21, 62], [21, 31, 32, 58], [21, 29, 31, 32, 58, 60, 61], [21, 59], [21, 31, 71], [21, 29, 88], [21, 29, 44], [21, 92], [21, 64, 78, 79, 81, 83, 85, 87, 89, 91, 92, 93, 95], [21, 29, 73], [21, 29, 50], [21, 32, 84], [21, 29, 56, 62, 64], [21, 29, 40], [21, 31, 48], [21, 86], [21, 29, 90], [21, 80], [21, 29, 36], [21, 94]], "referencedMap": [[57, 1], [70, 2], [26, 3], [29, 4], [31, 5], [25, 6], [27, 7], [30, 8], [32, 9], [98, 10], [64, 11], [63, 12], [46, 13], [47, 14], [34, 15], [35, 16], [82, 12], [83, 17], [68, 15], [69, 18], [38, 19], [39, 20], [66, 21], [67, 22], [52, 23], [53, 24], [42, 23], [43, 25], [54, 15], [55, 26], [24, 23], [97, 27], [28, 28], [58, 12], [78, 29], [79, 29], [61, 30], [62, 31], [59, 13], [60, 32], [71, 13], [75, 33], [77, 33], [76, 33], [72, 33], [88, 23], [89, 34], [44, 23], [45, 35], [92, 19], [93, 36], [96, 37], [73, 23], [74, 38], [50, 23], [51, 39], [84, 19], [85, 40], [56, 15], [65, 41], [40, 23], [41, 42], [48, 23], [49, 43], [86, 15], [87, 44], [90, 19], [91, 45], [80, 13], [81, 46], [36, 23], [37, 47], [94, 13], [95, 48]], "exportedModulesMap": [[57, 1], [70, 2], [26, 3], [29, 4], [31, 5], [25, 6], [27, 7], [30, 8], [32, 9], [98, 10], [64, 11], [63, 12], [46, 13], [47, 14], [34, 15], [35, 16], [82, 12], [83, 17], [68, 15], [69, 18], [38, 19], [39, 20], [66, 21], [67, 22], [52, 23], [53, 24], [42, 23], [43, 25], [54, 15], [55, 26], [24, 23], [97, 27], [28, 28], [58, 12], [78, 29], [79, 29], [61, 30], [62, 31], [59, 13], [60, 32], [71, 13], [75, 33], [77, 33], [76, 33], [72, 33], [88, 23], [89, 34], [44, 23], [45, 35], [92, 19], [93, 36], [96, 37], [73, 23], [74, 38], [50, 23], [51, 39], [84, 19], [85, 40], [56, 15], [65, 41], [40, 23], [41, 42], [48, 23], [49, 43], [86, 15], [87, 44], [90, 19], [91, 45], [80, 13], [81, 46], [36, 23], [37, 47], [94, 13], [95, 48]], "semanticDiagnosticsPerFile": [5, 7, 6, 2, 8, 9, 10, 11, 12, 13, 14, 15, 3, 4, 19, 16, 17, 18, 20, 1, 57, [70, [{"file": "./src/core/axios/axios.ts", "start": 37, "length": 7, "messageText": "Cannot find module 'axios' or its corresponding type declarations.", "category": 1, "code": 2307}]], [23, [{"file": "./src/core/axios/index.ts", "start": 30, "length": 7, "messageText": "Cannot find module 'axios' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/core/axios/index.ts", "start": 150, "length": 7, "messageText": "Cannot find name 'require'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node` and then add 'node' to the types field in your tsconfig.", "category": 1, "code": 2591}, {"file": "./src/core/axios/index.ts", "start": 345, "length": 7, "messageText": "Cannot find name 'require'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node` and then add 'node' to the types field in your tsconfig.", "category": 1, "code": 2591}, {"file": "./src/core/axios/index.ts", "start": 547, "length": 7, "messageText": "Cannot find name 'require'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node` and then add 'node' to the types field in your tsconfig.", "category": 1, "code": 2591}]], 26, [29, [{"file": "./src/core/events.ts", "start": 525, "length": 3, "messageText": "'sse' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 31, 22, 25, [27, [{"file": "./src/core/sse/sse.ts", "start": 3788, "length": 1, "messageText": "'e' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [30, [{"file": "./src/core/token-manager.ts", "start": 23, "length": 12, "messageText": "Cannot find module 'jwt-decode' or its corresponding type declarations.", "category": 1, "code": 2307}]], [32, [{"file": "./src/core/tools.ts", "start": 9992, "length": 4, "messageText": "'done' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 99, 21, 98, [64, [{"file": "./src/model/action/action.ts", "start": 149, "length": 47, "messageText": "'paramHandler' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/model/action/action.ts", "start": 3979, "length": 5, "messageText": "Property 'props' is declared but its value is never read.", "category": 1, "code": 6138, "reportsUnnecessary": true}, {"file": "./src/model/action/action.ts", "start": 8786, "length": 4, "messageText": "'done' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/model/action/action.ts", "start": 8975, "length": 6, "messageText": "'failed' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [63, [{"file": "./src/model/action/api.ts", "start": 2454, "length": 7, "messageText": "Cannot find name 'require'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node` and then add 'node' to the types field in your tsconfig.", "category": 1, "code": 2591}]], [46, [{"file": "./src/model/config-center/api.ts", "start": 80, "length": 50, "messageText": "'createFormData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [47, [{"file": "./src/model/config-center/config-center.ts", "start": 72, "length": 45, "messageText": "'sseEvents' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 34, [35, [{"file": "./src/model/dashboard/dashboard.ts", "start": 356, "length": 14, "messageText": "Property 'subProjectName' is declared but its value is never read.", "category": 1, "code": 6138, "reportsUnnecessary": true}, {"file": "./src/model/dashboard/dashboard.ts", "start": 388, "length": 13, "messageText": "Property 'dashboardName' is declared but its value is never read.", "category": 1, "code": 6138, "reportsUnnecessary": true}, {"file": "./src/model/dashboard/dashboard.ts", "start": 1078, "length": 13, "messageText": "Not all code paths return a value.", "category": 1, "code": 7030}]], 82, 83, [68, [{"file": "./src/model/domain-service/api.ts", "start": 0, "length": 35, "messageText": "'dto' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/model/domain-service/api.ts", "start": 64, "length": 7, "messageText": "Cannot find module 'axios' or its corresponding type declarations.", "category": 1, "code": 2307}]], [69, [{"file": "./src/model/domain-service/domain-service.ts", "start": 101, "length": 7, "messageText": "Cannot find module 'axios' or its corresponding type declarations.", "category": 1, "code": 2307}]], [38, [{"file": "./src/model/etl/api.ts", "start": 0, "length": 34, "messageText": "'encode' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [39, [{"file": "./src/model/etl/etl.ts", "start": 63, "length": 45, "messageText": "'sseEvents' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/model/etl/etl.ts", "start": 118, "length": 12, "messageText": "'encodeParams' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/model/etl/etl.ts", "start": 168, "length": 38, "messageText": "'global' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/model/etl/etl.ts", "start": 309, "length": 2, "messageText": "Property 'id' is declared but its value is never read.", "category": 1, "code": 6138, "reportsUnnecessary": true}, {"file": "./src/model/etl/etl.ts", "start": 1009, "length": 6, "messageText": "'failed' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [66, [{"file": "./src/model/gateway/api.ts", "start": 0, "length": 35, "messageText": "'dto' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/model/gateway/api.ts", "start": 36, "length": 54, "messageText": "'getAnonymousUrl' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 67, [52, [{"file": "./src/model/global-search/api.ts", "start": 80, "length": 38, "messageText": "'global' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 53, [42, [{"file": "./src/model/groovy-editor/api.ts", "start": 80, "length": 38, "messageText": "'global' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [43, [{"file": "./src/model/groovy-editor/groovy-editor.ts", "start": 72, "length": 45, "messageText": "'sseEvents' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 54, 55, [24, [{"file": "./src/model/index/api.ts", "start": 141, "length": 7, "messageText": "Cannot find module 'axios' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/model/index/api.ts", "start": 720, "length": 7, "messageText": "Cannot find name 'require'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node` and then add 'node' to the types field in your tsconfig.", "category": 1, "code": 2591}]], [33, [{"file": "./src/model/index/extend.ts", "start": 68, "length": 13, "messageText": "Cannot find module 'uniplat-sdk' or its corresponding type declarations.", "category": 1, "code": 2307}]], [97, [{"file": "./src/model/index/index.ts", "start": 1398, "length": 6, "messageText": "Property 'config' is declared but its value is never read.", "category": 1, "code": 6138, "reportsUnnecessary": true}]], [28, [{"file": "./src/model/index/sse-invoker.ts", "start": 872, "length": 6, "messageText": "Not all code paths return a value.", "category": 1, "code": 7030}]], 58, 78, 79, [61, [{"file": "./src/model/list/list-query.ts", "start": 341, "length": 10, "messageText": "'model_name' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [62, [{"file": "./src/model/list/list.ts", "start": 463, "length": 8, "messageText": "'LeftTree' is declared but never used.", "category": 1, "code": 6196, "reportsUnnecessary": true}]], 59, 60, 71, 75, [77, [{"file": "./src/model/login/bind-account.ts", "start": 1, "length": 39, "messageText": "'global' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 76, 72, [88, [{"file": "./src/model/model-schema-manager/api.ts", "start": 80, "length": 38, "messageText": "'global' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [89, [{"file": "./src/model/model-schema-manager/model-schema-manager.ts", "start": 78, "length": 45, "messageText": "'sseEvents' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [44, [{"file": "./src/model/model-validator/api.ts", "start": 80, "length": 38, "messageText": "'global' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [45, [{"file": "./src/model/model-validator/model-validator.ts", "start": 74, "length": 45, "messageText": "'sseEvents' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [92, [{"file": "./src/model/model/api.ts", "start": 36, "length": 39, "messageText": "'global' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 93, 96, [73, [{"file": "./src/model/new-swagger/api.ts", "start": 80, "length": 38, "messageText": "'global' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [74, [{"file": "./src/model/new-swagger/new-swagger.ts", "start": 70, "length": 45, "messageText": "'sseEvents' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [50, [{"file": "./src/model/password-box/api.ts", "start": 80, "length": 38, "messageText": "'global' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [51, [{"file": "./src/model/password-box/password-box.ts", "start": 71, "length": 45, "messageText": "'sseEvents' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [84, [{"file": "./src/model/pivot-table/api.ts", "start": 99, "length": 14, "messageText": "'createFormData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/model/pivot-table/api.ts", "start": 229, "length": 11, "messageText": "'isAnonymous' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [85, [{"file": "./src/model/pivot-table/pivot-table.ts", "start": 360, "length": 10, "messageText": "Property 'model_name' is declared but its value is never read.", "category": 1, "code": 6138, "reportsUnnecessary": true}, {"file": "./src/model/pivot-table/pivot-table.ts", "start": 388, "length": 14, "messageText": "Property 'pivottableName' is declared but its value is never read.", "category": 1, "code": 6138, "reportsUnnecessary": true}]], 56, 65, 40, [41, [{"file": "./src/model/scene/scene.ts", "start": 65, "length": 45, "messageText": "'sseEvents' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [48, [{"file": "./src/model/swagger/api.ts", "start": 80, "length": 38, "messageText": "'global' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 49, 86, 87, [90, [{"file": "./src/model/template-manager/api.ts", "start": 80, "length": 38, "messageText": "'global' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [91, [{"file": "./src/model/template-manager/template-manager.ts", "start": 75, "length": 45, "messageText": "'sseEvents' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 80, [81, [{"file": "./src/model/tree/tree.ts", "start": 72, "length": 18, "messageText": "'createInitErrorMsg' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/model/tree/tree.ts", "start": 231, "length": 9, "messageText": "Property 'modelName' is declared but its value is never read.", "category": 1, "code": 6138, "reportsUnnecessary": true}, {"file": "./src/model/tree/tree.ts", "start": 311, "length": 8, "messageText": "'property' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/model/tree/tree.ts", "start": 329, "length": 4, "messageText": "'node' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [36, [{"file": "./src/model/ui-config/api.ts", "start": 80, "length": 38, "messageText": "'global' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [37, [{"file": "./src/model/ui-config/ui-config.ts", "start": 68, "length": 45, "messageText": "'sseEvents' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/model/ui-config/ui-config.ts", "start": 218, "length": 9, "messageText": "'datamodel' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/model/ui-config/ui-config.ts", "start": 262, "length": 13, "messageText": "Property 'datamodelName' is declared but its value is never read.", "category": 1, "code": 6138, "reportsUnnecessary": true}]], 94, [95, [{"file": "./src/model/workflow2/workflow2.ts", "start": 77, "length": 18, "messageText": "'createInitErrorMsg' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/model/workflow2/workflow2.ts", "start": 530, "length": 9, "messageText": "Property 'modelName' is declared but its value is never read.", "category": 1, "code": 6138, "reportsUnnecessary": true}]], 100]}, "version": "4.3.2"}