import ModelValidatorApi from "./api"

export class ModelValidator {
    private api: ModelValidatorApi
    constructor(dataModelId: string) {
        this.api = new ModelValidatorApi(dataModelId)
    }
    public validate() {
        return this.api.validate()
    }
    public replaceGroovyWithJson() {
        return this.api.replaceGroovyWithJson()
    }
    public execute(actionId: string, dataId: string) {
        return this.api.execute(actionId, dataId)
    }
}
