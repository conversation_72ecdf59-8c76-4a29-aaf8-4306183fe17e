<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>SchemaItemType | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="../modules/modelschemamanagertypes.html">ModelSchemaManagerTypes</a>
				</li>
				<li>
					<a href="modelschemamanagertypes.schemaitemtype.html">SchemaItemType</a>
				</li>
			</ul>
			<h1>Enumeration SchemaItemType</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Enumeration members</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="modelschemamanagertypes.schemaitemtype.html#custom" class="tsd-kind-icon">Custom</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="modelschemamanagertypes.schemaitemtype.html#system" class="tsd-kind-icon">System</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Enumeration members</h2>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="custom" class="tsd-anchor"></a>
					<h3>Custom</h3>
					<div class="tsd-signature tsd-kind-icon">Custom<span class="tsd-signature-symbol">:</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:316</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="system" class="tsd-anchor"></a>
					<h3>System</h3>
					<div class="tsd-signature tsd-kind-icon">System<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:315</li>
						</ul>
					</aside>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
					<li class="current tsd-kind-namespace">
						<a href="../modules/modelschemamanagertypes.html">Model<wbr>Schema<wbr>Manager<wbr>Types</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-enum tsd-parent-kind-namespace">
						<a href="modelschemamanagertypes.schemaitemtype.html" class="tsd-kind-icon">Schema<wbr>Item<wbr>Type</a>
						<ul>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="modelschemamanagertypes.schemaitemtype.html#custom" class="tsd-kind-icon">Custom</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="modelschemamanagertypes.schemaitemtype.html#system" class="tsd-kind-icon">System</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
					<li class=" tsd-kind-enum tsd-parent-kind-namespace">
						<a href="modelschemamanagertypes.schematype.html" class="tsd-kind-icon">Schema<wbr>Type</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="../modules/modelschemamanagertypes.html#constructorparams" class="tsd-kind-icon">Constructor<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="../modules/modelschemamanagertypes.html#schemaitem" class="tsd-kind-icon">Schema<wbr>Item</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="../modules/modelschemamanagertypes.html#addapiparams" class="tsd-kind-icon">addAPIParams</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="../modules/modelschemamanagertypes.html#addsureapiparams" class="tsd-kind-icon">add<wbr>SureAPIParams</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="../modules/modelschemamanagertypes.html#saveapiparams" class="tsd-kind-icon">saveAPIParams</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="../modules/modelschemamanagertypes.html#shareapiparams" class="tsd-kind-icon">shareAPIParams</a>
					</li>
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>