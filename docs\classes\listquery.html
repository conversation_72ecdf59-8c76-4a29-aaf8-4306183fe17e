<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>ListQuery | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="listquery.html">ListQuery</a>
				</li>
			</ul>
			<h1>Class ListQuery&lt;RowType&gt;</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-type-parameters">
				<h3>Type parameters</h3>
				<ul class="tsd-type-parameters">
					<li>
						<h4>RowType<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListRow</span> = <span class="tsd-signature-type">dto.ListRow</span></h4>
					</li>
				</ul>
			</section>
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">ListQuery</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="listquery.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="listquery.html#api" class="tsd-kind-icon">api</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="listquery.html#currentpage" class="tsd-kind-icon">current<wbr>Page</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="listquery.html#donecallback" class="tsd-kind-icon">done<wbr>Callback</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="listquery.html#failedcallback" class="tsd-kind-icon">failed<wbr>Callback</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="listquery.html#ismeta" class="tsd-kind-icon">is<wbr>Meta</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="listquery.html#keyfieldvalue" class="tsd-kind-icon">key<wbr>Field<wbr>Value</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="listquery.html#listqueryparams" class="tsd-kind-icon">list<wbr>Query<wbr>Params</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="listquery.html#list_name" class="tsd-kind-icon">list_<wbr>name</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="listquery.html#pagescolumns" class="tsd-kind-icon">pages<wbr>Columns</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class"><a href="listquery.html#rawfilters" class="tsd-kind-icon">raw<wbr>Filters</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="listquery.html#shownrows" class="tsd-kind-icon">shown<wbr>Rows</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listquery.html#fullfillparams" class="tsd-kind-icon">fullfill<wbr>Params</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listquery.html#getallshownrowskeys" class="tsd-kind-icon">get<wbr>All<wbr>Shown<wbr>Rows<wbr>Keys</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listquery.html#getpagecounts" class="tsd-kind-icon">get<wbr>Page<wbr>Counts</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="listquery.html#getsingletabpagelist" class="tsd-kind-icon">get<wbr>Single<wbr>Tab<wbr>Page<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listquery.html#getspecificpagemeta" class="tsd-kind-icon">get<wbr>Specific<wbr>Page<wbr>Meta</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="listquery.html#getspecifictablist" class="tsd-kind-icon">get<wbr>Specific<wbr>Tab<wbr>List</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="listquery.html#init" class="tsd-kind-icon">init</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="listquery.html#isgetspecifictablistfunctype" class="tsd-kind-icon">is<wbr>Get<wbr>Specific<wbr>Tab<wbr>List<wbr>Func<wbr>Type</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listquery.html#isinfirstpage" class="tsd-kind-icon">is<wbr>InFirst<wbr>Page</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="listquery.html#ispageindex" class="tsd-kind-icon">is<wbr>Page<wbr>Index</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="listquery.html#updatecurrentpage" class="tsd-kind-icon">update<wbr>Current<wbr>Page</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="listquery.html#updatefilterparam" class="tsd-kind-icon">update<wbr>Filter<wbr>Param</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new <wbr>List<wbr>Query<span class="tsd-signature-symbol">(</span>api<span class="tsd-signature-symbol">: </span><a href="listapi.html" class="tsd-signature-type">listApi</a>, listData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.constructor</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>pagesColumns<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> }</span>, listQueryParams<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.fullfilledQueryProps</span>, doneCallback<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span>data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.IQueryResult</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span>, failedCallback<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span>, isMeta<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="listquery.html" class="tsd-signature-type">ListQuery</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-query.ts:23</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>api: <a href="listapi.html" class="tsd-signature-type">listApi</a></h5>
								</li>
								<li>
									<h5>listData: <span class="tsd-signature-type">dto.ListTypes.constructor</span><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>pagesColumns<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> }</span></h5>
								</li>
								<li>
									<h5>listQueryParams: <span class="tsd-signature-type">dto.ListTypes.fullfilledQueryProps</span></h5>
								</li>
								<li>
									<h5>doneCallback: <span class="tsd-signature-symbol">(</span>data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.IQueryResult</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.IQueryResult</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>data: <span class="tsd-signature-type">dto.ListTypes.IQueryResult</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
								<li>
									<h5>failedCallback: <span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>e: <span class="tsd-signature-type">string</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> isMeta: <span class="tsd-signature-type">boolean</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="listquery.html" class="tsd-signature-type">ListQuery</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="api" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> api</h3>
					<div class="tsd-signature tsd-kind-icon">api<span class="tsd-signature-symbol">:</span> <a href="listapi.html" class="tsd-signature-type">listApi</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list-query.ts:26</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="currentpage" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> current<wbr>Page</h3>
					<div class="tsd-signature tsd-kind-icon">current<wbr>Page<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list-query.ts:18</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="donecallback" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> done<wbr>Callback</h3>
					<div class="tsd-signature tsd-kind-icon">done<wbr>Callback<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.IQueryResult</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list-query.ts:33</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-class">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.IQueryResult</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>data: <span class="tsd-signature-type">dto.ListTypes.IQueryResult</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="failedcallback" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> failed<wbr>Callback</h3>
					<div class="tsd-signature tsd-kind-icon">failed<wbr>Callback<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list-query.ts:36</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-class">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>e: <span class="tsd-signature-type">string</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="ismeta" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> is<wbr>Meta</h3>
					<div class="tsd-signature tsd-kind-icon">is<wbr>Meta<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list-query.ts:23</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="keyfieldvalue" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> key<wbr>Field<wbr>Value</h3>
					<div class="tsd-signature tsd-kind-icon">key<wbr>Field<wbr>Value<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list-query.ts:14</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="listqueryparams" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> list<wbr>Query<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">list<wbr>Query<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.ListTypes.fullfilledQueryProps</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list-query.ts:32</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="list_name" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> list_<wbr>name</h3>
					<div class="tsd-signature tsd-kind-icon">list_<wbr>name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list-query.ts:12</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="pagescolumns" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> pages<wbr>Columns</h3>
					<div class="tsd-signature tsd-kind-icon">pages<wbr>Columns<span class="tsd-signature-symbol">:</span> <a href="../globals.html#pagescolumns" class="tsd-signature-type">PagesColumns</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list-query.ts:16</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class">
					<a name="rawfilters" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> raw<wbr>Filters</h3>
					<div class="tsd-signature tsd-kind-icon">raw<wbr>Filters<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.metaFilter</span><span class="tsd-signature-symbol">[]</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list-query.ts:15</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="shownrows" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> shown<wbr>Rows</h3>
					<div class="tsd-signature tsd-kind-icon">shown<wbr>Rows<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.metaRow</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/list/list-query.ts:13</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="fullfillparams" class="tsd-anchor"></a>
					<h3>fullfill<wbr>Params</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">fullfill<wbr>Params<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.queryProps</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../modules/listtypes.html#fullfilledqueryprops" class="tsd-signature-type">fullfilledQueryProps</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-query.ts:326</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.queryProps</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="../modules/listtypes.html#fullfilledqueryprops" class="tsd-signature-type">fullfilledQueryProps</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getallshownrowskeys" class="tsd-anchor"></a>
					<h3>get<wbr>All<wbr>Shown<wbr>Rows<wbr>Keys</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>All<wbr>Shown<wbr>Rows<wbr>Keys<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">RowType[string][1]</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-query.ts:89</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">RowType[string][1]</span><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getpagecounts" class="tsd-anchor"></a>
					<h3>get<wbr>Page<wbr>Counts</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Page<wbr>Counts<span class="tsd-signature-symbol">(</span>listQueryParams<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>page_datas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-query.ts:350</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>listQueryParams: <span class="tsd-signature-type">any</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>page_datas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>record_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="getsingletabpagelist" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> get<wbr>Single<wbr>Tab<wbr>Page<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Single<wbr>Tab<wbr>Page<wbr>List<span class="tsd-signature-symbol">(</span>item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdataofsinglepagerequestresult" class="tsd-signature-type">getListDataOfSinglePageRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-query.ts:129</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>item_index: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>item_size: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdataofsinglepagerequestresult" class="tsd-signature-type">getListDataOfSinglePageRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getspecificpagemeta" class="tsd-anchor"></a>
					<h3>get<wbr>Specific<wbr>Page<wbr>Meta</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Specific<wbr>Page<wbr>Meta<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.setColumnsForPagesParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#setcolumnsforpagesapiresult" class="tsd-signature-type">setColumnsForPagesApiResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-query.ts:45</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.setColumnsForPagesParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#setcolumnsforpagesapiresult" class="tsd-signature-type">setColumnsForPagesApiResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="getspecifictablist" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> get<wbr>Specific<wbr>Tab<wbr>List</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Specific<wbr>Tab<wbr>List<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>tabName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdatabytabrequestresult" class="tsd-signature-type">getListDataByTabRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-query.ts:99</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-symbol">{ </span>item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>tabName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5>item_<wbr>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5>item_<wbr>size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5>tab<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#getlistdatabytabrequestresult" class="tsd-signature-type">getListDataByTabRequestResult</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">RowType</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="init" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> init</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">init<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-query.ts:172</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="isgetspecifictablistfunctype" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> is<wbr>Get<wbr>Specific<wbr>Tab<wbr>List<wbr>Func<wbr>Type</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">is<wbr>Get<wbr>Specific<wbr>Tab<wbr>List<wbr>Func<wbr>Type<span class="tsd-signature-symbol">(</span>func<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.getSpecificTabListFuncType</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">dto.ListTypes.getSingleTabPageListFuncType</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">func</span><span class="tsd-signature-symbol"> is </span><span class="tsd-signature-type">dto.ListTypes.getSpecificTabListFuncType</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-query.ts:164</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>func: <span class="tsd-signature-type">dto.ListTypes.getSpecificTabListFuncType</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">dto.ListTypes.getSingleTabPageListFuncType</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">func</span><span class="tsd-signature-symbol"> is </span><span class="tsd-signature-type">dto.ListTypes.getSpecificTabListFuncType</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="isinfirstpage" class="tsd-anchor"></a>
					<h3>is<wbr>InFirst<wbr>Page</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">is<wbr>InFirst<wbr>Page<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-query.ts:159</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="ispageindex" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> is<wbr>Page<wbr>Index</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">is<wbr>Page<wbr>Index<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.queryProps</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">params</span><span class="tsd-signature-symbol"> is </span><span class="tsd-signature-type">dto.ListTypes.queryPropsPage</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-query.ts:320</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.ListTypes.queryProps</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">params</span><span class="tsd-signature-symbol"> is </span><span class="tsd-signature-type">dto.ListTypes.queryPropsPage</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="updatecurrentpage" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> update<wbr>Current<wbr>Page</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Current<wbr>Page<span class="tsd-signature-symbol">(</span>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-query.ts:152</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>index: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>size: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updatefilterparam" class="tsd-anchor"></a>
					<h3>update<wbr>Filter<wbr>Param</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Filter<wbr>Param<span class="tsd-signature-symbol">(</span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, filters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.filter</span><span class="tsd-signature-symbol">[]</span>, item_index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, item_size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#updatefilterparamrequestresult" class="tsd-signature-type">updateFilterParamRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/list/list-query.ts:53</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>property: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>filters: <span class="tsd-signature-type">dto.filter</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
								<li>
									<h5>item_index: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>item_size: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/listtypes.html#updatefilterparamrequestresult" class="tsd-signature-type">updateFilterParamRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class tsd-has-type-parameter">
						<a href="listquery.html" class="tsd-kind-icon">List<wbr>Query</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class">
								<a href="listquery.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="listquery.html#api" class="tsd-kind-icon">api</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="listquery.html#currentpage" class="tsd-kind-icon">current<wbr>Page</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="listquery.html#donecallback" class="tsd-kind-icon">done<wbr>Callback</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="listquery.html#failedcallback" class="tsd-kind-icon">failed<wbr>Callback</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="listquery.html#ismeta" class="tsd-kind-icon">is<wbr>Meta</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="listquery.html#keyfieldvalue" class="tsd-kind-icon">key<wbr>Field<wbr>Value</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="listquery.html#listqueryparams" class="tsd-kind-icon">list<wbr>Query<wbr>Params</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="listquery.html#list_name" class="tsd-kind-icon">list_<wbr>name</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="listquery.html#pagescolumns" class="tsd-kind-icon">pages<wbr>Columns</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class">
								<a href="listquery.html#rawfilters" class="tsd-kind-icon">raw<wbr>Filters</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="listquery.html#shownrows" class="tsd-kind-icon">shown<wbr>Rows</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listquery.html#fullfillparams" class="tsd-kind-icon">fullfill<wbr>Params</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listquery.html#getallshownrowskeys" class="tsd-kind-icon">get<wbr>All<wbr>Shown<wbr>Rows<wbr>Keys</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listquery.html#getpagecounts" class="tsd-kind-icon">get<wbr>Page<wbr>Counts</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="listquery.html#getsingletabpagelist" class="tsd-kind-icon">get<wbr>Single<wbr>Tab<wbr>Page<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listquery.html#getspecificpagemeta" class="tsd-kind-icon">get<wbr>Specific<wbr>Page<wbr>Meta</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="listquery.html#getspecifictablist" class="tsd-kind-icon">get<wbr>Specific<wbr>Tab<wbr>List</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="listquery.html#init" class="tsd-kind-icon">init</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="listquery.html#isgetspecifictablistfunctype" class="tsd-kind-icon">is<wbr>Get<wbr>Specific<wbr>Tab<wbr>List<wbr>Func<wbr>Type</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listquery.html#isinfirstpage" class="tsd-kind-icon">is<wbr>InFirst<wbr>Page</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="listquery.html#ispageindex" class="tsd-kind-icon">is<wbr>Page<wbr>Index</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="listquery.html#updatecurrentpage" class="tsd-kind-icon">update<wbr>Current<wbr>Page</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="listquery.html#updatefilterparam" class="tsd-kind-icon">update<wbr>Filter<wbr>Param</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>