import * as api from "./api"
export class BindAccount {
    /**
     * 获取账号绑定状态
     *
     */
    public bindTeammix(token: string) {
        return api.bindTeammixAccount(token)
    }

    /**
     * 绑定teammix账号
     * @param token
     */
    public bindXiaoBao(authcode: string) {
        return api.bindXiaoBaoAccount(authcode)
    }

    /**
     * 绑定小宝账号
     * @param authcode
     */
    public getAccountBindStatus() {
        return api.getAccountBindStatus()
    }
}
