import type * as dto from "../def/index"

import { filterHumbleFilter } from "./filters-filter"
export function createFiltersHandler(
    rawFilters: dto.metaFilter[],
    leftTree?: dto.ListTypes.getListDataRequestResult["meta"]["tree"]
) {
    return function handleFilters(
        keyValueFilters: dto.ListTypes.KeyValueFilters
    ) {
        const createErrorMsg = (property: string, msg: string) =>
            new Error(`搜索参数：${property}的值${msg}`)
        const humblefilters = filterHumbleFilter(rawFilters ?? [])
        // const simpleRelationshipFilter = filterSimpleRelationshipFilter(
        //     this._listQuery.rawFilters ?? []
        // )
        const result = keyValueFilters
            .map((kvFilter) => {
                if (kvFilter.value == null) {
                    throw new Error(`搜索参数的value不能为null`)
                }
                //特殊处理左树筛选
                if (kvFilter.property === leftTree?.field) {
                    return {
                        type: "tree",
                        visible: true,
                        property: kvFilter.property,
                        full_property: kvFilter.property,
                        status: {
                            selectedList: [{ id: kvFilter.value }],
                            direct: false,
                        },
                        treeModelName: leftTree.treeModelName,
                    }
                }
                const wanted =
                    humblefilters.find((r) => {
                        const p = kvFilter.property
                        return r.name === p
                    }) ||
                    humblefilters.find((r) => {
                        const p = kvFilter.property
                        return r.full_property === p
                    }) ||
                    humblefilters.find((r) => {
                        const p = kvFilter.property
                        return r.property === p
                    })
                if (!wanted) {
                    throw new Error(
                        `未在页面元数据中找到筛选条件：${kvFilter.property}`
                    )
                }

                const type = wanted.type
                if (["date", "datetime"].includes(type)) {
                    if (!Array.isArray(kvFilter.value)) {
                        kvFilter.value = [kvFilter.value, kvFilter.value]
                    }
                    const _k = kvFilter as dto.ListTypes.DateFilter
                    return {
                        ...wanted,
                        status: _k.value,
                    }
                } else if (["combo_text"].includes(type)) {
                    if (typeof kvFilter.value !== "string") {
                        throw createErrorMsg(kvFilter.property, "不是string")
                    }
                    const _k = kvFilter as dto.ListTypes.ComboTextFilter
                    return {
                        ...wanted,
                        property: wanted.full_property,
                        status: _k.value,
                        match: _k.match,
                    }
                } else if (["text"].includes(type)) {
                    if (typeof kvFilter.value !== "string") {
                        throw createErrorMsg(kvFilter.property, "不是string")
                    }
                    const _k = kvFilter as dto.ListTypes.TextFilter
                    if (_k.match !== null) {
                        return {
                            ...wanted,
                            status: _k.value,
                            match: _k.match,
                        }
                    }
                    return {
                        ...wanted,
                        status: _k.value,
                    }
                } else if (
                    [
                        "text-date",
                        "text-month",
                        "enum_radio",
                        "checkbox-group",
                        "fulltext",
                        "combineFulltext",
                        "text-year",
                    ].includes(type)
                ) {
                    if (typeof kvFilter.value !== "string") {
                        throw createErrorMsg(kvFilter.property, "不是string")
                    }
                    return {
                        ...wanted,
                        status: kvFilter.value,
                    }
                } else if (type === "enum") {
                    let value = kvFilter.value
                    if (!Array.isArray(value)) {
                        value = [value]
                    }
                    return {
                        ...wanted,
                        status: value,
                    }
                } else if (type === "date_between") {
                    return {
                        ...wanted,
                        status: kvFilter.value,
                    }
                } else if (type === "number") {
                    const _k = kvFilter as dto.ListTypes.NumberFilter
                    if (Array.isArray(_k.value)) {
                        _k.value.min = _k.value[0]
                        if (typeof _k.value.min === "string") {
                            _k.value.min = +_k.value.min
                        }
                        _k.value.max = _k.value[1]
                        if (typeof _k.value.max === "string") {
                            _k.value.max = +_k.value.max
                        }
                    }
                    // if (
                    //     typeof _k.value.min !== "number" ||
                    //     typeof _k.value.max !== "number"
                    // ) {
                    //     throw createErrorMsg(
                    //         kvFilter.property,
                    //         "需为{min:number;max:number}或[number,number]"
                    //     )
                    // }
                    const { min, max } = _k.value
                    return {
                        ...wanted,
                        min,
                        max,
                    }
                } else if (
                    type === "search" ||
                    type === "intentSearch" ||
                    type === "memberSelect"
                ) {
                    const _k = kvFilter as dto.ListTypes.SearchFilter
                    if (!Array.isArray(_k.value)) {
                        throw createErrorMsg(
                            kvFilter.property,
                            "需为{index:number;range:object}"
                        )
                    }
                    const range = _k.value

                    return {
                        ...wanted,
                        range,
                    }
                } else if (type === "boolean") {
                    return {
                        ...wanted,
                        status:
                            // boolean filter 的 value 接受 Boolean/Number/数字型String 值, 如 true, false, 1, '1' 都可支持
                            kvFilter.value.toString() ===
                            (+kvFilter.value).toString()
                                ? kvFilter.value
                                : !!kvFilter.value,
                    }
                } else if (type === "cascader") {
                    if (!Array.isArray(kvFilter.value)) {
                        throw createErrorMsg(
                            kvFilter.property,
                            "需为的值需为string[]"
                        )
                    }
                    return {
                        ...wanted,
                        status: kvFilter.value,
                    }
                } else if (type === "tree") {
                    const treeFilter = kvFilter as dto.ListTypes.TreeFilter
                    return {
                        type: "tree",
                        visible: true,
                        property: kvFilter.property,
                        full_property: wanted.full_property,
                        status: {
                            selectedList: getTreeFilterList(treeFilter),
                            direct: false,
                        },
                        treeModelName: wanted.treeModelName,
                    }
                } else {
                    throw new Error(`未处理${type}类型的筛选条件`)
                }
            })
            .map((k) => ({
                ...k,
                visible: true,
                property: k.full_property,
            }))
        return result as unknown as dto.filter[]
    }
}

function getTreeFilterList(treeFilter: dto.ListTypes.TreeFilter) {
    if (!treeFilter.value) {
        return []
    }

    if (!Array.isArray(treeFilter.value)) {
        return [{ id: treeFilter.value }]
    }

    const array = treeFilter.value as unknown[]
    return array.map((id) => {
        return { id }
    })
}
