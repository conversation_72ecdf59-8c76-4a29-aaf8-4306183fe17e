/* eslint-disable @typescript-eslint/no-explicit-any */
// export type CreatedAllPresent<Type> = {
//     [Property in keyof Type]-?: Type[Property];
// };
import { AxiosAdapter, AxiosInstance, AxiosResponse, Method } from "axios"
export { Method } from "axios"

export type PromiseFunc<T> = (value: T | PromiseLike<T>) => void

export namespace NewSwaggerTypes {
    export type domainItem = {
        name: string
        title: string
    }
    export type gatewayList = unknown
    export type gateway = unknown
    export type DomainModelList = unknown
    export type DomainServicList = unknown
    export type DomainModel = {
        actions: Array<{
            name: string
            meta: {
                action_name: string
                label: string
                container: string
                canBatch: boolean
                behavior: string
            }
        }>
        lists: Array<{
            name: string
            meta: {
                label: string
                key_field: string
                actions: action[]
                bottom_actions: action[]
                field_groups: field_group
                miniDetailFlag: boolean
                title_template: string
                checkboxes: boolean
            }
        }>
        details: Array<{
            name: string
            meta: {
                label: string
                key_field: string
                title_template: string
                actions: string
                pages: unknown[]
            }
        }>
        pivottables: []
    }
}
export namespace ModelValidatorTypes {
    export type ModelValidations = {
        schemaErrors: unknown[]
        suggestions: string[]
        selfValidations: string[]
        updateTimeOfSchemaValidations: string
        updateTimeOfSelfValidations: string
    }
}

export namespace TagManagerTypes {
    export type TagGroup = {
        tagGroupName: string
        alias: string
        mark?: string
        tags: TagInfo[]
    }

    export type TagInfo = {
        tagGroupName: string
        tagName: string
        tagColor: string
    }

    export type TagFilter = {
        tagGroup: string
        matchMode?: "any" | "all"
        exclude?: boolean
        tags: string[]
    }

    export type queryApiResult = TagInfo[]
}
export namespace GroovyEditorTypes {
    export type queryApiResult = { title: string; text: Array<string> }
}
export namespace TemplateManagerTypes {
    export type apiConstructor = {
        subProjectName: string
        model_name: string
        targetName: string
        templateType: string
    }
    export type queryApiResult = {
        meta: unknown
        rows: TemplateItem[]
    }

    export type addApiParams = {
        name: string //模板名
        url: string // 模板上传之后的url
        pageName: string //
        fileName: string // 导出文件名
    }

    export type editApiParams = {
        name: string //模板名
        url: string // 模板上传之后的url
        pageName: string //
        fileName: string // 导出文件名
        uniplat_version: number
        id: number
    }

    export type delApiParams = {
        uniplat_version: number
        id: number
    }
    export type TemplateItem = {
        id: number
        create_time: string
        file_name_template: string
        model_name: string
        name: string
        page_name: string
        project_name: string
        target_name: string
        type: string
        url: string
        url_value: string
        uniplat_version: number
    }
}
//ETL start
export namespace ETLTypes {
    export type id = string

    export type getDataAPIResult = {
        columns: Array<string>
        rows: Array<unknown>
    }
}
//ETL end
//config center start
export namespace ConfigCenterTypes {
    export type CreateTableAPIParams = {
        dataSourceName: string
        sqlString: string
    }
    export type QueryAPIResult = {
        id: string
        label: string
        children: QueryAPIResult
    }[]

    export type createDataModel = {
        subProjectName: string
        dataSourceName: string
        tableName: string
        modelName: string
    }
    export type createTableSQLScriptAPIParams = {
        dataSourceName: string
        dbtype: string
        tableName: string
    }
    export type createTableSQLScriptRequestResult = {
        sql: string
    }
    export type createSchemaSQLScriptRequestResult = {
        href: string
    }
    export type createSchemaSQLScript = Omit<
        createTableSQLScriptAPIParams,
        "tableName"
    >
    export type depolyApiResult = {
        msg: string
    }
}

//config center end
// utils start
export namespace Tools {
    type SlaveFilterItemValue = {
        status: unknown
        match?: ListTypes.filterMatchType
        typeValues?: string[]
        [key: string]: unknown
    }
    export type SlaveFilterItem = {
        typeProperty?: string
        modelName: string
        referName: string
        property: string
        text_filters?: SlaveFilterItemValue[]
        search_filters?: SlaveFilterItemValue[]
        boolean_filters?: SlaveFilterItemValue[]
        enum_filters?: SlaveFilterItemValue[]
        [key: string]: unknown
    }
    export type date_filter = { property: string; start: string; end: string }
    export type text_filter = { property: string; status: string }
    export type combo_text_filter = { fields: string; status: string }
    export type date_between_filter = {
        date: string
        startDate: string
        endDate: string
    }
    export type number_filter = { property: string; start: string; end: string }
    export type search_filter = {
        property: string
        range: string[]
        include: string
    }
    export type boolean_filter = { property: string; status: string }
    export type enum_filter = { property: string; status: string }
    export type cascader_filter = {
        filed_values: {
            [key: string]: string
        }
    }
    export type full_text_filter = { property: string; status: string }
    export type combine_full_text_filter = {
        properties: string
        status: string
    }
    export type tree_filter = {
        field: string
        treeModelName: string
        nodeId: unknown
        direct: boolean
        is_param: unknown
    }

    export type workflow_process_name_filter = {
        status: string
    }

    export type workflow_process_state_filter = {
        status: string
    }

    export type workflow_process_task_filter = {
        status: string
    }

    export type workflow_process_name_multi_filter = {
        status: string
    }

    export type workflow_process_state_multi_filter = {
        status: string
    }

    export type workflow_process_task_multi_filter = {
        status: string
    }

    export type workflow_instance_state_filter = {
        status: string
    }
    export type simple_relationship_filters = {
        model: string
        entityIds: string[]
        predicate: string
    }

    export type FiltersType = {
        date_filters: date_filter[]
        datetime_filters: date_filter[]
        text_filters: text_filter[]
        combo_text_filters: combo_text_filter[]
        date_between_filters: date_between_filter[]
        number_filters: number_filter[]
        search_filters: search_filter[]
        boolean_filters: boolean_filter[]
        enum_filters: enum_filter[]
        cascader_filters: cascader_filter[]
        full_text_filters: full_text_filter[]
        combine_full_text_filters: combine_full_text_filter[]
        tree_filters: tree_filter[]
        workflow_process_name_filters: workflow_process_name_filter[]
        workflow_process_state_filters: workflow_process_state_filter[]
        workflow_process_task_filters: workflow_process_task_filter[]
        workflow_instance_state_filters: workflow_instance_state_filter[]
        simple_relationship_filters: simple_relationship_filters[]
        workflow_process_name_multi_filters: workflow_process_name_multi_filter[]
        workflow_process_state_multi_filters: workflow_process_name_multi_filter[]
        workflow_process_task_multi_filters: workflow_process_task_multi_filter[]
        slave_filters?: SlaveFilterItem[]
    }
}

// utils end
//ModelSchemaManage start
export namespace ModelSchemaManagerTypes {
    export type ConstructorParams = {
        model_name: string
        type: SchemaType
        targetName: string
    }

    export type addAPIParams = {
        name: string
        content: any
    }
    export type saveAPIParams = {
        v: number
        id: number
        content: string
        name: string
    }
    export type addSureAPIParams = {
        newName: string
        content: string
    }
    export type shareAPIParams = {
        schemaId: number
        id: string
    }
    export enum SchemaType {
        List = 1,
        Pivottable = 2,
    }

    export const enum SchemaItemType {
        System = 1,
        Custom,
    }

    //is_default是V1的
    //不知为何后端欧豪修改为default
    // 又改成isDefault
    //为了兼容两个都保留
    export type SchemaItem = {
        content: string
        id: number
        is_default: boolean
        isDefault: boolean
        name: string
        v: number | string
        type: SchemaItemType
    }
}

//ModelSchemaManage end

//ui config editor start
export namespace UIConfigTypes {
    export enum EnumType {
        isNOT = 0,
        isBoolean,
        isSchemaEnum,
        isLocalEnum,
        isServerEnum,
        isRequestEnum,
    }

    export type SchemaNode = {
        key: string
        type: string
        required: boolean
        enumType: EnumType
        enum?: Array<string>
        enumServer?: string
        enumLocal?: string
        enumRequest?: string
        default?: unknown
        definedProperties: Array<string>
        title: string
        description: string
        editor?: string
    }

    export type queryRequestResult = {
        enums: { [key: string]: unknown }
        filename: string
        metaSource: string
        schema: Array<string>
        text: Array<string>
        title: string
    }
}

//ui config editor end
//scene start
export namespace SceneTypes {
    export type SceneConfig = {
        type: "list" | "tree"
        placeholder: { label: string; search: string }
        fields: { label: string; template: string; width?: string }[]
    }

    export type SceneData = {
        key: string
        label: string
        data: unknown
        fields: string[]
    }

    export type SceneDatas = {
        [name: string]: SceneData
    }

    export type SceneDataList = {
        list: SceneData[]
        limit_begin: number
        limit_size: number
        record_count: number
    }

    export type LoadListApiParams = {
        scene: string
        keyword: string
        itemIndex: number
        itemSize: number
        parent?: string
    }
}

//scene end

// org start
export namespace Org {
    export type Org = {
        id: number
        name: string
        type: string
        certified: boolean
        creator: number
        createTime: string
    }

    export type ApplicationOrg = Org & {
        title: string
        application: string
        oid: string
        poid: string
        instanceId: string
        enabled: boolean
    }

    export type OrgList<T extends Org> = {
        list: T[]
        count: number
    }

    export type LoadListApiParams = {
        name: string
        itemIndex: number
        itemSize: number
        application?: string
        oid?: string | number
    }
}
// org end

// global search start
export namespace GlobalSearchTypes {
    export type searchApiParams = {
        keyword: string
        pageIndex: number
    }
    export type searchRequestResult = {
        searchResult: {
            hits: {
                totalHits: {
                    value: number
                }
            }
        }
        render: string
    }

    export type ApplicationSearchParameter = {
        keyword: string
        types: string[]
        item_index: number
        item_size: number
        domain?: string
        model_name?: string
        list_name?: string
    }

    export type ApplicationSearchResult = {
        lists: {
            domain: string
            modelName: string
            listName: string
            [key: string]: unknown
            result: unknown
        }[]
    }
}

// global search end
// password box start
export namespace PasswordBoxTypes {
    export type addAPIParams = {
        name: string
        password: string
    }
}
// password box end
// swagger box start
export namespace SwaggerTypes {
    export type queryResult = {
        name: string
        label: string
        id: string
        datamodels: {
            actions: {
                label: string
                name: string
                inputs: {
                    label: string
                    property: string
                    type: string
                }[]
            }[]
            customs: {
                description: {
                    description: string
                    methods: []
                    parameters: []
                }
                interfaceName: string
            }[]
            name: string
            queries: []
        }[]
    }[]

    export type requestParams = {
        methodName: Method
        projectname: string
        model_name: string
        interfaceName: string
        label: string
    }
}

// swagger box end
// index search start
export namespace IndexSearchTypes {
    export type SearchRequestResult = GlobalSearchTypes.searchRequestResult
    export type SearchApiParams = {
        searchText: string
        searchType: string
        pageIndex: number
    }
    export type ExportDataApiParams = {
        searchText: string
        searchType: string
    }
    export type exportZipApiParams = ExportDataApiParams
}

// index search end

// pivottable start
export namespace PivotTableTypes {
    export type queryParams = {
        prefilters: prefilter[]
    }
    export type queryResult = {
        title_template: string
        labe_width: number
        values: Array<{
            property: string
            summary: string
            fullName: string
        }>
        rows: Array<{
            property: string
            kind: string
            fullName: string
        }>
        columns: Array<{
            property: string
            kind: string
            fullName: string
        }>
        filters: Array<{
            property: string
            template: string
            value: unknown
        }>
        sub_project: string
        name: string
        label: string
        fields: Array<HumbleFilter>
        exportBtnEnable: boolean
        [key: string]: unknown
    }
    export type refreshAPIParams = {
        filters: Tools.FiltersType
        prefilters: prefilter[]
        values: unknown
        rows?: unknown
        columns?: unknown
    }

    export type refreshResult = {
        total: Array<{
            [key: string]: {
                display: unknown
                emptyValue: boolean
                value: unknown
            }
        }>
        columns: Array<{
            columns: null
            property: string
            value: {
                display: unknown
                emptyValue: boolean
                value: unknown
            }
        }>
        rows: Array<{
            [key: string]: {
                display: unknown
                emptyValue: boolean
                value: unknown
            }
        }>
    }
    export type getDetailAPIParams = {
        filters: Tools.FiltersType
        prefilters: prefilter[]
        values: unknown
        groupFilters: unknown
        item_index: number
        item_size: number
    }

    export type getDetailRequestResult = {
        rows: metaRow[]
        item_size: number
        item_index: number
        record_count: number
        key_field: string
    }

    export type exportAPIExcelParams = refreshAPIParams

    export type refreshParams = {
        filters?: filter[]
        prefilters?: prefilter[]
        groupFields?: unknown[]
        summaryFields?: unknown[]
    } & Omit<
        refreshAPIParams,
        "filters" | "prefilters" | "groupFields" | "summaryFields"
    >

    export type getDetaiParams = Omit<
        getDetailAPIParams,
        "filters" | "prefilters" | "groupFields" | "summaryFields"
    >
}

// pivottable end

// role start
export namespace RoleTypes {
    export type getRoleUserVersionRequestResult = {
        meta: Meta
        row: metaRow & {
            action: action[]
        }
    }
    export type getRoleVersionRequestResult = getRoleUserVersionRequestResult
    export type RoleRight = {
        create_time: string
        entry_name: string
        id: number
        role_id: number
        uniplat_version: number
    }

    export type right = {
        id: number
        label: string
        children: right[]
    }
    export type getGlobalRightsTree = right[]
    export type role = {
        create_time: string
        id: number
        role_code: string
        name: string
        useplace: string
    }
    export type queryRequestResult = role[]
    export type getAuthUsers = {
        id: number
        org_name: string
        role_name: string
        user_id: number
        user_name: string
    }[]

    export type SaveRightsChangeParams = {
        role_id: number
        add_entrys: string[]
        remove_entrys: string[]
    }

    export type DataSource = {
        value: string
        label: string
    }

    export type Entry = {
        name: string
        label: string
        domain: string
        model: string
        datasource: string
        self: boolean
        sourceRoleIds?: string[]
    }

    export type EntryRight = {
        domain: string
        model: string
        datasource: string
        name: string
        self?: boolean
        selfDatasource?: string[]
        sourceDatasource?: string[]
        sourceRoleIds?: string[]
        type?: string
    }

    export type UpdateRoleEntryRightParams = {
        role_id: number
        add_entries?: EntryRight[]
        remove_entries?: EntryRight[]
        entry_type?: string
    }
}

// role end

//dashboard start
export namespace DashboardTypes {
    export type chatData = {
        dataset: {
            dimensions: string[]
            source: []
        }
        layout: {
            left: number
            right: number
            top: number
            bottom: number
        }
        model_names: string[]
        option: {
            [key: string]: unknown
        }
        type: string
    }

    export type getDashboard = {
        lastModified: number
        dashboard: chatData[]
    }
    export type refreshSpecifyChat = chatData
}

//dashboard end

//detail start
export namespace DetailTypes {
    export type SmartQueryResult<
        RowType extends ListRow = ListRow,
        LogRow extends DetailTypes.GetLogsRequestRow = DetailTypes.GetLogsRequestRow
    > = getDetailRequestResult<RowType, LogRow>
    export type SmartQueryResult2<
        LogRow extends DetailTypes.GetLogsRequestRow = DetailTypes.GetLogsRequestRow
    > = getDetailRequestResult2<LogRow>

    export type detailsQueryParam = {
        name: string
        itemIndex: number
        itemSize: number
    }
    export type constructorType = {
        model_name: string
        keyValue: string | number
        detailName?: string
    }

    export type GetLogsRequestRow = {
        [key: string]: string | number | boolean | null
    }
    export type GetLogsRequest<
        T extends GetLogsRequestRow = GetLogsRequestRow
    > = {
        columns: string[]
        page_count: number
        page_index: number
        record_count: number
        rows: T[]
    }
    export type FieldGroup = {
        align: string
        autoHeight: boolean
        canSort: boolean
        catalog: string
        description: string
        fields: field[]
        fixed: string
        label: string
        label_width: number
        optional: boolean
        span: number
        template: string
        visible: true
        tagGroups: string[]
        tagsStyle: Array<"tag" | "color-text" | "text">
        tagsOnNewLine: boolean
        renderMode: "html" | "text" | "pre"
        actions: action[]
        intents: RowIntent[]
    }
    export type section = {
        actions: action[]
        intents: RowIntent[]
        dialogPages: any[]
        dialog_width: string
        expansion: boolean
        fieldGroupsSpan: number
        field_groups: FieldGroup[]
        label: string
        pageGroupsSpan: number
        page_groups: any[]
        span: number
    }

    export type getDetailAPIParams = {
        log?: {
            showLog: boolean
            pageIndex: number
        }
        details?: detailsQueryParam[]
    }
    export type getDetailRequestResult<
        T extends ListRow = ListRow,
        LogRow extends GetLogsRequestRow = GetLogsRequestRow
    > = {
        details: {
            [key: string]: ListTypes.getListDataRequestResult
        } | null
        log: GetLogsRequest<LogRow> | null
        meta: Meta["meta"]
        row: metaRow<T> & {
            action: action[]
        }
        subProjectName: string
    }

    export type getDetailRequestResult2<
        LogRow extends GetLogsRequestRow = GetLogsRequestRow
    > = {
        details: {
            [key: string]: ListTypes.getListDataRequestResult
        } | null
        log: GetLogsRequest<LogRow> | null
        meta: Meta["meta"]
        row: metaRow2
        subProjectName: string
    }

    export type createExportUrl = {
        export_template?: string
        template_name?: string
    }
}

export enum IntentContainer {
    Sidebar = "sidebar",
    Dialog = "dialog",
    Page = "page",
    NewTab = "tab",
    RedirectNewTab = "redirectTab",
    SelfTab = "self-tab",
}

export enum IntentAction {
    Chat = "openChat",
    Workflow = "showWorkflows",
    WorkflowList = "showWorkFlowList",
    Table = "showList",
    Detail = "showDetail",
    Action = "execute",
    Actions = "executes",
    OpenWorkflowDetail = "openWorkflowDetail",
    ShowLogList = "showLogList",
    ExportTargetList = "exportTargetList",
    ExportTargetDetail = "exportTargetDetail",
    ExportWorkflowList = "exportWorkflowList",
    OpenWorkflowOperator = "openWorkflowOperator",
    StartProcess = "startProcess",
    ShowReport = "showReport",
    ShowComponent = "showComponent",
}

export type RowIntent = {
    enabled: number
    container: IntentContainer
    name: string
    label: string
    when: string
    action: IntentAction
    parameters: { [key: string]: any }
    model: string
    // 工作流
    processModelName?: string
    icon?: string
}

export type NotifyMeta = {
    label: string
    modelName: string
    listName: string
    prefilters: prefilters
    [key: string]: unknown
}

// detail end
//universal start

export const enum MessageType {
    Text = "text",
    Image = "image",
    File = "file",
    Video = "video",
    Voice = "voice",
    GeneralOrderMsg = "general_order_msg",
    Withdraw = "withdraw",
    MyPurchasePlan = "my_purchase_plan",
    MyWelfare = "my_welfare",
    QuestionAnswer = "question_answer",
    Action = "action",
    Notify = "notify",
    MpNavigate = "mp-navigate",
    PayV1 = "gpay",
    Pay = "gpay2",
    PayResult = "gresult",
    RefundV1 = "grefund",
    Refund = "grefund2",
    Card = "card",
    Position = "position",
    NavigateCard = "navigate-card",
    JobDescription = "jd",
    PlatformShare = "platform-share",
    PlatformBasicNotify = "basic_notify",
    Notice = "notice",
}

export type Meta = {
    meta: {
        actions: action[]
        intents: RowIntent[]
        bottom_actions: action[]
        header: {
            actions: action[]
            field_groups: DetailTypes.FieldGroup[]
        }
        key_field: string
        label: string
        logBtnEnable: boolean
        pages: {
            label: string
            name: string
            sections: DetailTypes.section[]
            list?: {
                list_name: string | null
                name: string
                prefilters: prefilters
            }
            workflow_list?: {
                list_name: string | null
                name: string
                prefilters: prefilters
            }
        }[]
        name: string
        sections: DetailTypes.section[]
        show_workflow: boolean
        template_names: string[]
        title_template: string
        exportBtnEnable: boolean
        modelName: string
        system_templates?: {
            generated_file_name: string
            name: string
            path: string
        }[]
        notifies?: NotifyMeta[]
        chatMeta?: {
            msgActions: {
                [key in MessageType]: []
            }
        }
    }
}

export type action = {
    action_name: string
    authed: boolean
    back_image: string
    behavior: string
    canBatch: boolean
    col_count: number
    container: string
    css: string
    enabled: boolean
    forward: string
    label: string
    label_position: string
    label_width: number
    need_footer: boolean
    need_title: boolean
    on: string
    open_in_new_tab: boolean
    prompt: string
    size_percent: number
    uniplat_version: number
    icon?: string
}
export type simpleRelationshipFilter = {
    label: string
    type: string
    model: string
    entityIds: string[]
    predicates: Array<{
        text: string
        value: string
    }>
}
export type HumbleFilter = {
    defaultValue: string
    ext_properties: unknown
    full_property: string
    label: string
    property: string
    name?: string
    trim: boolean
    type: string
    width: number
    treeModelName: string
    optionalValues?: Array<{
        label: string
        value: string | { start: string; end: string }
    }>
}
// export type simpleRelationshipFilter = any
export type metaFilter = simpleRelationshipFilter | HumbleFilter

export type field = {
    ext_properties: unknown
    full_property: string
    label: string
    property: string
    type: string
    width: number
}
export type field_group = {
    fields: field[]
    calculators: string[]
    label: string
    label_width: number
    span: number
    align: "left" | "center" | "right"
    fixed: "left" | "right"
    auto_height: boolean
    template: string
    sumTemplate: string
    dynamicField: string
    catalog: string
    description: string
    optional: boolean
    visible: boolean
    /**
     * 列级别的 action
     */
    actions?: []
    [key: string]: unknown
}
export type ListRow = {
    [key: string]: [unknown, unknown]
}
export type RowTypeMapper<T extends ListRow> = {
    [property in keyof T]: {
        display: T[property][0]
        value: T[property][1]
        emptyValue: boolean
    }
}
export type metaRow<T extends ListRow = ListRow> = RowTypeMapper<T> & {
    tags: { [key: string]: TagManagerTypes.TagInfo[] }
    actions: action[]
    intents: RowIntent[]
    uniplat_version: {
        value: number
        display: number
    }
}

export type metaRow2 = {
    tags: { [key: string]: TagManagerTypes.TagInfo[] }
    actions: action[]
    intents: RowIntent[]
    uniplatVersion: number
    keyValue: number | string
    chat: { [key: string]: unknown }
    workflowInfo: {
        total: number
        runnning: number
        finish: number
        instances: unknown[]
    }
    fieldGroups: {
        [key: string]: {
            value: string
            actions: action[]
            intents: RowIntent[]
            historyVersion: unknown
            tagEditor: unknown
            miniEditor: unknown
        }
    }
    objectData?: {
        [key: string]: string | number | unknown
    }
    detailName: string
    accessKey: string
    [key: string]: unknown
}

export enum order {
    ascending = "ascending",
    descending = "descending",
}

export type orderObj = {
    column?: string
    order?: order
}

export type uuid = string

export type prefilter = {
    property: string
    value: unknown
}

export type ActionIntent = { [key: string]: any }

export type prefilters = prefilter[]
export type column = "*" | string

export type filter = {
    label: string
    /**用于描述该字段的类型 */
    type:
        | "boolean"
        | "date"
        | "number"
        | "enum"
        | "cascader"
        | "text"
        | "text-date"
        | "text-month"
        | "combo_text"
        | "search"
        | "date_between"
        | "checkbox-group"
        | "enum_radio"
        | "fulltext"
        | "combineFulltext"
        | "tree"
        | "intentSearch"
    property: string[] | string
    width: number
    min: string
    max: string
    ext_properties: {
        [key: string]: unknown
    }
    status: string[]
    trim: boolean
    pageName: string
    visible: boolean
    is_param: boolean
    uniKey: string
    strategy?: string
}

export type UserInfoChangedEventListener = (userInifo: {
    readonly username: string
    readonly isSuperAdmin: boolean
    readonly jwtToken: string
}) => void
export type UniversalErrorCallback = (msg: string, res?: AxiosResponse) => void
export type UniversalErrorResponseCallback = (r: any) => void
// universal end

// workflow start
export namespace WorkflowTypes {
    export enum WorkflowTabs {
        NotDeal = "NotDeal",
        Dealing = "Dealing",
        Finished = "Finished",
        Statistics = "Statistics",
    }

    export type getProcessInfoParams = {
        ids: number[]
        tabName: WorkflowTabs
    }
    export type createWorkflow = {
        flowStatus: string
        remark: string
        operator: string
        priority: string
        endDate: string
        assignNum: string
        selectRadioType: 0 | 1
        processName: string //工作流名字
        // allFilters: unknown;
    }
    export type updateWorkflow = {
        flowStatus: string //工作流节点
        remark: string //备注
        operator: string //处理人
        priority: string //优先级
        endDate: string
        processId: unknown
    }
}

// workflow end

//list start
export namespace ListTypes {
    export type getSpecificTabListFuncType<RowType extends ListRow = ListRow> =
        (
            tabName: string,
            item_index: number,
            item_size: number,
            listQueryParams?: ListTypes.queryProps
        ) => Promise<ListTypes.getListDataByTabRequestResult<RowType> | null>
    export type getSpecificTabListFuncType2 = (
        tabName: string,
        item_index: number,
        item_size: number,
        listQueryParams?: ListTypes.queryProps
    ) => Promise<ListTypes.getListDataByTabRequestResult2 | null>
    export type getSingleTabPageListFuncType<
        RowType extends ListRow = ListRow
    > = (
        item_index: number,
        item_size: number,
        listQueryParams?: ListTypes.queryProps
    ) => Promise<ListTypes.getListDataOfSinglePageRequestResult<RowType>>

    export type getSingleTabPageListFuncType2 = (
        item_index: number,
        item_size: number,
        listQueryParams?: ListTypes.queryProps
    ) => Promise<ListTypes.getListDataOfSinglePageRequestResult2>

    export type IQueryResult<RowType extends ListRow = ListRow> = {
        pageData: ListTypes.getListDataRequestResult<RowType>
        getList(
            tabName: string,
            item_index: number,
            item_size?: number,
            listQueryParams?: ListTypes.queryProps
        ): Promise<ListTypes.getListDataByTabRequestResult<RowType> | null>
        getList(
            item_index: number,
            item_size?: number,
            listQueryParams?: ListTypes.queryProps
        ): Promise<ListTypes.getListDataOfSinglePageRequestResult<RowType>>
        getList(
            a: string | number,
            b?: number,
            c?: number | ListTypes.queryProps,
            d?: ListTypes.queryProps
        ):
            | Promise<ListTypes.getListDataByTabRequestResult<RowType> | null>
            | Promise<ListTypes.getListDataOfSinglePageRequestResult<RowType>>
        pageCount?: ListTypes.getListCountRequestResult
    }

    export type IQueryResult2 = {
        pageData: ListTypes.getListDataRequestResult2
        getList(
            tabName: string,
            item_index: number,
            item_size?: number,
            listQueryParams?: ListTypes.queryProps
        ): Promise<ListTypes.getListDataByTabRequestResult2 | null>
        getList(
            item_index: number,
            item_size?: number,
            listQueryParams?: ListTypes.queryProps
        ): Promise<ListTypes.getListDataOfSinglePageRequestResult2>
        getList(
            a: string | number,
            b?: number,
            c?: number | ListTypes.queryProps,
            d?: ListTypes.queryProps
        ):
            | Promise<ListTypes.getListDataByTabRequestResult2 | null>
            | Promise<ListTypes.getListDataOfSinglePageRequestResult2>
        pageCount?: ListTypes.getListCountRequestResult
    }

    export type setColumnsForPagesParams = {
        page_name: string
        columns: string[]
    }
    export type setColumnsForPagesApiResult = {
        name: string
        title: string
        prefilters: prefilters
        actions: action[]
        filters: filter[]
        field_groups: field_group[]
        bottom_actions: action[]
        checkboxs: boolean
    }

    export type PrefiltersObject = {
        [property: string]: unknown
    }
    export enum filterMatchType {
        start = "start",
        fuzzy = "fuzzy",
        exact = "exact",
    }
    export type KeyValueFilter = {
        property: string
    }
    export type DateFilter = KeyValueFilter & {
        value: [string, string]
    }
    export type TextFilter = KeyValueFilter & {
        value: string
        match?: filterMatchType
    }
    export type TextDataFilter = TextFilter
    export type TextMonthFilter = TextFilter
    export type EnumradioFilter = TextFilter
    export type ComboTextFilter = KeyValueFilter & {
        value: string
        match?: filterMatchType
    }
    export type DateBetweenFilter = KeyValueFilter & {
        value: string
    }
    export type NumberFilter = KeyValueFilter & {
        value: {
            min: number
            max: number
        }
    }
    export type SearchFilter = KeyValueFilter & {
        value: unknown[]
    }
    export type BooleanFilter = KeyValueFilter & {
        value: boolean
    }
    export type Enumfilter = KeyValueFilter & {
        value: Array<unknown>
    }
    export type CheckBoxfilter = Enumfilter

    export type CascaderFilter = KeyValueFilter & {
        value: string[]
    }
    export type FulltextFilter = KeyValueFilter & {
        value: string
    }
    export type CombineFulltextFilter = FulltextFilter
    export type TreeFilter = KeyValueFilter & {
        value: unknown[] | unknown
    }

    export type KeyValueFilters = Array<
        | DateFilter
        | TextFilter
        | TextDataFilter
        | TextMonthFilter
        | EnumradioFilter
        | ComboTextFilter
        | DateBetweenFilter
        | NumberFilter
        | SearchFilter
        | BooleanFilter
        | Enumfilter
        | CheckBoxfilter
        | CascaderFilter
        | FulltextFilter
        | CombineFulltextFilter
        | TreeFilter
    >

    export type getListDataByTabParams = {
        item_index: number
        item_size: number
        name?: string
        filters: unknown
        prefilters: prefilters
        tagFilters: TagManagerTypes.TagFilter[]
        order_obj: orderObj
        columns: column[]
    }

    export type getListDataOfSinglePageParams = {
        item_index: number
        item_size: number
        name?: string
        filters: unknown
        prefilters: prefilters
        order_obj: orderObj
        columns: column[]
        tagFilters: TagManagerTypes.TagFilter[]
    }

    export type filterGroup = {
        by_date: string
        columns: {
            ext_properties: unknown
            full_property: string
            label: string
            property: string
            type: string
            width: number
        }[]
    }

    export type updateActionRequestResult = {
        actions: {
            action_name: string
            forward: string
        }[]
    }

    export type BaseListDataRequestResult = {
        information: string | null
        customSummaries: { [key: string]: any }
        customProperties: { [key: string]: any }
        operator: null
        meta: {
            systemSchemes: Array<{
                name: string
                content: string
            }>
            lists: unknown[]
            miniDetailExpandFirst: boolean
            actions?: action[]
            bottom_actions?: action[]
            checkboxes: boolean
            detail_action_visible: boolean
            log_action_visible: boolean
            group_sums: boolean
            key_field: string
            label: string
            miniDetailFlag: boolean
            title_template: string
            field_groups: field_group[]
            filters: metaFilter[]
            normalFilters: string[]
            intents: RowIntent[]
            pages: {
                name: string
                actions: action[]
                intents: RowIntent[]
                bottom_actions: action[]
                checkboxs: boolean
                field_groups: field_group[]
                [key: string]: unknown
            }[]
            sortDefMetas?: {
                label: string
                property: string
                type: "desc" | "asc"
            }[]
            tree?: {
                field: string
                treeModelName: string
                summary: {
                    modelName: string
                    field: string
                }
                searchProperties?: string[]
                depth?: number
                [key: string]: unknown
            }
            tree_with_data?: {
                treeModelName: string
                template: string
                dataModelName: string
                foreignKey: string
                treeForeignKey: string
                fields: string[]
            }
            row_actions: action[]
            exportBtnEnable: boolean
            manualLoadData: boolean
            export_template?: any[]
            template_names?: string[]
            modelName: string
            showActionsAndIntentsLimit: number
            top_section_refs: string[]
            section_refs: string[]
            bottom_section_refs: string[]
            notifies?: NotifyMeta[]
        }
        title?: string
        tagGroups: TagManagerTypes.TagGroup[]
        subProjectName: string
        summary: string
        states: any
    }

    type PageData = {
        item_index: number
        item_size: number
        name: string
        record_count: number
        rows: []
        system_templates?: {
            name: string
            path: string
            pageName: string
        }[]
    }

    export type getListDataRequestResult<T extends ListRow = ListRow> =
        BaseListDataRequestResult & {
            rows: metaRow<T>[]
            recordCount4Tabs: any
            record_count: number
            summaries: string[]
            system_templates: any[]
            page_datas?: { [key: string]: PageData }
            item_index: number
            item_size: number
        }

    export type getListDataRequestResult2 = BaseListDataRequestResult & {
        rows: metaRow2[]
        recordCountForTabs: any
        recordCount: number
        summaries: { [key: string]: string }
        systemTemplates: any[]
        pageDatas?: { [key: string]: PageData }
        itemIndex: number
        itemSize: number
    }

    export type getListCountRequestResult = {
        page_datas?: {
            [key: string]: {
                item_index: number
                item_size: number
                name: string
                record_count: number
                rows: []
            }
        }
    }

    export type getListDataByTabRequestResult<T extends ListRow = ListRow> = {
        customSummaries: { [key: string]: string | number }
        item_index: number
        item_size: number
        name: string
        record_count: number
        rows: metaRow<T>[]
        summaries: string[]
        summary: string
    }

    export type getListDataByTabRequestResult2 = {
        customSummaries: { [key: string]: string | number }
        itemIndex: number
        itemSize: number
        name: string
        recordCount: number
        rows: metaRow2[]
        summaries: { [key: string]: string }
        summary: string
    }

    export type getListDataOfSinglePageRequestResult<
        T extends ListRow = ListRow
    > = getListDataRequestResult<T>

    export type getListDataOfSinglePageRequestResult2 =
        getListDataRequestResult2

    export type updateFilterParamRequestResult = {
        property: string
        [key: string]: unknown
    }[]
    export type constructor = {
        model_name: string
        list_name?: string
    }
    export type exportToExcel = {
        name?: string
        filters: Tools.FiltersType
        prefilters: prefilters
        tagFilters: TagManagerTypes.TagFilter[]
        order_obj: orderObj
        page_name?: string
    }

    export type updateAction = {
        pageName?: string
        selectedList: unknown
        actionParams?: unknown
    }
    export type getWorkflow = {
        ids: number[]
        tabName: string
    }
    export type initProps = {
        name?: string
        item_index: number
        item_size: number
        prefilters?: prefilters
        columns?: column[]
        order_obj?: orderObj
        filters?: Tools.FiltersType
        tagFilters?: TagManagerTypes.TagFilter[]
    }

    export type queryPropsEasy = Omit<queryPropsPage, "filters"> & {
        filters?: KeyValueFilters
    }
    export type queryPropsHard = Omit<queryPropsIndex, "filters"> & {
        filters?: filter[]
    }

    export type queryPropsBase = {
        item_size: number
        tagFilters?: TagManagerTypes.TagFilter[]
        prefilters?: prefilters
        columns?: column[]
        order_obj?: orderObj
        filters?: KeyValueFilters | Array<filter | TreeTypes.filter>
        sorts?: { property: string; type: "desc" | "asc" }[]
        workflowType?: string
        tabName?: string
        filters4Workflow?: unknown
        router?: string
        fields?: string[]
        slave_filters?: Tools.SlaveFilterItem[]
    }
    export type queryPropsPage = queryPropsBase & {
        pageIndex: number
    }
    export type queryPropsIndex = queryPropsBase & {
        item_index: number
    }

    export type queryProps = queryPropsPage | queryPropsIndex

    export type fullfilledQueryProps = {
        item_index: number
        item_size: number
        tagFilters: TagManagerTypes.TagFilter[]
        prefilters: prefilters
        columns: column[]
        order_obj: orderObj
        filters: filter[]
        slave_filters?: Tools.SlaveFilterItem[]
        sorts: { property: string; type: "desc" | "asc" }[]
        workflowType?: string
        tabName?: string
        filters4Workflow?: unknown
        router: string
        fields?: string[]
    }
    export type exportExcelParams = {
        tabName?: string
        workflowType?: string
        page_name?: string
        export_template?: string
        template_name?: string
        pages?: IPage[]
        item_index?: number
        item_size?: number
    }
    export type IPage = {
        pageName: string
        columns: string[]
    }
}
// list end
export namespace OAuthLoginTypes {
    export type teammixOauthLoginApiResult = {
        username: string
        jwt: string
    }
    export type hrsOauthLoginApiResult = {
        username: string
        jwt: string
        orgId: string
    }

    export type hrsOauthLoginApiParams = {
        token: string
        hrsEmpOpenId: string
    }
}
// sdk index start

export namespace Index {
    export type injectDependencyParams = {
        broadcastChannel?: unknown
    }

    export type loginRequestResult = {
        isSuperUser: boolean
        jwt: string
        passwordChanged: boolean
    }
    export type loginByPassportRequestResult = {
        firstName: string
        isSuperUser: boolean
        jwt: string
        username: string
    }

    export type BindPassportRequestResult = {
        username: string
        isSuperUser: boolean
        jwt: string
    }
    export type BindPassportParams = {
        token: string
        username: string
        password: string
    }
    export type authLogin = {
        username: string
        isSuperUser: boolean
        jwt: string
    }
    export type authLoginBindParams = {
        authcode: string
        username: string
        password: string
    }

    export type authLoginBind = {
        username: string
        isSuperUser: boolean
        jwt: string
    }

    export type bindStatusRequestResult = {
        create_time: string
        id: number
        scene: string
        scene_user_id: string
        user_id: number
    }[]

    export type refreshTokenRequestResult = {
        jwt: string
    }
    export type getUserInfo = {
        id: number
        firstName?: string
        isSuperUser: boolean
        username: string
    }

    export type reloadApiResult = void

    export type BaseApplicationConfig = {
        projectTitle: string
        slogan: string
        showLoginEntrance: boolean
        loginEntranceTitle: string
        customHomeRedirect: boolean
        customHomeRedirectPath: string
        loginBg: string
        /** 左侧登录slogan相关配置。可不配置，不配置的话会有默认配置 */
        loginSlogan?: {
            /** 是否显示slogan图标，默认显示，若不想要，需要配置为 false */
            show?: boolean
            /** slogan 图标地址，有默认值，可不配置 */
            url?: string
        }
        /** 是否显示slogan图标，默认显示，若不想要，需要配置为 false */
        loginMethod?: {
            /** 是否启用用户名密码登录，不配置默认true，若不想要，需要设置为 false */
            usernamePassword?: boolean
            /** 是否启用老小保后台登录，不配置默认true，若不想要，需要设置为 false */
            qqxbBackLogin?: boolean
            /** 是否启用老Passport登录，不配置默认true，若不想要，需要设置为 false */
            oidc?: boolean
        }
        /** copyright 可不配置，不配置的话会有默认配置 */
        copyright?: string
    }

    export type InitialData = BaseApplicationConfig & {
        rootEntrances: string[]
        rootEntrancesWithTitle: { name: string; title: string; icon?: string }[]
    }

    export type AppInstanceConfig = {
        organizationTitles: {
            name: string
            oid: string
            applicationInstanceTitleMetas: {
                name: string
                title: string
                xid: string
                entrances: {
                    name: string
                    icon: string
                    title: string
                    [key: string]: unknown
                }[]
                [key: string]: unknown
            }[]
        }[]
    }

    export type AppAdminConfig = {
        applicationTitles: {
            name: string
            title: string
            entrances: {
                name: string
                icon: string
                title: string
            }[]
        }[]
    }

    export type ApplicationConfig = BaseApplicationConfig &
        AppAdminConfig &
        AppInstanceConfig

    export type connectParams = {
        baseUrl: string
        sseUrl?: string
        axiosAdapter?: AxiosAdapter
        /**
         * 可配置请求超时时间，默认 60s
         */
        axiosTimeout?: number
        /**
         * 本地授权自动延期时间，默认为60*60*1000（一小时），如果不想开启自动延期，请将此值设为0
         */
        refreshInterval?: number
        /**
         * 部分拦截器需要在内部拦截器构造前执行来获取更多配置信息
         */
        useInterceptor?: (instance: AxiosInstance) => void
    }

    export type loginParams = {
        rootEntrance: string
        codeToVerify: string
        username: string
        password: string
    }
    export type loginByTokenParams = {
        token: string
        isSuperUser?: boolean
        username?: string
    }

    export type InitConfig = {
        needOrg: boolean
        scenes: SceneConfig[]
        /**
         * true 时，header 的title 用场景值替换
         */
        replaceHeaderTitleWithScene?: boolean
        /**
         * 为true时可以不选组织和场景进入系统
         */
        canUseSystemWithoutScene?: boolean
        notExistSceneItems: MenuItem
    }
    export type SceneConfig = {
        scene: string
        config: SceneTypes.SceneConfig
        defaultValue?: string
    }
    export type InitData = {
        readonly orgId?: number
        readonly xid?: number
        readonly scenes?: readonly Scene[]
    }
    export type Scene = {
        name: string
        key: string
    }

    export type EntranceToolbarItemSubItem = {
        action: {
            action_name: string
            authed: boolean
            back_image: string
            behavior: string
            canBatch: boolean
            col_count: number
            container: string
            css: string
            enabled: boolean
            forward: string
            label: string
            label_position: string
            label_width: number
            need_footer: boolean
            need_title: boolean
            on: string
            open_in_new_tab: boolean
            prompt: string
            size_percent: number
            uniplat_version: number
        }
        badge: string
        behavior: string
        label: string
        title: string
    }

    export type EntranceToolbarItem = {
        action: string
        badge: string
        behavior: string
        icon: string
        input: string
        items: EntranceToolbarItemSubItem[]
        label: string
        title: string
    }

    export type Entrance = {
        canUseSystemWithoutScene?: boolean
        notExistSceneItems?: MenuItem
        homepage: string
        homepageFullscreen: boolean
        id: string
        items: MenuItem[]
        name: string
        application: string
        order: number
        sub_project_name: string
        toolbar: {
            header: {
                enhancer: string
                left: EntranceToolbarItem[]
                right: EntranceToolbarItem[]
            }
        }
        tooltip?: string
        features?: {
            /**
             * 是否启用【消息】和【会话】顶级侧边栏入口
             */
            chat: boolean
            /**
             * 是否启用【待办】顶级侧边栏入口
             */
            todo: boolean
        }
        globalSearchTypes?: { key: string; value: string }[]
        sectionRefs: { modelName: string; sectionName: string }[]
    }
    export type MenuItem = {
        id: string
        items: MenuItem[]
        order: number
        title?: string
        href?: string
        isUnion?: boolean
        unionItems?: MenuItem[]
        unionTitle?: string
        badge?: Record<string, number>
        container: IntentContainer
        fullscreen: boolean
        icon?: string
        [key: string]: any
    }
}

// sdk index end
// sse start
export namespace SSE {
    export type callBackFromOutside = (msg?: string | number) => void
    /**
     * @param createByMyself 修改模型数据的人是不是当前登录账号
     */
    export type callBackOfModelUpdated = (createByMyself: boolean) => void

    export type callBackOfBigActionExecute = (
        modelName: string,
        taskId: number
    ) => void

    export type callBackOfModelUpdatedFullMsg = (msg: msg) => void

    /**
     * @param createByMyself 修改模型数据的人是不是当前登录账号
     * @param indexs 后台数据已经更新了的图表
     */
    export type callbackOfModelUpdatedOfDashboad = (
        createByMyself: boolean,
        indexs: number[]
    ) => void

    export type msgModel = {
        model: string
        selectedList: string[]
    }

    export type msg = {
        createByMyself: boolean
        self: boolean
        type: string
        dataUpdates: msgModel[]
        [key: string]:
            | boolean
            | string
            | number
            | msgModel[]
            | Record<string, number>
    }

    export type transportMessageListener = (e: msg) => void
    export type bigActionUpdateListener = (e: msg) => void
    export type notifyListener = (message: msg) => void
    export type MenuDataChangedListener = (v: Record<string, number>) => void
    export type ConnectivityObserver = (online: boolean) => void
    export type removeSSEListener = () => void
}

// sse end

// action start
export namespace ActionTypes {
    export type BigActionSchemaLabel = {
        property: string
        label: string
    }

    export type BigActionTaskData = {
        id: number
        taskId: number
        params: string
        status: number
        errorMsg: string
    }

    export type BigActionTaskDetail = {
        id: number
        modelName: string
        actionName: string
        actionLabel: string
        fileName: string
        createTime: string
        creatorName: string
        total: number
        finish: number
        beginTime: string
        finishTime: string
        taskType: number
        schemaLabel: string
        status: number
    }

    export type BigActionDetailResult = {
        dataList: BigActionTaskData[]
        detail: BigActionTaskDetail
        labels: BigActionSchemaLabel[]
        total: number
        pageIndex: number
        pageSize: number
    }

    export type BigActionDetailQuery = {
        taskId: number
        status: number
        pageIndex: number
        pageSize: number
    }
    /**
     * bigAction 任务当前的运行状态
     */
    export type BigActionCheckResp = {
        /**
         * true 存在运行中的任务
         */
        running: boolean
        /**
         * 当前 action 对于数据模型的数据源
         */
        datasource?: string
        /**
         * 运行中的任务id
         */
        taskId?: number
    }

    export type initialParams = {
        selected_list: ActionTypes.selectedList[]
        prefilters: prefilter[]
    }

    export type updateInitialParams = {
        selected_list?: selectedList[]
        prefilters?: prefilter[]
        workflowExecuteContext?: workflow2.WorkflowExecuteContext
    }

    export type addInputs_parameterParams = {
        [property: string]: unknown
    }

    export namespace DetailParameterManager {
        export type rowData = {
            property: string
            value: string
        }
        export type result = Omit<ActionTypes.detaDetail, "values"> & {
            values: Array<{
                keyValue: string
                rowData: Array<rowData>
            }>
        }
        export type addParam = {
            [property: string]: unknown
        }
    }

    export type constructorType = {
        model_name: string
        action_name: string
    }

    export type selectedList = { v: number | string; id: number | string }
    export type detaDetail = {
        name: string
        values: Array<{
            keyValue: string
            rowData: Array<{
                property: string
                value: string | number
            }>
        }>
        deleted: string[]
    }

    export type inputs_parameters = {
        property: string
        value: unknown
    }
    export type executeParams = {
        dataDetails?: Array<detaDetail>
        filters?: Record<string, unknown>
        inputs_parameters?: inputs_parameters[]
        tagInfos?: {
            behavior: "insert" | "update" | "delete"
            tags: TagManagerTypes.TagInfo[]
        }[]
        /**
         * 工作流的上下文参数
         */
        workflowExecuteContext?: workflow2.WorkflowExecuteContext
    }
    export type executeNowParams = {
        dataDetails?: Array<detaDetail>
        inputs_parameters?: inputs_parameters[]
        selected_list?: selectedList[]
        prefilters?: prefilters
    }

    export type getDataSourceRequestParams = {
        funcName: string
        additionQuery?: unknown
    }

    export type updateControlsPropertiesRequestParams = executeParams
    export type updateControlsPropertiesRequestResult = {
        masters: {
            [key: string]: unknown
        }[]
        details: {
            name: string
            controls: { [key: string]: unknown }[]
            label: string
            visible: string
            datas: Array<{
                keyValue: string
                rowData: {
                    [property: string]: {
                        display: unknown
                        value: unknown
                        emptyValue: boolean
                    }
                }
                controls: { [key: string]: unknown }[]
            }>
        }[]
    }
    export type ValidationsListItem = { listIndex: unknown; error: string }
    export type MainHint = {
        error: string
        listIndex: number
        sender: string
        suggestion: string
    }
    export type MainValidation = {
        listIndex: number
        sender: string
        error: string
        suggestion: string
    }
    export type HintsListItem = any
    export type validationsOfDetail = any
    export type ValidatorInputerResult = {
        silent: boolean
        mainValidations: Array<MainValidation>
        validationsOfList: ValidationsListItem[]
        mainHints: MainHint[]
        hintsOfList: HintsListItem[]
        validationsOfDetails: validationsOfDetail[]
    }

    export type getDataSourceRequestResult = {
        [key: string]: unknown
    }[]
    export type validateRequestResult = {
        hintsOfList: HintsListItem[]
        mainHints: MainHint[]
        mainValidations: MainValidation[]
        silent: boolean
        validationsOfDetails: validationsOfDetail[]
        validationsOfList: ValidationsListItem[]
    }

    export type getAuthorsListRequestResult = {
        id: number
        is_superuser: boolean
        login_name: string
        name: string
    }[]

    export type validateBatchParams = executeBatchParams
    export type list_parameter = {
        [key: string]: unknown
    }
    export type TagInfos = TagManagerTypes.TagInfo[]
    export type executeBatchParams = executeParams & {
        list_parameters?: list_parameter[]
        selected_list?: ActionTypes.selectedList[]
        tagInfosForList?: TagInfos[]
        batchSchema: string // excel模版名字
    }
    export type executeEachParams = executeParams & {
        filters: Tools.FiltersType
        tagFilters: TagManagerTypes.TagFilter[]
        top?: number //前多少条记录
    }

    export type JointSearchParams = {
        search_field: string
        keyword: string
        page_index: number
        pageSize?: number
        selected: number[]
        actionId?: string
        form_params: unknown
        tagFilters: TagManagerTypes.TagFilter[]
        selected_list: ActionTypes.selectedList[]
        prefilters: prefilter[]
    }

    export type rule = {
        message: string
        trigger: string
        required?: boolean
        pattern?: string
        max?: number
        min?: number
        type?: string
    }

    export enum ValidatorTrigger {
        change = "change",
        blur = "blur",
    }
    export type inputsParameter = {
        default_value?: string
        ext_properties: any
        label: string
        label_width: number
        model_name: string
        property: string
        readonly: boolean
        type: string
        treeModelName?: string
        treeParentMode: boolean
        tip: string
        validator: {
            trigger: ValidatorTrigger
        }
        clearable: boolean
        filterable: boolean
        placeholder: string
        [key: string]: unknown
    }
    export type envParameter = {
        property: string
        result: number | string | boolean
    }
    export type details_parameters = {
        name: string
        label: string
        keyField: string
        visible: boolean
        datas: Array<{
            keyValue: string
            controls: unknown[]
            rowData: {
                [property: string]: {
                    display: unknown
                    value: unknown
                    emptyValue: boolean
                }
            }
        }>
        controls: Array<inputsParameter>
        allowUserModifyRows: boolean
        style: "form" | "table" | "template"
        rules: {
            [key: string]: {
                message: string
                required: boolean
                trigger: string
            }[]
        }
        template?: string[][]
    }
    export type info = {
        actionId: string
        col_count: number
        size_percent: number
        uniplat_version: number
        label_width: number
        subProjectName: string
        type: string
        prompt: string
        action_name: string
        back_image: string
        behavior: string
        container: string
        css: string
        forward: string
        label: string
        label_position: string
        on: string
        open_in_new_tab: boolean
        authed: boolean
        canBatch: boolean
        enabled: boolean
        need_footer: boolean
        need_title: boolean
        rules: {
            [key: string]: rule[]
        }
        parameters: {
            details_parameters: details_parameters[]
            env_parameters: envParameter[]
            inputs_parameters: inputsParameter[]
            inputs_style: "template" | "form"
            inputs_template: string[][]
        }
        tagGroups: {
            behaviors: Array<"insert" | "update" | "delete">
            tagGroups: TagManagerTypes.TagGroup[]
            tags: TagManagerTypes.TagInfo[]
        }
        secondaryConfirmMsg?: string
        [key: string]: unknown
    }

    export type eachSseMessageResp = {
        id?: string
        type?: string
        max?: number
        current?: number
        msg?: string
        forward?: string
    }

    export type eachErrorRow = { id: string; error: string }

    export type eachOnProgress = (percent: number, successCount: number) => void
    export type eachOnError = (errorRow: eachErrorRow[]) => void
}
// action end;

export type addTransportMessageListener = (
    cb: SSE.transportMessageListener
) => SSE.removeSSEListener
export type addSseNotifyMessageListener = (
    cb: SSE.notifyListener
) => SSE.removeSSEListener
export type addConnectivityObserver = (
    cb: SSE.ConnectivityObserver
) => SSE.removeSSEListener

export type passportUnbind = "账号未绑定"

export namespace ModelTypes {
    export type getForwardURLApiResult = {
        url: string
    }
    export type groupSearchApiParams = {
        prefilters: prefilters
        group: unknown
        by_date: string
        [key: string]: unknown
    }
    export type groupSearchApiResult = {
        rows: unknown[]
        columns: unknown[]
    }
    export type searchJointApiParams = {
        search_field: string
        keyword: string
        page_index: number
        selected: number[]
        actionId?: string
        form_params: unknown
        selected_list: Array<{ v: number | string; id: number | string }>
        tagFilters: TagManagerTypes.TagFilter[]
    }
    export type searchJointApiResult = {
        rows: Array<{
            [key: string]: unknown
        }>
        record_count: number
        page_count: number
        search_field: string
        selected: number[]
        key_field: string
        field_defs: unknown
    }
    export type mappingFetchAPIParams = {
        actionName?: string
        mappingName: string
        actionId?: string
        form_params: unknown
        selected_list: unknown
        prefilters?: prefilter[]
        nodeValue?: string | number | boolean
    }
    export type JointSearchRawPrefilters = {
        [property: string]: unknown
    }
    export type SuperCascaderFetchApiParams = {
        actionId: string
        form_params: any
        selected_list?: ActionTypes.selectedList[]
        prefilters?: prefilter[]
        superCascaderName: string
        parent?: string
    }
    export type SuperCascaderFetchApiResult = SuperCascader.MetaNode[]
    export type SuperCascaderSearchApiParams = {
        actionId: string
        form_params: any
        selected_list?: ActionTypes.selectedList[]
        prefilters?: prefilter[]
        superCascaderName: string
        keyword: string
        limitBegin: number
    }
    export type SuperCascaderSearchApiResult = {
        count: number
        nodes: SuperCascader.Node[]
    }
}

export enum SdkListRowType {
    Default = 0,
    Number = 1,
    Boolean = 2,
    Array = 3,
}

export type SdkListRowPredict = {
    value: string
    type?: SdkListRowType
    alias?: string
    label?: boolean
}

export type SdkListRowPredictObject = {
    [key: string]: "" | number | unknown[] | boolean | "label" | string
}

/**
 * 一个目前sdk内外都在用的方法，放到sdk里避免代码重复
 */
export type getAllFilters = (filter: unknown) => Tools.FiltersType
export type buildFilters = (
    filters: ListTypes.getListDataRequestResult["meta"]["filters"],
    prefilter: prefilters
) => unknown[]
export type metaFilterFunc = (
    filter: unknown,
    index: number,
    pageName: string
) => unknown

export type SdkConstructorParams = {
    sse?: boolean
    ssr?: boolean
    disableRefreshToken?: boolean
    /**
     * 本地缓存token时，是否键值添加RootEntrance作为前缀。默认不开启
     */
    useRootEntranceAsTokenPrefix?: boolean
}

export namespace TreeTypes {
    export type filter = {
        type: "tree"
        visible: true
        property: string
        status: {
            selectedList: unknown[]
            direct: false
        }
        treeModelName: string
    }
    /**
     * 树节点
     */
    export type Node = {
        id: unknown
        data: unknown
        dataMap?: {
            actions: action[]
            intents: RowIntent[]
            uniplat_version: { value: string | number; display: string }
            [key: string]: unknown
        }
        display: string
        children: Node[]
        relationDataCount?: number
        notTreeNode: boolean
        hasChildren?: boolean
        leaf?: boolean
    }

    /**
     * 树节点
     */
    export type Node2 = {
        id: unknown
        rowData?: metaRow2
        display: string
        children: Node2[]
        relationDataCount?: number
        notTreeNode: boolean
        hasChildren?: boolean
        leaf?: boolean
    }

    /**
     * 携带了元数据的树列表
     */
    export type TreeWithMeta = {
        trees: Node[]
        fieldDefs: field[]
        actions: action[] | null
        fieldGroups?: DetailTypes.FieldGroup[]
        fieldGroups2?: DetailTypes.FieldGroup[]
        detailVisible?: boolean
        checkboxes?: boolean
        label?: string
    }

    /**
     * 携带了元数据的树列表
     */
    export type TreeWithMeta2 = {
        trees: Node2[]
        actions?: action[]
        intents?: RowIntent[]
        fieldGroups?: DetailTypes.FieldGroup[]
        detailVisible?: boolean
        checkboxes?: boolean
        label?: string
    }

    /**
     * 树查询的合计时使用
     */
    export type TreeSummary = {
        modelName: string
        field: string
    }

    /**
     * 树携带业务数据的定义
     */
    export type ListTreeWithDataDef = {
        treeModelName: string
        template: string
        dataModelName: string
        foreignKey: string
        fields: string[]
    }
}

export namespace workflow2 {
    export type AssociateProcessInfo = {
        id: number
        associateId: number
        processName: string
        state: string
        /**
         * 0-未启动
         * 1-处理中
         * 2-已结束
         */
        status: number
    }
    /**
     * 状态
     */
    export type StateDef = {
        stateName: string
        description?: string
        /**
         * 分组名称
         */
        group?: string
        /**
         * true 终结节点
         */
        end: boolean
        tasks?: TaskDef[]
        /**
         * 任务后续状态
         */
        afterTasks?: Set<string>
        /**
         * 是否禁用
         */
        disable?: boolean
        /**
         * 排序字段
         */
        order?: number
    }

    /**
     * 任务
     */
    export type TaskDef = {
        taskName: string
        description?: string
        states?: StateDef[]
        /**
         * 任务的默认处理时间,天
         */
        defaultDays?: number
        /**
         * 任务默认的处理人
         */
        defaultDealerHandle?: string
        /**
         * 任务后续状态
         */
        afterStates?: Set<string>
        disable?: boolean
    }

    /**
     * 流程定义
     */
    export type ProcessDef = {
        modelName: string
        /**
         * 默认负责人钩子
         */
        defaultMasterHandle: string
        /**
         * 前置条件钩子
         */
        preHandle: string
        processName: string
        description?: string
        /**
         * 任务可取消
         */
        cancellable: boolean
        /**
         * 任务可编辑
         */
        editable: boolean
        /**
         * 任务可更换
         */
        changeable: boolean
        states: StateDef[]
        tasks: TaskDef[]
        groups?: Array<workflow2.StateGroup>
        stateChangeCallback?: string
        // 流程分组
        group?: string
    }

    export type StartProcessParam = {
        processName: string
        associateId?: number
        state: string
        task?: string
        dealer?: number
        dealerName?: string
        taskBeginTime?: string
        taskEndTime?: string
        remark?: string
        days?: number
        description?: string
        dispatchType?: string
    }

    export type ChangeProcessStateParam = {
        processId?: number
        /**
         * 待更换的状态
         */
        state: string
        /**
         * 说明
         */
        remark: string
    }

    export type UpdateTaskParam = {
        processId?: number
        /**
         * 流程的当前任务，创建时应该是空
         *
         */
        task?: string
        state: string
        /**
         * 创建任务时的新任务
         */
        newTask?: string
        dealer?: number
        dealerName?: string
        taskBeginTime?: string
        taskEndTime?: string
        /**
         * 备注
         */
        remark?: string
        /**
         * 选择的处理天数
         */
        days?: number
    }

    export type CancelTaskParam = {
        processId?: number
        task?: string
        state: string
        remark?: string
    }

    export type FinishTaskParam = {
        processId?: number
        task?: string
        state: string
        nextState: string
        remark?: string
        nextTask?: UpdateTaskParam
    }

    export type ChangeTaskParam = {
        processId?: number
        task?: string
        state: string
        currentTask: string
        /**
         * 评论
         */
        remark?: string
    }

    /**
     * 工作流过滤条件
     */
    export type WorkflowFilter = {
        processName?: string
        state?: string
        task?: string
        /**
         * all 全部, to-do 待办, relation 已办
         */
        queryType: string
        taskBeginTime: string[]
        taskEndTime: string[]
        instanceState: string
    }
    /**
     * 列表查询的参数
     */
    export type ListQueryParam = {
        /**
         * 过滤器
         */
        filters?: Tools.FiltersType
        /**
         * 前置过滤器
         */
        prefilters?: prefilter[]
        pageIndex: number
        pageSize: number
        workflowFilter: WorkflowFilter
        tagFilters?: TagManagerTypes.TagFilter[] | []
        listName?: string
        detailName?: string
        sorts?: any[]
        workflowListName?: string
        columns?: column[]
    }

    export type exportExcelParams = ListQueryParam & {
        templateName: string
    }

    export type ProcessInfo = {
        processName: string
        description?: string
        modelName: string
        states: StateDef[]
        groups: StateGroup[]
        tasks: TaskDef[]
        /**
         * 任务可取消
         */
        cancellable: boolean
        /**
         * 任务可编辑
         */
        editable: boolean
        /**
         * 任务可更换
         */
        changeable: boolean
        action: string
        superAdmin: string
        relations?: ProcessRelation[]
        dataSource?: string
        /**
         * 处理人分配的规则
         */
        ruleOfDistribution?: []
    }

    export type ProcessRelation = {
        type: string
        name: string
        items: string[]
    }

    export type ProcessEditParam = {
        processName: string
        /**
         * 前置条件，对应的 action 名称
         */
        action?: string
        description?: string
        /**
         * 任务可取消
         */
        cancellable: boolean
        /**
         * 任务可编辑
         */
        editable: boolean
        /**
         * 任务可更换
         */
        changeable: boolean
        groups: StateGroup[]
        tasks: TaskDef[]
        superAdmin: string
        relations?: ProcessRelation[]
        dataSource?: string
        /**
         * 流程变动回调配置
         */
        stateChangeCallback?: string
    }

    export type StateGroup = {
        name: string
        order?: number
        states: StateDef[]
    }

    export type WorkObject = {
        label: string
        display: string
    }

    export type ListRowItem = {
        data: unknown
        objects?: Array<WorkObject>
        fieldGroup?: unknown
    }

    export type ListResult = {
        rows: Array<ListRowItem>
        recordCount: number
        pageIndex: number
        pageSize: number
        filters: unknown[]
        processWithCounts: ProcessWithCount[]
        hasRight?: boolean
        headers?: string[]
        online?: any
        // 表头 fg
        fieldGroups?: field_group[]
        // 标签信息
        tagGroups?: TagManagerTypes.TagGroup[]
        sortDefs?: unknown[]
        // 是否有创建工作流的权限
        hasWorkflowCreateRight?: boolean
        exportTemplateNames?: string[]
        subProjectName?: string
        meta?: WorkflowListMeta
    }

    export type WorkflowListMeta = {
        label: string
        showSwitchComponent: boolean
        appointProcessName: string
        processNameListRight: string[]
        detailName?: string
        modelName: string
        manualLoadData?: boolean
    }

    export type ProcessDetail = {
        detail: ListRowItem
        states: Array<string>
        /**
         * 终结节点的状态
         */
        endStates: Array<string>
        tasks: Array<string>
        /**
         * 任务可取消
         */
        cancellable: boolean
        /**
         * 任务可编辑
         */
        editable: boolean
        /**
         * 任务可更换
         */
        changeable: boolean
        /**
         * true 说明当前可编辑
         * false 无编辑权限，只能查看信息
         */
        detailEditable: boolean
        /**
         * true 流程已完成 false 流程未完成
         */
        complete: boolean
        groups: StateGroup[]
        actions: any[]
        /**
         * true 包含工作流规约的详情页
         */
        hasWorkflowDetail?: boolean
        /**
         * 当前任务的定义，如果存在
         */
        taskDef?: TaskDef
        /**
         * true: 可以撤回
         */
        canRollback?: boolean
    }

    export type ProcessOperation = {
        id: number
        processId: number
        state: string
        task: string
        dealer: string
        dealerName: string
        type: number
        beforeOperation: string
        afterOperation: string
        remarkId: number
        remark: string
        createTime: string
    }

    export type WorkRecord = {
        state: string
        finishState: string
        task: string
        createTime: string
        finishTime: string
        creator: number
        creatorName: string
        completer: number
        completerName: number
    }

    export type Remark = {
        id: number
        processId: number
        state: string
        task: string
        content: string
        dealerName: string
        createTime: string
        fromScene: string
        /**
         * 附件文件相对路径。多个文件会用逗号分割存储，使用时需要手动拆分
         */
        attachments?: string
    }

    export type ProcessWithCount = {
        processName: string
        count: number
        editable?: boolean
        /**
         * 当前用户该流程是否在线
         */
        online?: boolean
    }

    export type StartCheckResult = {
        existCount: number
        states: StateDef[]
        tasks: TaskDef[]
    }

    export type BatchParam<T> = {
        param: T
        ids: number[]
    }

    export type BatchFailDetail = {
        index: number
        id: number
        message: string
    }

    export type BatchResult = {
        total: number
        success: number
        fail: number
        failDetails: BatchFailDetail[]
    }

    export type Dealer = {
        id: number
        name: string
    }

    export type AddRemarkParam = {
        processId: number
        remark: string
        /**
         * 评论来源，可以为空
         */
        fromScene?: string
        /**
         * 附件文件相对路径。多个文件用逗号分割
         */
        attachments?: string
    }

    export type listOperationParam = {
        type?: number
        beginTime?: string
        endTime?: string
    }

    export type ActionInfo = {
        name: string
        label: string
    }
    export type ProcessInstancePanel = {
        id: number
        processName: number
        state: number
        dealerName: number
    }

    export type WorkflowExecuteContext = {
        processId: number
        associateId: number
        state: string
        task?: string
        dealer?: number
        /**
         * 是否完成
         */
        finish: boolean
    }

    export type DatasourceWithCount = {
        /**
         * 数据源
         */
        datasource: string
        /**
         * 我的代办的数量
         */
        todoCount: number
        /**
         * 我负责的数量
         */
        masterCount: number
    }

    export type HomeWorkflowCount = {
        /**
         * 待办的统计数量
         */
        todoCount: number
        /**
         * 我负责的统计数量
         */
        masterCount: number
    }
}

export namespace FileModel {
    export type DomainTreeModelItem = {
        label: string
        name: string
        database: string
    }

    export type DomainTree = {
        models?: DomainTreeModelItem[]
        label: string
        name: string
    }

    export type DataSources = {
        label: string
        name: string
    }
}

export type HistoryVersionIdentity = {
    model: string
    objectId: any
    field: string
    identity: string
    label: string
    color: string
}

export type HistoryVersion = {
    oldValue: string
    newValue: string
    updateTime: string
    updateUserId: string
    updateUserName: string
    identity: string
    label: string
    color: string
}
export namespace SuperCascader {
    export type Node = {
        key: string
        label: string
        isLeaf: boolean
        parent?: Node
    }

    export type MetaNode = {
        key: string
        label: string
        isLeaf: boolean
    }

    export type QuickSelectionMeta = {
        label: string
        nodes: Node[]
    }

    export type Layout = {
        minWidth: number
        column: number
        direction: "column" | "row"
    }

    export type Meta = {
        modelName: string
        name: string
        label: string
        selectableNode: "any" | "leaf"
        joiner: string
        searchable: boolean
        searchInputPlaceholder?: string
        valueNode?: Node
        lastNodes: MetaNode[]
        quickSelections: QuickSelectionMeta[]
        layout: Layout
    }
}

export namespace Report {
    export type ReportMeta = {
        filters: filter[]
        label: string
        name: string
        defaultTemplate?: string
        templates: string[]
        [key: string]: unknown
    }
}

export namespace ListSection {
    export type Meta = {
        relatedWithFilter: boolean
        chart_groups: {
            name: string
            span: number
            title: string
            option: unknown
        }[]
        chart_groups_span: number
    } & DetailTypes.section
}
