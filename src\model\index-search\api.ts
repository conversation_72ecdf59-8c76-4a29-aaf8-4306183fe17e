import axios from "../../core/axios/index"
import type * as dto from "../../def/index"

export default class IndexSearchApi {
    public query() {
        return axios.get(`general/es/searchTypes`)
    }

    public search(params: dto.IndexSearchTypes.SearchApiParams) {
        return axios.get<dto.IndexSearchTypes.SearchRequestResult>(
            `general/es/search/${params.searchText}/type/${params.searchType}/page/${params.pageIndex}`
        )
    }

    public exportData(params: dto.IndexSearchTypes.ExportDataApiParams) {
        return axios.get<string>(
            `general/es/export/${params.searchText}/type/${params.searchType}`
        )
    }
    public exportZip(params: dto.IndexSearchTypes.exportZipApiParams) {
        return axios.get<string>(
            `general/es/exportZip/${params.searchText}/type/${params.searchType}`
        )
    }
}
