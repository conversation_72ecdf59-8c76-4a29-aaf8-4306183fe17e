<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>DomainService | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="domainservice.html">DomainService</a>
				</li>
			</ul>
			<h1>Class DomainService</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">DomainService</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="domainservice.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section tsd-is-private tsd-is-private-protected">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="domainservice.html#api" class="tsd-kind-icon">api</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="domainservice.html#anonymousgost" class="tsd-kind-icon">anonymous<wbr>Gost</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="domainservice.html#anonymouspost" class="tsd-kind-icon">anonymous<wbr>Post</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="domainservice.html#get" class="tsd-kind-icon">get</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="domainservice.html#post" class="tsd-kind-icon">post</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="domainservice.html#request" class="tsd-kind-icon">request</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new <wbr>Domain<wbr>Service<span class="tsd-signature-symbol">(</span>subProjectName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, serviceName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, apiName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="domainservice.html" class="tsd-signature-type">DomainService</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/domain-service/domain-service.ts:6</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>subProjectName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>serviceName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>apiName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="domainservice.html" class="tsd-signature-type">DomainService</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="api" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> api</h3>
					<div class="tsd-signature tsd-kind-icon">api<span class="tsd-signature-symbol">:</span> <a href="domainserviceapi.html" class="tsd-signature-type">DomainServiceApi</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/domain-service/domain-service.ts:6</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="anonymousgost" class="tsd-anchor"></a>
					<h3>anonymous<wbr>Gost</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">anonymous<wbr>Gost&lt;T&gt;<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span>, headers<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/domain-service/domain-service.ts:40</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> params: <span class="tsd-signature-type">unknown</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> headers: <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="anonymouspost" class="tsd-anchor"></a>
					<h3>anonymous<wbr>Post</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">anonymous<wbr>Post&lt;T&gt;<span class="tsd-signature-symbol">(</span>data<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span>, params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span>, headers<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/domain-service/domain-service.ts:51</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> data: <span class="tsd-signature-type">unknown</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> params: <span class="tsd-signature-type">unknown</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> headers: <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="get" class="tsd-anchor"></a>
					<h3>get</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">get&lt;T&gt;<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span>, headers<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/domain-service/domain-service.ts:24</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> params: <span class="tsd-signature-type">unknown</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> headers: <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="post" class="tsd-anchor"></a>
					<h3>post</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">post&lt;T&gt;<span class="tsd-signature-symbol">(</span>data<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span>, params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span>, headers<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/domain-service/domain-service.ts:28</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> data: <span class="tsd-signature-type">unknown</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> params: <span class="tsd-signature-type">unknown</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> headers: <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="request" class="tsd-anchor"></a>
					<h3>request</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">request&lt;ParamsType, DataType, ReturnType&gt;<span class="tsd-signature-symbol">(</span>method<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Method</span>, config<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>data<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">DataType</span><span class="tsd-signature-symbol">; </span>headers<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">ParamsType</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ReturnType</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/domain-service/domain-service.ts:10</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>ParamsType</h4>
								</li>
								<li>
									<h4>DataType</h4>
								</li>
								<li>
									<h4>ReturnType</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>method: <span class="tsd-signature-type">Method</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagDefault value">Default value</span> config: <span class="tsd-signature-symbol">{ </span>data<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">DataType</span><span class="tsd-signature-symbol">; </span>headers<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">ParamsType</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> = {}</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> data<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">DataType</span></h5>
										</li>
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> headers<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{}</span></h5>
											<ul class="tsd-parameters">
												<li class="tsd-parameter-index-signature">
													<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span></h5>
												</li>
											</ul>
										</li>
										<li class="tsd-parameter">
											<h5><span class="tsd-flag ts-flagOptional">Optional</span> params<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">ParamsType</span></h5>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">ReturnType</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="domainservice.html" class="tsd-kind-icon">Domain<wbr>Service</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class">
								<a href="domainservice.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="domainservice.html#api" class="tsd-kind-icon">api</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="domainservice.html#anonymousgost" class="tsd-kind-icon">anonymous<wbr>Gost</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="domainservice.html#anonymouspost" class="tsd-kind-icon">anonymous<wbr>Post</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="domainservice.html#get" class="tsd-kind-icon">get</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="domainservice.html#post" class="tsd-kind-icon">post</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="domainservice.html#request" class="tsd-kind-icon">request</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>