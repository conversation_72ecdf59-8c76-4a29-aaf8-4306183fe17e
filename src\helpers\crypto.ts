import { AxiosRequestConfig } from "axios"
import * as CryptoJS from "crypto-js"

const s = Symbol()
const c = {}
let has = false
let enableCryptoPayload = false

export function parse(input: string | Record<string, unknown>, key?: string) {
    if ((has || key) && typeof input === "string") {
        const encryptedHex = CryptoJS.enc.Hex.parse(input)
        const decrypted = CryptoJS.AES.decrypt(
            { ciphertext: encryptedHex } as CryptoJS.lib.CipherParams,
            key ? CryptoJS.enc.Utf8.parse(key) : c[s],
            {
                mode: CryptoJS.mode.ECB,
                padding: CryptoJS.pad.Pkcs7,
            }
        )
        try {
            const v = decrypted.toString(CryptoJS.enc.Utf8)
            if (v.startsWith("{")) {
                return JSON.parse(v)
            }
            return v
        } catch (e) {
            console.error(e)
            return input
        }
    }
    return input
}

export function parse4Base64(input: string | Record<string, unknown>) {
    if (has && typeof input === "string") {
        const decrypt = CryptoJS.AES.decrypt(input, c[s], {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
        })
        try {
            return CryptoJS.enc.Utf8.stringify(decrypt).toString()
        } catch (e) {
            console.error(e)
            return input
        }
    }
    return input
}

/**
 * @param input 需要加密的字符串
 * @param key 可选密钥，如果没有，则使用 set 函数里面设置的密钥
 */
export function encode(input: string | Record<string, unknown>, key?: string) {
    if (has || key) {
        const d = (i: string) =>
            CryptoJS.AES.encrypt(i, key ? CryptoJS.enc.Utf8.parse(key) : c[s], {
                mode: CryptoJS.mode.ECB,
                padding: CryptoJS.pad.Pkcs7,
            }).toString(CryptoJS.format.Hex)

        if (typeof input === "string") {
            return d(input)
        }
        return d(JSON.stringify(input))
    }
    return input
}

export function encodeBase64(input: string, key: string) {
    const keyUtf8 = CryptoJS.enc.Utf8.parse(key)
    const inputUtf8 = CryptoJS.enc.Utf8.parse(input)
    const encrypted = CryptoJS.AES.encrypt(inputUtf8, keyUtf8, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
    })

    return encrypted.toString()
}
export function set(k: string) {
    c[s] = CryptoJS.enc.Utf8.parse(k)
    has = true
}

export function enablePayloadEncode() {
    enableCryptoPayload = true
}

export function encodePayload<T>(payload: T) {
    if (enableCryptoPayload) {
        return encode(payload as unknown as string)
    }
    return payload
}

const ct = "Content-Type"
const ctv = "application/stream+json"
const p = "parameters"

/**
 * 默认这些url不进行参数加密
 */
let defaultUncodeUrlReg: RegExp[] = [
    // 2024 5 10 execute 也开启了加密
    // /\/action\/(.*)\/execute/,
    // /\/action2\/(.*)\/execute/,
    /file\/upload/,
    /file\/image/,
]

export function setUncodeUrl(items: RegExp[]) {
    defaultUncodeUrlReg = items
}

export function rebuildAxiosConfig(config: AxiosRequestConfig) {
    if (enableCryptoPayload && has) {
        const method = config.method
        const url = config.url
        if (method === "POST" || method === "post") {
            for (const item of defaultUncodeUrlReg) {
                if (item.test(url)) {
                    return config
                }
            }
            const payload = config.data
            if (payload) {
                const form = payload as FormData
                if (form.getAll) {
                    const parameters = form.get(p)
                    if (parameters) {
                        form.delete(p)
                        form.append(p, encodePayload(parameters) as string)
                        !config.headers && (config.headers = {})
                        config.headers[ct] = ctv
                    }
                } else {
                    config.data = encodePayload(payload)
                    !config.headers && (config.headers = {})
                    config.headers[ct] = ctv
                }
            }
        } else if (method === "get" || method === "GET") {
            if (url && url.includes(`${p}=`)) {
                const paths = url.split("?")
                const query = paths[1]
                if (query) {
                    const qs = query.split("&")
                    // 可能有多个parameters，所以不用break
                    for (let i = 0; i < qs.length; i++) {
                        const q = qs[i]
                        if (q.startsWith(`${p}=`)) {
                            const v = q.split("=")
                            v[1] = encodePayload(v[1]) as string
                            qs[i] = v.join("=")
                            config.url = `${paths[0]}?${qs.join("&")}`
                        }
                    }
                }
            }
        }
    }
    return config
}

export function hasKey() {
    return has
}

const storageKey = Symbol()
/**
 * 是否开启缓存内部数据加密
 * @param key 密钥，不配置的话，默认使用 set 函数里面设置的密钥
 */
export function enableStorageEncode(key?: string) {
    if (key) {
        c[storageKey] = key
    }
}

export const storageEncoder = {
    enabled: () => !!c[storageKey],
    encode: (v: string) => {
        if (c[storageKey]) {
            return encode(v, c[storageKey]) as string
        }
        return v
    },
    decode: (v: string) => {
        if (c[storageKey]) {
            return parse(v, c[storageKey])
        }
        return v
    },
}

export function isParameterEncode() {
    return has && enableCryptoPayload
}
