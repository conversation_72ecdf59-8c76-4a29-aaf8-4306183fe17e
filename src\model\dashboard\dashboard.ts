import { events } from "../../core/events"
import type * as dto from "../../def/index"

import DetailApi from "./api"
export class Dashboard {
    private api: DetailApi
    private callbackOnChange?: dto.SSE.callbackOfModelUpdatedOfDashboad
    private dashboards: dto.DashboardTypes.chatData[] = []
    constructor(subProjectName: string, dashboardName: string) {
        this.api = new DetailApi(subProjectName, dashboardName)
    }

    public async query() {
        const data = await this.api.getDashboard()
        this.dashboards = data.dashboard
        return Object.freeze(data)
    }

    public async refreshSpecifyChat(index: number) {
        const data = await this.api.refreshSpecifyChat(index)
        this.dashboards[index] = data
        return Object.freeze(data)
    }

    private onTransportMessage = (msg: dto.SSE.msg) => {
        const models = msg.dataUpdates.map((k) => k.model)
        const dashboardsModels = this.dashboards.map((k) => k.model_names)
        const indexs = dashboardsModels
            .map((k, idx) => {
                const udpated = k.every((modelName) =>
                    models.includes(modelName)
                )
                if (udpated) {
                    return idx
                }
                return null
            })
            .filter((k): k is number => k != null)
        if (indexs.length === 0) return
        this.callbackOnChange &&
            this.callbackOnChange(msg.createByMyself, indexs)
    }

    public registerOnChange(cb: dto.SSE.callbackOfModelUpdatedOfDashboad) {
        this.callbackOnChange = cb
        return events.addTransportMessageListener(this.onTransportMessage)
    }
}
