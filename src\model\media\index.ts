import axiosSource, { AxiosRequestConfig, Canceler } from "axios"
import { Md5 } from "ts-md5"

import axios from "../../core/axios/index"
import { global } from "../../core/global"
import { parse } from "../../helpers/crypto"

import { UploadType } from "./../../def/media"

export class MediaController {
    private wxUpload(
        fileName: string,
        file: string | File,
        md5: string,
        getTask?: (task: UniApp.UploadTask) => void,
        modelName?: string
    ): Promise<{ code: string; fullPath: string }> {
        return new Promise((resolve, reject) => {
            const uploadConfig: UniApp.UploadFileOption = {
                url: `${global.baseUrl}general/file/upload`,
                name: "file",
                header: { Authorization: global.getCurrentToken() },
                formData: {
                    md5: md5,
                    filename:
                        fileName || (typeof file !== "string" ? file.name : ""),
                    modelName: modelName || "",
                },
                success(res) {
                    let data = JSON.parse(res.data)
                    try {
                        data = parse(data).data
                    } catch (err) {
                        console.error(err)
                    }
                    resolve({
                        code: data.code,
                        fullPath: data.fullPath,
                    })
                },
                fail(err) {
                    reject(err)
                },
            }
            if (typeof file === "string") {
                uploadConfig.filePath = file
            } else {
                uploadConfig.file = file
            }
            const uploadTask = uni.uploadFile(uploadConfig)
            if (getTask) {
                getTask(uploadTask)
            }
        })
    }

    private blobToUint8Array(blob: Blob): Promise<Uint8Array> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader()
            reader.readAsArrayBuffer(blob)
            reader.onload = () => {
                const buffer = new Uint8Array(reader.result as ArrayBuffer)
                resolve(buffer)
            }
            reader.onerror = (error) => reject(error)
        })
    }

    public uploadFile(
        file: File,
        config?: {
            onUploadProgress?: (e: {
                loaded: number
                total: number
                percent: number
            }) => void
            getCancelSource?: (cancel: Canceler) => void
            timeout?: number
        }
    ) {
        const form = new FormData()
        form.append("file", file)
        const axiosConfig: AxiosRequestConfig = {}
        if (config && config.getCancelSource) {
            const source = axiosSource.CancelToken.source()
            axiosConfig.cancelToken = source.token
            config.getCancelSource(source.cancel)
        }
        if (config && config.onUploadProgress) {
            axiosConfig.onUploadProgress = (e) => {
                const percent = Math.round((e.loaded * 100) / e.total)
                config.onUploadProgress({
                    loaded: e.loaded,
                    total: e.total,
                    percent,
                })
            }
        }
        if (config?.timeout !== undefined) {
            axiosConfig.timeout = config.timeout
        }
        return axios.post<{ url: string; fullPath: string }>(
            `general/upload/`,
            form,
            axiosConfig
        )
    }

    public async uploadFileV2(
        file: File,
        config?: {
            onUploadProgress?: (e: {
                loaded: number
                total: number
                percent: number
            }) => void
            getCancelSource?: (cancel: Canceler) => void
            timeout?: number
        },
        modelName?: string
    ) {
        const axiosConfig: AxiosRequestConfig = {}
        if (config && config.getCancelSource) {
            const source = axiosSource.CancelToken.source()
            axiosConfig.cancelToken = source.token
            config.getCancelSource(source.cancel)
        }
        if (config && config.onUploadProgress) {
            axiosConfig.onUploadProgress = (e) => {
                const percent = Math.round((e.loaded * 100) / e.total)
                config.onUploadProgress({
                    loaded: e.loaded,
                    total: e.total,
                    percent,
                })
            }
        }

        if (config?.timeout !== undefined) {
            axiosConfig.timeout = config.timeout
        }
        const buffer = await this.blobToUint8Array(file)
        const md5 = new Md5()
        const spark = md5.appendByteArray(buffer)
        const hexHash = spark.end(false).toString()
        const temp = file.name.split(".")
        let type = ""
        if (temp.length > 1) {
            type = temp[temp.length - 1]
        }
        const q = [
            `md5=${hexHash}`,
            `type=${encodeURIComponent(type)}`,
            `size=${file.size}`,
            `filename=${encodeURIComponent(file.name)}`,
        ]
        if (modelName) {
            q.push(`&modelName=${modelName}`)
        }
        let resp = await axios.get<{
            uri: string
            code: string
            value: string
            fullPath: string
        } | null>(`general/file/check?${q.join("&")}`)
        if (!resp) {
            const form = new FormData()
            form.append("file", file)
            form.append("md5", hexHash)
            form.append("filename", file.name)
            modelName && form.append("modelName", modelName)
            resp = await axios.post<{
                uri: string
                code: string
                value: string
                fullPath: string
            }>(`general/file/upload`, form, axiosConfig)
        }
        const url = `fs/${resp.code}__${file.name}`
        return {
            url,
            fullUrl: `${global.baseUrl}${url}`,
            fileName: file.name,
            code: resp.code,
            fullPath: resp.fullPath,
        }
    }

    public async uploadFileForWxApp(
        fileName: string,
        path: string | File,
        size: number,
        buffer: Uint8Array,
        getTask?: (task: UniApp.UploadTask) => void,
        modelName?: string
    ) {
        const md5 = new Md5()
        const spark = md5.appendByteArray(buffer)
        const hexHash = spark.end(false).toString()
        const temp = fileName.split(".")
        let type = ""
        if (temp.length > 1) {
            type = temp[temp.length - 1]
        }
        let paths = `general/file/check?md5=${hexHash}&type=${encodeURIComponent(
            type
        )}&size=${size}&filename=${encodeURIComponent(fileName)}`
        if (modelName) {
            paths += `&modelName=${modelName}`
        }
        let resp = await axios.get<{ code: string; fullPath: string } | null>(
            paths
        )
        if (!resp) {
            resp = await this.wxUpload(
                fileName,
                path,
                hexHash,
                getTask,
                modelName
            )
        }
        const url = `fs/${resp.code}__${fileName}`
        return {
            url,
            fullUrl: `${global.baseUrl}${url}`,
            fileName,
            fullPath: resp.fullPath,
        }
    }
    /**
     * @param file tempFile可以为base64字符串，无前缀，此时type为必填,base64为true
     */
    public async uploadFileForUniApp(
        file:
            | {
                  tempFile: string
                  name: string
                  type?: string
                  base64?: boolean
              }
            | UniApp.ChooseFile
            | UniApp.ChooseImageSuccessCallbackResultFile
            | UniApp.ChooseFileSuccessCallbackResultFile
            | File,
        getTask?: (task: UniApp.UploadTask) => void
    ) {
        const fileType = (file as { type: string }).type || ""
        if ((file as { base64: boolean }).base64) {
            file = this.base64toFile(
                (file as { tempFile?: string }).tempFile,
                fileType
            )
        }
        let fileName = (file as File)?.name
        // 因为uniapp选择图片与选择文件api差异，选择图片时拿不到文件名，此步骤为兼容处理
        if (!fileName) {
            const path = (file as UniApp.ChooseFile)?.path || ""
            const temp = path.split(".")
            if (temp.length > 1) {
                fileName = "temp." + temp[temp.length - 1]
            }
        }
        const tempFile = (file as { tempFile?: string }).tempFile
        if (tempFile) {
            return this.wxUpload(
                fileName,
                tempFile,
                Md5.hashStr(tempFile),
                getTask
            ).then((res) => {
                const url = `fs/${res.code}__${fileName}`
                return {
                    url,
                    fullUrl: `${global.baseUrl}${url}`,
                    fileName,
                    fullPath: res.fullPath,
                }
            })
        }
        // h5和抖音小程序不支持 getFileSystemManager 有考虑其他方法，但是不如这个直接
        try {
            const fileBufferArrayBuffer = uni
                .getFileSystemManager()
                .readFileSync((file as UniApp.ChooseFile).path)
            const fileBuffer = new Uint8Array(
                fileBufferArrayBuffer as ArrayBuffer
            )
            return this.uploadFileForWxApp(
                fileName,
                (file as UniApp.ChooseFile).path,
                (file as UniApp.ChooseFile).size,
                fileBuffer,
                getTask
            )
        } catch {
            const fileBuffer = await this.blobToUint8Array(file as File)
            return this.uploadFileForWxApp(
                fileName,
                file as File,
                (file as File).size,
                fileBuffer,
                getTask
            )
        }
    }

    public base64toFile(base64Str: string, fileType: string) {
        if (base64Str.indexOf(",") < 0) {
            base64Str = this.getBase64Type(fileType) + base64Str
        }
        const myBlob = this.dataURLtoBlob(base64Str)
        const file = new File(myBlob, "temp." + fileType, {
            lastModified: Date.now(),
        })
        return file
    }

    public dataURLtoBlob(dataurl: string) {
        const arr = dataurl.split(","),
            bstr = atob(arr[1])
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        return [u8arr]
    }

    public getBase64Type(type: string) {
        switch (type) {
            case "txt":
                return "data:text/plain;base64,"
            case "doc":
                return "data:application/msword;base64,"
            case "docx":
                return "data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,"
            case "xls":
                return "data:application/vnd.ms-excel;base64,"
            case "xlsx":
                return "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"
            case "pdf":
                return "data:application/pdf;base64,"
            case "pptx":
                return "data:application/vnd.openxmlformats-officedocument.presentationml.presentation;base64,"
            case "ppt":
                return "data:application/vnd.ms-powerpoint;base64,"
            case "png":
                return "data:image/png;base64,"
            case "jpg":
                return "data:image/jpeg;base64,"
            case "gif":
                return "data:image/gif;base64,"
            case "svg":
                return "data:image/svg+xml;base64,"
            case "ico":
                return "data:image/x-icon;base64,"
            case "bmp":
                return "data:image/bmp;base64,"
            default:
                return "data:application/octet-stream;base64,"
        }
    }

    public async downloadFile(url: string) {
        const originAxioInstance = axiosSource.create()
        let arraybuffer: ArrayBuffer
        try {
            const response = await originAxioInstance.get(url, {
                responseType: "arraybuffer",
                withCredentials: false,
            })
            if (!response.headers["content-type"].startsWith("application/")) {
                throw new Error("文件类型错误")
            }
            arraybuffer = response.data
        } catch (err) {
            console.error(err)
            throw new Error("文件下载失败")
        }
        return arraybuffer
    }

    public buildThumbnail(url: string, w?: number, h?: number, view?: boolean) {
        if (/fs\//.test(url)) {
            const tail = "parameters="
            const hasParameter = url.includes(`?${tail}`)
            const p: { width?: number; height?: number } = {}
            if (hasParameter) {
                const parameters = JSON.parse(
                    decodeURIComponent(url.split("?")[1].replace(tail, ""))
                ) as Record<string, string | number>
                Object.assign(p, parameters)
            }
            !p.width && w && Object.assign(p, { width: w })
            !p.height && h && Object.assign(p, { height: h })
            view && Object.assign(p, { forceDownload: false })
            return `${url.split("?")[0]}?parameters=${encodeURIComponent(
                JSON.stringify(p)
            )}`
        }
        return url
    }

    public async uploadFileOthers(file: File, modelName?: string) {
        const buffer = await this.blobToUint8Array(file)
        const md5 = new Md5()
        const spark = md5.appendByteArray(buffer)
        const hexHash = spark.end(false).toString()
        const form = new FormData()
        form.append("file", file)
        form.append("md5", hexHash)
        form.append("filename", file.name)
        form.append("parameters", "{}")
        modelName && form.append("modelName", modelName)
        const data = await axios.post<{ uri: string; code: string }>(
            `/general/file/upload/passportOthers`,
            form
        )
        return { data }
    }

    /**
     * 选择文件上传，适用于Web，H5
     */
    public chooseFile(
        type: UploadType | string[] = UploadType.Default,
        size?: number
    ) {
        return new Promise<File>((resolve, reject) => {
            const target = document.createElement("input")
            target.setAttribute("type", "file")
            // 添加这个属性，就可以唤起相机的功能
            type === UploadType.Camera &&
                target.setAttribute("capture", "camera")
            // 这里如果不加属性 accept 是 "image/*" 或者 "video/*"，就默认打开摄像头，既可以拍照也可以录像
            type === UploadType.Image &&
                target.setAttribute("accept", "image/*")
            if (Array.isArray(type)) {
                target.setAttribute("accept", type.join(","))
            }
            target.setAttribute("style", "display:none")
            // 监听改变事件
            target.addEventListener("change", (e: Event) => {
                // 拿到文件对象
                if (e && e.target) {
                    const t = e.target as HTMLInputElement
                    const { files } = t
                    if (files) {
                        // 返回的是一个文件对象
                        if (size && files[0].size >= size * 1024 * 1024) {
                            let errFileType = "图片"
                            if (type === UploadType.Default) {
                                errFileType = "文件"
                            }
                            reject(new Error(`上传的${errFileType}太大了~`))
                            return
                        }
                        resolve(files[0])
                        setTimeout(() => target.remove(), 200)
                        return
                    }
                }
                reject(new Error("系统不支持"))
                setTimeout(() => target.remove(), 200)
            })
            document.body.appendChild(target)
            // 这里是模拟点击了input控件
            target.click()
        })
    }

    public chooseFiles(type: UploadType | string[] = UploadType.Default) {
        return new Promise<File[]>((resolve, reject) => {
            const target = document.createElement("input")
            target.setAttribute("type", "file")
            type === UploadType.Camera &&
                target.setAttribute("capture", "camera")
            type === UploadType.Image &&
                target.setAttribute("accept", "image/*")
            if (Array.isArray(type)) {
                target.setAttribute("accept", type.join(","))
            }
            target.setAttribute("style", "display:none")
            target.setAttribute("multiple", "multiple")
            target.addEventListener("change", (e: Event) => {
                if (e && e.target) {
                    const t = e.target as HTMLInputElement
                    const { files } = t
                    if (files) {
                        const items: File[] = []
                        for (let i = 0; i < files.length; i++) {
                            items.push(files[i])
                        }
                        resolve(items)
                        setTimeout(() => target.remove(), 200)
                        return
                    }
                }
                reject(new Error("系统不支持"))
                setTimeout(() => target.remove(), 200)
            })
            document.body.appendChild(target)
            target.click()
        })
    }
}

export const mediaController = new MediaController()
