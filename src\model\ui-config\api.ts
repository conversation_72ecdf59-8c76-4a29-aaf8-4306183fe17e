import axios from "../../core/axios/index"
import type * as dto from "../../def/index"

export default class UIConfigApi {
    constructor(private datamodelName: string) {}

    public query() {
        return axios.get<dto.UIConfigTypes.queryRequestResult>(
            `general/configs/uiconfig/${this.datamodelName}`
        )
    }

    public save(datamodel: unknown) {
        const formdata = new FormData()
        formdata.append("text", JSON.stringify(datamodel, null, "\t"))
        return axios.post<void>(
            `general/configs/uiconfig/${this.datamodelName}/save`,
            formdata
        )
    }

    public requestMappingValues(
        enumRequestString: dto.UIConfigTypes.SchemaNode["enumRequest"]
    ) {
        const enumRequest = eval("`" + enumRequestString + "`")
        return axios.get<string[]>(`general/configs/${enumRequest}`)
    }
}
