import { global } from "../../core/global"
import type * as dto from "../../def/index"

import * as api from "./api"

export class PassportLogin {
    constructor(private done: () => void, private failed: () => void) {}

    /**
     * 用passport的token登录本sdk
     */
    public async login(token: string) {
        try {
            const data = await api.getJWTTokenByPassportToken(token)
            global.username = data.username
            global.isSuperAdmin = data.isSuperUser
            global.jwtToken = data.jwt
            this.done()
            return data
        } catch (error) {
            this.failed()
            const unbind = "账号未绑定"
            if (error.toString() === unbind) {
                return unbind
            }
            throw error
        }
    }
    /**
     * 将passport的账号和uniplat的账号绑定起来
     */
    public async binding(params: dto.Index.BindPassportParams) {
        const data = await api.bindPassportTokenToJwt(params)
        global.username = data.username
        global.isSuperAdmin = data.isSuperUser
        global.jwtToken = data.jwt
        this.done()
        return data
    }
}
