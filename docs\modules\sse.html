<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>SSE | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="sse.html">SSE</a>
				</li>
			</ul>
			<h1>Namespace SSE</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Type aliases</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="sse.html#connectivityobserver" class="tsd-kind-icon">Connectivity<wbr>Observer</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="sse.html#menudatachangedlistener" class="tsd-kind-icon">Menu<wbr>Data<wbr>Changed<wbr>Listener</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="sse.html#bigactionupdatelistener" class="tsd-kind-icon">big<wbr>Action<wbr>Update<wbr>Listener</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="sse.html#callbackfromoutside" class="tsd-kind-icon">call<wbr>Back<wbr>From<wbr>Outside</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="sse.html#callbackofbigactionexecute" class="tsd-kind-icon">call<wbr>Back<wbr>OfBig<wbr>Action<wbr>Execute</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="sse.html#callbackofmodelupdated" class="tsd-kind-icon">call<wbr>Back<wbr>OfModel<wbr>Updated</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="sse.html#callbackofmodelupdatedfullmsg" class="tsd-kind-icon">call<wbr>Back<wbr>OfModel<wbr>Updated<wbr>Full<wbr>Msg</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="sse.html#callbackofmodelupdatedofdashboad" class="tsd-kind-icon">callback<wbr>OfModel<wbr>Updated<wbr>OfDashboad</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="sse.html#msg" class="tsd-kind-icon">msg</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="sse.html#msgmodel" class="tsd-kind-icon">msg<wbr>Model</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="sse.html#notifylistener" class="tsd-kind-icon">notify<wbr>Listener</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="sse.html#removesselistener" class="tsd-kind-icon">removeSSEListener</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="sse.html#transportmessagelistener" class="tsd-kind-icon">transport<wbr>Message<wbr>Listener</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Type aliases</h2>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="connectivityobserver" class="tsd-anchor"></a>
					<h3>Connectivity<wbr>Observer</h3>
					<div class="tsd-signature tsd-kind-icon">Connectivity<wbr>Observer<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>online<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1919</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>online<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>online: <span class="tsd-signature-type">boolean</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="menudatachangedlistener" class="tsd-anchor"></a>
					<h3>Menu<wbr>Data<wbr>Changed<wbr>Listener</h3>
					<div class="tsd-signature tsd-kind-icon">Menu<wbr>Data<wbr>Changed<wbr>Listener<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>v<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1918</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>v<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>v: <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="bigactionupdatelistener" class="tsd-anchor"></a>
					<h3>big<wbr>Action<wbr>Update<wbr>Listener</h3>
					<div class="tsd-signature tsd-kind-icon">big<wbr>Action<wbr>Update<wbr>Listener<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><a href="sse.html#msg" class="tsd-signature-type">msg</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1916</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><a href="sse.html#msg" class="tsd-signature-type">msg</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>e: <a href="sse.html#msg" class="tsd-signature-type">msg</a></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="callbackfromoutside" class="tsd-anchor"></a>
					<h3>call<wbr>Back<wbr>From<wbr>Outside</h3>
					<div class="tsd-signature tsd-kind-icon">call<wbr>Back<wbr>From<wbr>Outside<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>msg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1875</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>msg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5><span class="tsd-flag ts-flagOptional">Optional</span> msg: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="callbackofbigactionexecute" class="tsd-anchor"></a>
					<h3>call<wbr>Back<wbr>OfBig<wbr>Action<wbr>Execute</h3>
					<div class="tsd-signature tsd-kind-icon">call<wbr>Back<wbr>OfBig<wbr>Action<wbr>Execute<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>modelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, taskId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1881</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>modelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, taskId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>modelName: <span class="tsd-signature-type">string</span></h5>
											</li>
											<li>
												<h5>taskId: <span class="tsd-signature-type">number</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="callbackofmodelupdated" class="tsd-anchor"></a>
					<h3>call<wbr>Back<wbr>OfModel<wbr>Updated</h3>
					<div class="tsd-signature tsd-kind-icon">call<wbr>Back<wbr>OfModel<wbr>Updated<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>createByMyself<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1879</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<dl class="tsd-comment-tags">
							<dt>param</dt>
							<dd><p>修改模型数据的人是不是当前登录账号</p>
							</dd>
						</dl>
					</div>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>createByMyself<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>createByMyself: <span class="tsd-signature-type">boolean</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="callbackofmodelupdatedfullmsg" class="tsd-anchor"></a>
					<h3>call<wbr>Back<wbr>OfModel<wbr>Updated<wbr>Full<wbr>Msg</h3>
					<div class="tsd-signature tsd-kind-icon">call<wbr>Back<wbr>OfModel<wbr>Updated<wbr>Full<wbr>Msg<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>msg<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">msg</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1886</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>msg<span class="tsd-signature-symbol">: </span><a href="" class="tsd-signature-type">msg</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>msg: <a href="" class="tsd-signature-type">msg</a></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="callbackofmodelupdatedofdashboad" class="tsd-anchor"></a>
					<h3>callback<wbr>OfModel<wbr>Updated<wbr>OfDashboad</h3>
					<div class="tsd-signature tsd-kind-icon">callback<wbr>OfModel<wbr>Updated<wbr>OfDashboad<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>createByMyself<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span>, indexs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1892</li>
						</ul>
					</aside>
					<div class="tsd-comment tsd-typography">
						<dl class="tsd-comment-tags">
							<dt>param</dt>
							<dd><p>修改模型数据的人是不是当前登录账号</p>
							</dd>
							<dt>param</dt>
							<dd><p>后台数据已经更新了的图表</p>
							</dd>
						</dl>
					</div>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>createByMyself<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span>, indexs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>createByMyself: <span class="tsd-signature-type">boolean</span></h5>
											</li>
											<li>
												<h5>indexs: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="msg" class="tsd-anchor"></a>
					<h3>msg</h3>
					<div class="tsd-signature tsd-kind-icon">msg<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>createByMyself<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>dataUpdates<span class="tsd-signature-symbol">: </span><a href="sse.html#msgmodel" class="tsd-signature-type">msgModel</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>self<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1902</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-index-signature">
								<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><a href="sse.html#msgmodel" class="tsd-signature-type">msgModel</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>create<wbr>ByMyself<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>data<wbr>Updates<span class="tsd-signature-symbol">: </span><a href="sse.html#msgmodel" class="tsd-signature-type">msgModel</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>self<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="msgmodel" class="tsd-anchor"></a>
					<h3>msg<wbr>Model</h3>
					<div class="tsd-signature tsd-kind-icon">msg<wbr>Model<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>model<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>selectedList<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1897</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>model<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>selected<wbr>List<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="notifylistener" class="tsd-anchor"></a>
					<h3>notify<wbr>Listener</h3>
					<div class="tsd-signature tsd-kind-icon">notify<wbr>Listener<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>message<span class="tsd-signature-symbol">: </span><a href="sse.html#msg" class="tsd-signature-type">msg</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1917</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>message<span class="tsd-signature-symbol">: </span><a href="sse.html#msg" class="tsd-signature-type">msg</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>message: <a href="sse.html#msg" class="tsd-signature-type">msg</a></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="removesselistener" class="tsd-anchor"></a>
					<h3>removeSSEListener</h3>
					<div class="tsd-signature tsd-kind-icon">removeSSEListener<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1920</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="transportmessagelistener" class="tsd-anchor"></a>
					<h3>transport<wbr>Message<wbr>Listener</h3>
					<div class="tsd-signature tsd-kind-icon">transport<wbr>Message<wbr>Listener<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><a href="sse.html#msg" class="tsd-signature-type">msg</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1915</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>e<span class="tsd-signature-symbol">: </span><a href="sse.html#msg" class="tsd-signature-type">msg</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>e: <a href="sse.html#msg" class="tsd-signature-type">msg</a></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
					<li class="current tsd-kind-namespace">
						<a href="sse.html">SSE</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="sse.html#connectivityobserver" class="tsd-kind-icon">Connectivity<wbr>Observer</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="sse.html#menudatachangedlistener" class="tsd-kind-icon">Menu<wbr>Data<wbr>Changed<wbr>Listener</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="sse.html#bigactionupdatelistener" class="tsd-kind-icon">big<wbr>Action<wbr>Update<wbr>Listener</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="sse.html#callbackfromoutside" class="tsd-kind-icon">call<wbr>Back<wbr>From<wbr>Outside</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="sse.html#callbackofbigactionexecute" class="tsd-kind-icon">call<wbr>Back<wbr>OfBig<wbr>Action<wbr>Execute</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="sse.html#callbackofmodelupdated" class="tsd-kind-icon">call<wbr>Back<wbr>OfModel<wbr>Updated</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="sse.html#callbackofmodelupdatedfullmsg" class="tsd-kind-icon">call<wbr>Back<wbr>OfModel<wbr>Updated<wbr>Full<wbr>Msg</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="sse.html#callbackofmodelupdatedofdashboad" class="tsd-kind-icon">callback<wbr>OfModel<wbr>Updated<wbr>OfDashboad</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="sse.html#msg" class="tsd-kind-icon">msg</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="sse.html#msgmodel" class="tsd-kind-icon">msg<wbr>Model</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="sse.html#notifylistener" class="tsd-kind-icon">notify<wbr>Listener</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="sse.html#removesselistener" class="tsd-kind-icon">removeSSEListener</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="sse.html#transportmessagelistener" class="tsd-kind-icon">transport<wbr>Message<wbr>Listener</a>
					</li>
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>