import axios from "../../core/axios/index"
import type * as dto from "../../def/index"

export class Role<PERSON><PERSON> {
    public query() {
        return axios.get<dto.RoleTypes.queryRequestResult>(
            `general/model/uniplat_base.role/request/list/`
        )
    }

    public getAuthUsers(roleId: number) {
        return axios.get<dto.RoleTypes.getAuthUsers>(
            `general/model/uniplat_base.roleuser/request/list?role_id=${roleId}`
        )
    }

    /**
     * 全量权限
     * @deprecated 权限改造
     */
    public getGlobalRightsTree() {
        return axios.get<dto.RoleTypes.getGlobalRightsTree>(
            `general/model/uniplat_base.role_entry_right/request/tree/`
        )
    }

    public getNewGlobalRightsTree(xid: number) {
        return axios.get<dto.RoleTypes.getGlobalRightsTree>(
            `general/project/uniplat_base/service/uniplat_right/getEntryTree?xid=${xid}`
        )
    }

    /** @deprecated 权限改造 */
    public getRoleRights(roleId: number) {
        return axios.get<dto.RoleTypes.RoleRight[]>(
            `general/model/uniplat_base.role_entry_right/request/list?role_id=${roleId}`
        )
    }

    /** 权限改造 */
    public getNewRoleRights(roleId: number) {
        return axios.get<dto.RoleTypes.RoleRight[]>(
            `general/project/uniplat_base/service/uniplat_right/getRoleEntryRights?roleId=${roleId}`
        )
    }

    public saveRightsChange(params: dto.RoleTypes.SaveRightsChangeParams) {
        const f = new FormData()
        f.append("role_id", String(params.role_id))
        f.append("add_entrys", JSON.stringify(params.add_entrys))
        f.append("remove_entrys", JSON.stringify(params.remove_entrys))
        return axios.post<void>(
            `general/model/uniplat_base.role_entry_right/request/save/`,
            f
        )
    }

    public getRoleTreeByXid(xid: number) {
        const f = new FormData()
        f.append(
            "prefilters",
            JSON.stringify([{ property: "xid", value: xid }])
        )
        return axios.post<void>(`general/tree/uniplat_role/list/lazy/meta`, f)
    }

    public depriveUserRights(userId: number) {
        return axios.get<void>(
            `general/model/uniplat_base.roleuser/request/delete?id=${userId}`
        )
    }

    public getRoleUserVersion(userId: number) {
        return axios.get<dto.RoleTypes.getRoleUserVersionRequestResult>(
            `general/model/roleuser/key/${userId}/detail`
        )
    }
    public getRoleVersion(roleId: number) {
        return axios.get<dto.RoleTypes.getRoleUserVersionRequestResult>(
            `general/model/role/key/${roleId}/detail`
        )
    }

    /** @deprecated 权限改造 */
    public getModelEntries(model_name: string) {
        return axios.get<dto.RoleTypes.Entry[]>(
            `general/model/uniplat_base.role_entry_right/request/entry_list?model_name=${model_name}`
        )
    }

    public getDataSourceList() {
        return axios.get<dto.RoleTypes.DataSource[]>(
            `general/project/uniplat_base/service/right/getDataSourceList`
        )
    }

    public getNewDataSourceList() {
        return axios.get<dto.RoleTypes.DataSource[]>(
            `general/project/uniplat_base/service/uniplat_right/getDataSourceList`
        )
    }

    public getEntryList(model = "") {
        return axios.get<dto.RoleTypes.Entry[]>(
            `general/project/uniplat_base/service/right/getEntryList?model=${model}`
        )
    }

    public getNewEntryList(xid: number, model = "") {
        return axios.get<dto.RoleTypes.Entry[]>(
            `general/project/uniplat_base/service/uniplat_right/getEntryList?xid=${xid}&modelName=${model}`
        )
    }

    public getRoleEntryRights(roleId: number) {
        return axios.get<dto.RoleTypes.EntryRight[]>(
            `general/project/uniplat_base/service/right/getRoleEntryRights?roleId=${roleId}`
        )
    }

    public getNewRoleEntryRights(roleId: number) {
        return axios.get<dto.RoleTypes.EntryRight[]>(
            `general/project/uniplat_base/service/uniplat_right/getRoleEntryRights?roleId=${roleId}`
        )
    }
}
