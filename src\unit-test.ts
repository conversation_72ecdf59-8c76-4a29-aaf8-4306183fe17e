import jwtDecode from "jwt-decode"

import { buildFilters, getAllFilters, metaFilter } from "./core/tools/filter"
import { SdkListRowType, UniplatSdkExtender } from "./model/index/extend"
// eslint-disable-next-line import/order
import { UniplatSdk } from "./model/index/index"
export {
    UniplatSdk,
    getAllFilters,
    buildFilters,
    metaFilter,
    UniplatSdkExtender,
    SdkListRowType,
    jwtDecode as decodeJwt,
}

// eslint-disable-next-line import/order
import { enableStorageEncode } from "./helpers/crypto"

enableStorageEncode("test")

const sdk = new UniplatSdk()

sdk.global.jwtToken = 'aisdjaioshdoia shduoahsdouihasouihdoiq wheoqiw yeoqwy ehoqwyhe oqwyhe qouw'
sdk.global.getCurrentToken()
