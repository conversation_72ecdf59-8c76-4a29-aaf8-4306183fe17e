import { Axi<PERSON><PERSON>nst<PERSON>, AxiosRequestConfig, Canceler } from "axios"
import jwtDecode from "jwt-decode"

import { axiosFactory, setUnactiveCallback } from "../../core/axios/axios"
import { defaultAxiosBuilder } from "../../core/axios/default"
import { channel } from "../../core/broadcast-channel"
import { events } from "../../core/events"
import type { Events } from "../../core/events"
import { global } from "../../core/global"
import type { Global } from "../../core/global"
import { isInUniApp } from "../../core/in-uniapp"
import { tokenChecker as tokenChecker } from "../../core/token-manager"
import type * as dto from "../../def/index"
import { DomainService } from "../domain-service/domain-service"
import { AuthLogin } from "../login/auth"
import { BindAccount } from "../login/bind-account"
import { OAuthLogin } from "../login/o-auth"
import { PassportLogin } from "../login/passport"
import { mediaController } from "../media"
import { Model } from "../model/model"
import { Org } from "../org/org"
import { Report } from "../report/model"
import { Scene } from "../scene/scene"

import { PluginProvider } from "./../plugin-provider"
import * as api from "./api"
import { sseInvoker } from "./sse-invoker"

type voidFunc = () => void

export class UniplatSdk {
    constructor(
        private config: dto.SdkConstructorParams = {
            sse: false,
            ssr: false,
            disableRefreshToken: false,
        }
    ) {
        sseInvoker.init(config.sse)
        if (config.ssr) {
            this.global.ssr = true
        }
        this.global.useRootEntranceAsTokenPrefix =
            config.useRootEntranceAsTokenPrefix
        this.global.sse = config.sse
        this.global.disableRefreshToken = config.disableRefreshToken

        const oldAddTokenExpiring = events.addTokenExpiring.bind(events)
        this.events = events
        this.proxyEvents(oldAddTokenExpiring)
    }

    private _isLoggedin = false
    private seed?: number
    private overwriteRootEntranceNo = 0
    private disableLongSse = false

    public readonly configurationApi = api.configurationApi

    /** 用来添加事件监听的类 */
    public events: Events

    /** 一个用于共享变量的类 */
    public global: Global = global

    /** 是否登录了*/
    public get isLoggedIn() {
        return this._isLoggedin
    }

    public readonly mediaController = mediaController

    public readonly customPluginProvider = new PluginProvider()

    private proxyEvents(
        oldAddTokenExpiring: (cb: dto.SSE.callBackFromOutside) => void
    ) {
        this.events.addTokenExpiring = (cb: dto.SSE.callBackFromOutside) => {
            oldAddTokenExpiring((m) => {
                this.loggedout()
                cb(m)
            })
        }
    }

    private loggedin() {
        this._isLoggedin = true
    }

    private loggedout() {
        this._isLoggedin = false
        global.uid = ""
        tokenChecker.stop()
    }

    private afterLogin() {
        const u = jwtDecode<{ exp: number; sub: string; user_id: number }>(
            global.getCurrentToken()
        )
        if (u.user_id) {
            global.uid = u.user_id + ""
        }
        if (!global.disableRefreshToken) {
            tokenChecker.start()
        }
        this.config.sse && sseInvoker.initEventSource(!this.disableLongSse)
        this.loggedin()
        this.events.callLogedin()
    }

    public disableSseLongConnection() {
        this.disableLongSse = true
    }

    /**
     * 获取sdk所用的axios实例
     */
    public getAxios() {
        return axiosFactory.getAxios() as AxiosInstance
    }

    public get<T>(url: string, config?: AxiosRequestConfig) {
        return this.getAxios().get<void, T>(url, config)
    }

    public post<T>(url: string, data?: unknown, config?: AxiosRequestConfig) {
        return this.getAxios().post<void, T>(url, data, config)
    }

    /**
     * 注入broadcast-channel依赖
     */
    public injectDependency(params: dto.Index.injectDependencyParams) {
        if (params.broadcastChannel) {
            channel.save(
                params.broadcastChannel as Parameters<typeof channel.save>[0]
            )
        }
    }

    /**链接一个uniplat服务器 */
    public connect(params: dto.Index.connectParams) {
        global.baseUrl = params.baseUrl.endsWith("/")
            ? params.baseUrl
            : `${params.baseUrl}/`
        params.refreshInterval !== undefined &&
            (global.refreshInterval = params.refreshInterval)
        if (params.sseUrl != null) {
            global.SSEURL = params.sseUrl
        }
        const serviceOption = {
            adapter: params.axiosAdapter,
            useInterceptor: params.useInterceptor
        }
        !isInUniApp() &&
            Object.assign(serviceOption, { timeout: params.axiosTimeout })
        axiosFactory.init(serviceOption)
        defaultAxiosBuilder.init(serviceOption)
        const token = global.getCurrentToken()

        if (token) {
            console.log("已经登录了")
            this.afterLogin()
        } else {
            this.loggedout()
            console.log("还没有登录")
        }
    }

    /**
     * 获取sse链接状态
     */
    public getSSEConnectivity() {
        return sseInvoker.getSSEConnectivity()
    }

    /**
     * 获取日志列表
     */
    public getLogList() {
        return api.getLogList()
    }

    /**
     * 如果使用unipalt登录，需要填写验证码
     */
    public getVerifyImage() {
        this.seed = Math.round(Math.random() * 100000)
        return api.createVerifyCodeImageUrl(this.seed)
    }

    public getVerifyImageAndSeed() {
        this.seed = Math.round(Math.random() * 100000)
        return {
            seed: this.seed,
            img: api.createVerifyCodeImageUrl(this.seed),
        }
    }

    /**
     * 当sdk外面已经登录了，直接传token给sdk，让sdk也登录
     * @param params
     */
    public loginByToken(params: dto.Index.loginByTokenParams) {
        global.username = params.username ?? ""
        global.isSuperAdmin = params.isSuperUser ?? false
        global.jwtToken = params.token
        this.afterLogin()
    }

    /**
     * uniplat登录
     */
    public async login(params: dto.Index.loginParams) {
        if (this.seed == null) {
            throw new Error("先获取验证码")
        }
        const data = await api.login({
            username: params.username,
            password: params.password,
            rootEntrance: params.rootEntrance,
            seed: this.seed,
            codeToVerify: params.codeToVerify,
        })
        global.jwtToken = data.jwt
        global.isSuperAdmin = data.isSuperUser
        if (params.rootEntrance) {
            global.rootEntrance = params.rootEntrance
        }
        global.username = params.username

        this.afterLogin()
        return data
    }

    /**
     * 登出
     */
    public async logout(doLogout = false) {
        try {
            doLogout && (await api.logout2())
            // eslint-disable-next-line no-empty
        } catch {}
        global.clearJWTToken()
        global.isSuperAdmin = false
        sseInvoker.close()
        this.loggedout()
    }

    /**
     * 断开sdk与uniplat服务器的链接
     */
    public async disconnect() {
        this.logout()
        global.clear()
    }

    /**
     * 获取登录用户信息
     *
     */
    public async getUserInfo() {
        const data = await api.getUserInfo()
        global.username = data.username
        global.isSuperAdmin = data.isSuperUser
        global.uid = data.id.toString()
        return data
    }

    /**
     * 获取指定token对应的用户的信息
     *
     */
    public async getUserInfoByJwt(jwt: string) {
        const data = await api.getUserInfoByJwt(jwt)
        global.username = data.username
        global.isSuperAdmin = data.isSuperUser
        return data
    }

    public setInitData(initData: dto.Index.InitData) {
        global.initData = initData
    }

    /**
     * 获取一个scene类
     */
    public scene() {
        return new Scene()
    }

    public org() {
        return new Org()
    }

    public model(name: string) {
        return new Model(name)
    }

    public report(domain: string, reportName: string, secondaryApi?: string) {
        return new Report(domain, reportName, secondaryApi)
    }

    /**
     * 获取领域服务
     */
    public domainService(
        subProjectName: string,
        serviceName: string,
        apiName: string
    ) {
        return new DomainService(subProjectName, serviceName, apiName)
    }

    private waitForLogin(): [voidFunc, voidFunc] {
        let done: voidFunc = () => {
            throw new Error("waitForLogin方法出错")
        }

        let failed: voidFunc = () => {
            throw new Error("waitForLogin方法出错")
        }
        new Promise<void>((resolve, reject) => {
            done = resolve
            failed = reject
        })
            .then(() => {
                this.afterLogin()
            })
            .catch(() => {
                this.loggedout()
            })
        return [done, failed]
    }

    /**
     * 获取passport登录方法
     */
    public getPassportLogin() {
        return new PassportLogin(...this.waitForLogin())
    }

    /**
     * 获取auth登录方法
     */
    public getAuthLogin() {
        return new AuthLogin(...this.waitForLogin())
    }

    /**
     * 获取绑定小包和teammix账号的方法
     */
    public getBindAccountMethods() {
        return new BindAccount()
    }

    public getOAuthMethods() {
        return new OAuthLogin()
    }

    /**
     * 上传文件
     * @param file
     * @param config
     * @returns
     */
    public uploadFile(
        file: File,
        config?: {
            /**
             * 上传进度
             */
            onUploadProgress?: (e: {
                loaded: number
                total: number
                percent: number
            }) => void
            /**
             * 拿到取消方法
             */
            getCancelSource?: (cancel: Canceler) => void
            timeout?: number
        }
    ) {
        return mediaController.uploadFile(file, config)
    }

    /**
     * 上传文件V2
     */
    public uploadFileV2(
        file: File,
        config?: {
            onUploadProgress?: (e: {
                loaded: number
                total: number
                percent: number
            }) => void
            getCancelSource?: (cancel: Canceler) => void
            timeout?: number
        },
        modelName?: string
    ) {
        return mediaController.uploadFileV2(file, config, modelName)
    }

    /**
     * 微信上传文件
     */
    public uploadFileForWxApp(
        fileName: string,
        path: string,
        size: number,
        buffer: Uint8Array,
        getTask?: (any) => void,
        modelName?: string
    ) {
        return mediaController.uploadFileForWxApp(
            fileName,
            path,
            size,
            buffer,
            getTask,
            modelName
        )
    }

    /**
     * 微信上传文件
     * tempFile可以直接是base64图片
     */
    public uploadFileForUniApp(
        file:
            | {
                  tempFile: string
                  name: string
                  type?: string
                  base64?: boolean
              }
            | UniApp.ChooseFile
            | UniApp.ChooseImageSuccessCallbackResultFile
            | UniApp.ChooseFileSuccessCallbackResultFile
            | File,
        getTask?: (task: UniApp.UploadTask) => void
    ) {
        return mediaController.uploadFileForUniApp(file, getTask)
    }

    /**
     * 下载一个位于指定url的文件
     */
    public downloadFile(url: string) {
        return mediaController.downloadFile(url)
    }

    /**
     * 薪酬，工资表上传/上传其他文件
     */
    public uploadFileOthers(file: File, modelName?: string) {
        return mediaController.uploadFileOthers(file, modelName)
    }

    /**
     * 设置用户在非活动状态下时限，如果达到此时限，则调用相应回调函数
     * @param duration 时限，单位为毫秒
     * @param trigger 回调
     */
    public setUnActiveTimeout(duration: number, trigger: () => void) {
        setUnactiveCallback(duration, trigger)
    }

    public overwriteRootEntrance(e: string) {
        const instance = axiosFactory.getAxios()
        this.overwriteRootEntranceNo = instance.interceptors.request.use(
            (config) => {
                if (config.headers) {
                    config.headers.Entrance = encodeURIComponent(
                        decodeURIComponent(e)
                    )
                }
                return config
            }
        )
    }

    public disposeOverwriteRootEntrance() {
        if (this.overwriteRootEntranceNo) {
            const instance = axiosFactory.getAxios()
            instance.interceptors.request.eject(this.overwriteRootEntranceNo)
            this.overwriteRootEntranceNo = 0
        }
    }
}

export { sseInvoker }
