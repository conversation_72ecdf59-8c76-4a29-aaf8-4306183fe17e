import type * as dto from "../../def/index"

import GlobalSearchApi from "./api"
export class GlobalSearch {
    private api: GlobalSearchApi
    constructor() {
        this.api = new GlobalSearchApi()
    }

    public search(params: dto.GlobalSearchTypes.searchApiParams) {
        return this.api.search(params)
    }
    public exportData(keyword: string) {
        return this.api.exportData(keyword)
    }
    public exportZip() {
        return this.api.exportZip()
    }

    public applicationSearch(
        application: string,
        entrance: string,
        p: dto.GlobalSearchTypes.ApplicationSearchParameter
    ) {
        return this.api.applicationSearch(application, entrance, p)
    }
}
