import cloneDeep from "lodash/cloneDeep"

import { AnonymousModel } from "../../core/anonymous-api"
import { events } from "../../core/events"
import { createFiltersHandler } from "../../core/handle-filter"
import { buildFilters, getAllFilters } from "../../core/tools/filter"
import { exportToExcelForList } from "../../core/tools/sse"
import type * as dto from "../../def/index"

import { listApi, listApi2 } from "./api"
import { ListQuery, ListQuery2 } from "./list-query"
import type { PagesColumns } from "./list-query"
import { Workflow } from "./workflow"

const createInitErrorMsg = () => new Error("请先调用query方法初始化数据")

class BaseList extends AnonymousModel {
    protected metaModelName = ""
}

export class List<RowType extends dto.ListRow = dto.ListRow> extends BaseList {
    protected api: listApi
    private callbackOnChange: dto.SSE.callBackOfModelUpdated = () => null
    private _listQuery?: ListQuery<RowType>
    private listQueryParams?: dto.ListTypes.fullfilledQueryProps
    private pagesColumns: PagesColumns = {}
    private leftTree?: dto.ListTypes.getListDataRequestResult["meta"]["tree"]

    constructor(private props: dto.ListTypes.constructor) {
        super()
        this.api = new listApi(this.props.model_name)
    }

    protected get modelName() {
        return this.props.model_name
    }

    /**
     * 获取工作流类
     */
    public getWorkflow(params: dto.ListTypes.getWorkflow) {
        if (this.listQueryParams == null) {
            throw createInitErrorMsg()
        }
        const allFilters = {
            filters: getAllFilters(this.listQueryParams.filters),
            tagFilters: this.listQueryParams.tagFilters,
            filters4Workflow: this.listQueryParams.filters4Workflow,
            prefilters: this.listQueryParams.prefilters,
            order_obj: this.listQueryParams.order_obj,
            page_index: 0,
            columns: ["*"],
        }
        return new Workflow({
            model_name: this.props.model_name,
            allFilters,
            ...params,
        })
    }

    private isPageIndex(
        params: dto.ListTypes.queryProps
    ): params is dto.ListTypes.queryPropsPage {
        return (params as dto.ListTypes.queryPropsPage).pageIndex != null
    }

    public fullfillParams(params: dto.ListTypes.queryProps) {
        const result = {
            ...params,
            prefilters: params.prefilters || [],
            columns: params.columns || ["*"],
            order_obj: params.order_obj || {},
            filters: params.filters || [],
            tagFilters: params.tagFilters || [],
            sorts: params.sorts || [],
        }

        if (this.isPageIndex(params)) {
            if (params.pageIndex <= 0) {
                throw new Error("页码不能小于1")
            }
            const item_index = (params.pageIndex - 1) * params.item_size
            return {
                ...result,
                item_index,
            } as dto.ListTypes.fullfilledQueryProps
        }
        return result as dto.ListTypes.fullfilledQueryProps
    }

    private onTransportMessage = (msg: dto.SSE.msg) => {
        if (!this._listQuery) {
            return
        }
        const models = msg.dataUpdates.filter(
            (x) => x.model === this.metaModelName || this.props.model_name
        )
        if (models.length === 0) return
        const createByMyself = msg.createByMyself
        if (this._listQuery.isInFirstPage() && createByMyself) {
            this.callbackOnChange(createByMyself)
            return
        }
        const keys = this._listQuery.getAllShownRowsKeys()
        const ifNeedRefresh = models.some((model) => {
            const selectedList = model?.selectedList || []
            if (selectedList.length === 0) return false
            return selectedList.some((wantedKey) =>
                keys.some((key) => String(key) === String(wantedKey))
            )
        })

        if (!ifNeedRefresh) return
        this.callbackOnChange(createByMyself)
    }
    /**
     * 更新action
     * @param params
     */
    public updateAction(params: dto.ListTypes.updateAction) {
        if (this.listQueryParams == null) {
            throw createInitErrorMsg()
        }
        return this.api.updateAction({
            pageName: params.pageName,
            selectedList: params.selectedList,
            actionParams: params.actionParams,
            prefilters: this.listQueryParams.prefilters,
            name: this.props.list_name,
        })
    }
    /**
     * 注册当前模型的数据变化时的回掉
     * @param CallBackOfModelUpdated
     */
    public registerOnChange(cb: dto.SSE.callBackOfModelUpdated) {
        this.callbackOnChange = cb
        return events.addTransportMessageListener(this.onTransportMessage)
    }

    /**
     * 查询list页面的基本数据
     * 返回值中pageData是页面基本数据
     * getList是获取列表的方法。
     * 查询条件一定不变化时，可以使用getlist方法进行分页查询
     * 查询条件变化，就有重新调用本方法
     *
     */
    public query(listQueryParams: dto.ListTypes.queryProps) {
        return this.getListQuery(listQueryParams)
    }

    public queryMeta(listQueryParams: dto.ListTypes.queryProps) {
        return this.getListQuery(listQueryParams, true)
    }

    private async getListQuery(
        listQueryParams: dto.ListTypes.queryProps,
        isMeta?: boolean
    ) {
        this.listQueryParams = this.fullfillParams(listQueryParams)
        const result = await new Promise<dto.ListTypes.IQueryResult<RowType>>(
            (done, failed) => {
                if (this.listQueryParams == null) {
                    throw createInitErrorMsg()
                }
                this._listQuery = new ListQuery<RowType>(
                    this.api,
                    { ...this.props, pagesColumns: this.pagesColumns },
                    this.listQueryParams,
                    done,
                    failed,
                    isMeta
                )
            }
        )
        this.leftTree = result.pageData.meta.tree
        if (this.listQueryParams.filters.length === 0) {
            const { pageData } = result
            this.listQueryParams.filters = buildFilters(
                pageData.meta.filters,
                this.listQueryParams.prefilters
            )
        }
        this.metaModelName = result.pageData.meta.modelName
        return result
    }

    public async getPageCount(listQueryParams?: dto.ListTypes.queryProps) {
        if (listQueryParams) {
            const queryParams = this.fullfillParams(listQueryParams)
            const params = cloneDeep(queryParams)
            return this._listQuery.getPageCounts(params)
        }
        return this._listQuery.getPageCounts(this.listQueryParams)
    }

    public async getPageCountV2(
        listQueryParams?: dto.ListTypes.queryPropsEasy
    ) {
        if (listQueryParams) {
            const queryParams = this.fullfillParams({
                ...listQueryParams,
                filters: this.handleFilters(listQueryParams.filters ?? []),
                prefilters:
                    listQueryParams?.prefilters ??
                    this.listQueryParams.prefilters,
            })
            const params = cloneDeep(queryParams)
            if (!listQueryParams.filters) {
                params.filters = this.listQueryParams.filters
            }
            return this._listQuery.getPageCounts(params)
        }
        return this._listQuery.getPageCounts(this.listQueryParams)
    }

    /**
     * 如果查询条件中有一项可以跟随另一项自动填写，则调用本方法
     */
    public updateFilterParam(
        property: string,
        filters: dto.filter[],
        item_index: number,
        item_size: number
    ) {
        if (this._listQuery == null) {
            throw createInitErrorMsg()
        }
        return this._listQuery.updateFilterParam(
            property,
            filters,
            item_index,
            item_size
        )
    }

    public getFilterGroup(parameters: unknown) {
        return this.api.getfilterGroupDetail(parameters)
    }

    public setColumnsForPages(params: dto.ListTypes.setColumnsForPagesParams) {
        // 保存每个页签的表头设置
        // 用于之后查询数据
        this.pagesColumns[params.page_name] = params.columns
    }

    /**
     * 获取行详情
     *  keyFieldValue: row的keyfield的值
     */
    public getRowDetail(keyFieldValue: string) {
        return this.api.getRowDetail(keyFieldValue)
    }

    /**
     * 导出列表为excel
     * @param ListExportExcelParams
     * @returns 返回一个开始导出的方法startDownload, 新方法有一个参数用来监听导出进度
     * 返回的方法是一个Promise，resolve时得到exlce下载链接
     */
    public exportToExcel(params: dto.ListTypes.exportExcelParams = {}) {
        if (this.listQueryParams == null) {
            throw createInitErrorMsg()
        }
        const { export_template, page_name, template_name, pages } = params

        const parametersObj = {
            name: this.props.list_name,
            filters: getAllFilters(
                this.listQueryParams.filters,
                this.listQueryParams.slave_filters
            ),
            prefilters: this.listQueryParams.prefilters,
            order_obj: this.listQueryParams.order_obj,
            workflowType: this.listQueryParams.workflowType,
            tabName: this.listQueryParams.tabName,
            tagFilters: this.listQueryParams.tagFilters,
            pages,
            page_name,
            template_name,
            export_template,
        }
        return exportToExcelForList(this.api.createExportUrl(parametersObj))
    }

    /**
     * exportToExcelV2
     */
    public exportToExcelV2(
        eid: string,
        headers?: { CurrentOrg?: string; Entrance?: string }
    ) {
        return exportToExcelForList(this.api.createExportUrlV2(eid), headers)
    }

    public exportToWord(
        eid: string,
        headers?: { CurrentOrg?: string; Entrance?: string }
    ) {
        return exportToExcelForList(this.api.createExportUrl2Word(eid), headers)
    }

    /**
     * 导出为csv
     */
    public exportToExcelV2ForCsv(
        eid: string,
        headers?: { CurrentOrg?: string; Entrance?: string }
    ) {
        return exportToExcelForList(
            this.api.createExportUrlV2ForCsv(eid),
            headers
        )
    }

    /**
     * postParamsForExcel
     */
    public postParamsForExcel(params: dto.ListTypes.exportExcelParams = {}) {
        if (this.listQueryParams == null) {
            throw createInitErrorMsg()
        }
        const { export_template, page_name, template_name, pages } = params

        const parametersObj = {
            name: this.props.list_name,
            filters: Array.isArray(this.listQueryParams.filters)
                ? getAllFilters(
                      this.listQueryParams.filters,
                      this.listQueryParams.slave_filters
                  )
                : this.listQueryParams.filters,
            prefilters: this.listQueryParams.prefilters,
            order_obj: this.listQueryParams.order_obj,
            workflowType: this.listQueryParams.workflowType,
            tabName: this.listQueryParams.tabName,
            tagFilters: this.listQueryParams.tagFilters,
            pages,
            page_name,
            template_name,
            export_template,
        }

        return this.api.postParam4Excel(parametersObj)
    }

    /**
     * 得到某个标签页的模版
     * @param tabName
     * @returns Promise<url>
     */
    public getDefaultTemplateUrl(tabName = "") {
        return this.api.getDefaultTemplateUrl(this.props.list_name, tabName)
    }
    /**
     * 得到所有模版
     * @returns Promise<url>
     */
    public getAlltemplateUrl() {
        return this.api.getAllDefaultTemplateUrl(this.props.list_name)
    }

    /**
     * 传入简单的键值对数组，放回sdk所需的filters参数
     * @param keyValueFilter
     */
    protected handleFilters(keyValueFilter: dto.ListTypes.KeyValueFilters) {
        if (keyValueFilter.length === 0) return []
        if (this._listQuery == null) {
            throw createInitErrorMsg()
        }
        return createFiltersHandler(
            this._listQuery.rawFilters,
            this.leftTree
        )(keyValueFilter)
    }

    public removeFilters(items: dto.filter[]) {
        const keys = items.map((i) => i.property)
        if (this.listQueryParams) {
            this.listQueryParams.filters = this.listQueryParams.filters.filter(
                (i) => !keys.includes(i.property)
            )
        }
    }

    public updateListName(name: string) {
        this.props.list_name = name
        this._listQuery && this._listQuery.updateListName(name)
    }
}

export class List2 extends BaseList {
    protected api: listApi2
    private callbackOnChange: dto.SSE.callBackOfModelUpdated = () => null
    private _listQuery?: ListQuery2
    private listQueryParams?: dto.ListTypes.fullfilledQueryProps
    private pagesColumns: PagesColumns = {}
    private leftTree?: dto.ListTypes.getListDataRequestResult2["meta"]["tree"]

    constructor(private props: dto.ListTypes.constructor) {
        super()
        this.api = new listApi2(this.props.model_name)
    }

    protected get modelName() {
        return this.props.model_name
    }

    /**
     * 获取工作流类
     */
    public getWorkflow(params: dto.ListTypes.getWorkflow) {
        if (this.listQueryParams == null) {
            throw createInitErrorMsg()
        }
        const allFilters = {
            filters: getAllFilters(this.listQueryParams.filters),
            tagFilters: this.listQueryParams.tagFilters,
            filters4Workflow: this.listQueryParams.filters4Workflow,
            prefilters: this.listQueryParams.prefilters,
            order_obj: this.listQueryParams.order_obj,
            page_index: 0,
            columns: ["*"],
        }
        return new Workflow({
            model_name: this.props.model_name,
            allFilters,
            ...params,
        })
    }

    private isPageIndex(
        params: dto.ListTypes.queryProps
    ): params is dto.ListTypes.queryPropsPage {
        return (params as dto.ListTypes.queryPropsPage).pageIndex != null
    }

    public fullfillParams(params: dto.ListTypes.queryProps) {
        const result = {
            ...params,
            prefilters: params.prefilters || [],
            columns: params.columns || ["*"],
            order_obj: params.order_obj || {},
            filters: params.filters || [],
            tagFilters: params.tagFilters || [],
            sorts: params.sorts || [],
        }

        if (this.isPageIndex(params)) {
            if (params.pageIndex <= 0) {
                throw new Error("页码不能小于1")
            }
            const item_index = (params.pageIndex - 1) * params.item_size
            return {
                ...result,
                item_index,
            } as dto.ListTypes.fullfilledQueryProps
        }
        return result as dto.ListTypes.fullfilledQueryProps
    }

    private onTransportMessage = (msg: dto.SSE.msg) => {
        if (!this._listQuery) {
            return
        }
        const models = msg.dataUpdates.filter(
            (x) => x.model === this.metaModelName || this.props.model_name
        )
        if (models.length === 0) return
        const createByMyself = msg.createByMyself
        if (this._listQuery.isInFirstPage() && createByMyself) {
            this.callbackOnChange(createByMyself)
            return
        }
        const keys = this._listQuery.getAllShownRowsKeys()
        const ifNeedRefresh = models.some((model) => {
            const selectedList = model?.selectedList || []
            if (selectedList.length === 0) return false
            return selectedList.some((wantedKey) =>
                keys.some((key) => String(key) === String(wantedKey))
            )
        })

        if (!ifNeedRefresh) return
        this.callbackOnChange(createByMyself)
    }
    /**
     * 更新action
     * @param params
     */
    public updateAction(params: dto.ListTypes.updateAction) {
        if (this.listQueryParams == null) {
            throw createInitErrorMsg()
        }
        return this.api.updateAction({
            pageName: params.pageName,
            selectedList: params.selectedList,
            actionParams: params.actionParams,
            prefilters: this.listQueryParams.prefilters,
            name: this.props.list_name,
        })
    }
    /**
     * 注册当前模型的数据变化时的回掉
     * @param CallBackOfModelUpdated
     */
    public registerOnChange(cb: dto.SSE.callBackOfModelUpdated) {
        this.callbackOnChange = cb
        return events.addTransportMessageListener(this.onTransportMessage)
    }

    /**
     * 查询list页面的基本数据
     * 返回值中pageData是页面基本数据
     * getList是获取列表的方法。
     * 查询条件一定不变化时，可以使用getlist方法进行分页查询
     * 查询条件变化，就有重新调用本方法
     *
     */
    public query(listQueryParams: dto.ListTypes.queryProps) {
        return this.getListQuery(listQueryParams)
    }

    public queryMeta(listQueryParams: dto.ListTypes.queryProps) {
        return this.getListQuery(listQueryParams, true)
    }

    private async getListQuery(
        listQueryParams: dto.ListTypes.queryProps,
        isMeta?: boolean
    ) {
        this.listQueryParams = this.fullfillParams(listQueryParams)
        const result = await new Promise<dto.ListTypes.IQueryResult2>(
            (done, failed) => {
                if (this.listQueryParams == null) {
                    throw createInitErrorMsg()
                }
                this._listQuery = new ListQuery2(
                    this.api,
                    { ...this.props, pagesColumns: this.pagesColumns },
                    this.listQueryParams,
                    done,
                    failed,
                    isMeta
                )
            }
        )
        this.leftTree = result.pageData.meta.tree
        if (this.listQueryParams.filters.length === 0) {
            const { pageData } = result
            this.listQueryParams.filters = buildFilters(
                pageData.meta.filters,
                this.listQueryParams.prefilters
            )
        }
        this.metaModelName = result.pageData.meta.modelName
        return result
    }

    public async getPageCount(listQueryParams?: dto.ListTypes.queryProps) {
        if (listQueryParams) {
            const queryParams = this.fullfillParams(listQueryParams)
            const params = cloneDeep(queryParams)
            return this._listQuery.getPageCounts(params)
        }
        return this._listQuery.getPageCounts(this.listQueryParams)
    }

    public async getPageCountV2(
        listQueryParams?: dto.ListTypes.queryPropsEasy
    ) {
        if (listQueryParams) {
            const queryParams = this.fullfillParams({
                ...listQueryParams,
                filters: this.handleFilters(listQueryParams.filters ?? []),
                prefilters:
                    listQueryParams?.prefilters ??
                    this.listQueryParams.prefilters,
            })
            const params = cloneDeep(queryParams)
            if (!listQueryParams.filters) {
                params.filters = this.listQueryParams.filters
            }
            return this._listQuery.getPageCounts(params)
        }
        return this._listQuery.getPageCounts(this.listQueryParams)
    }

    /**
     * 如果查询条件中有一项可以跟随另一项自动填写，则调用本方法
     */
    public updateFilterParam(
        property: string,
        filters: dto.filter[],
        item_index: number,
        item_size: number
    ) {
        if (this._listQuery == null) {
            throw createInitErrorMsg()
        }
        return this._listQuery.updateFilterParam(
            property,
            filters,
            item_index,
            item_size
        )
    }

    public getFilterGroup(parameters: unknown) {
        return this.api.getfilterGroupDetail(parameters)
    }

    public setColumnsForPages(params: dto.ListTypes.setColumnsForPagesParams) {
        // 保存每个页签的表头设置
        // 用于之后查询数据
        this.pagesColumns[params.page_name] = params.columns
    }

    /**
     * 获取行详情
     *  keyFieldValue: row的keyfield的值
     */
    public getRowDetail(keyFieldValue: string) {
        return this.api.getRowDetail(keyFieldValue)
    }

    /**
     * 导出列表为excel
     * @param ListExportExcelParams
     * @returns 返回一个开始导出的方法startDownload, 新方法有一个参数用来监听导出进度
     * 返回的方法是一个Promise，resolve时得到exlce下载链接
     */
    public exportToExcel(params: dto.ListTypes.exportExcelParams = {}) {
        if (this.listQueryParams == null) {
            throw createInitErrorMsg()
        }
        const { export_template, page_name, template_name, pages } = params

        const parametersObj = {
            name: this.props.list_name,
            filters: getAllFilters(
                this.listQueryParams.filters,
                this.listQueryParams.slave_filters
            ),
            prefilters: this.listQueryParams.prefilters,
            order_obj: this.listQueryParams.order_obj,
            workflowType: this.listQueryParams.workflowType,
            tabName: this.listQueryParams.tabName,
            tagFilters: this.listQueryParams.tagFilters,
            pages,
            page_name,
            template_name,
            export_template,
        }
        return exportToExcelForList(this.api.createExportUrl(parametersObj))
    }

    /**
     * exportToExcelV2
     */
    public exportToExcelV2(
        eid: string,
        headers?: { CurrentOrg?: string; Entrance?: string }
    ) {
        return exportToExcelForList(this.api.createExportUrlV2(eid), headers)
    }

    public exportToWord(
        eid: string,
        headers?: { CurrentOrg?: string; Entrance?: string }
    ) {
        return exportToExcelForList(this.api.createExportUrl2Word(eid), headers)
    }

    /**
     * 导出为csv
     */
    public exportToExcelV2ForCsv(
        eid: string,
        headers?: { CurrentOrg?: string; Entrance?: string }
    ) {
        return exportToExcelForList(
            this.api.createExportUrlV2ForCsv(eid),
            headers
        )
    }

    /**
     * postParamsForExcel
     */
    public postParamsForExcel(params: dto.ListTypes.exportExcelParams = {}) {
        if (this.listQueryParams == null) {
            throw createInitErrorMsg()
        }
        const { export_template, page_name, template_name, pages } = params

        const parametersObj = {
            name: this.props.list_name,
            filters: Array.isArray(this.listQueryParams.filters)
                ? getAllFilters(
                      this.listQueryParams.filters,
                      this.listQueryParams.slave_filters
                  )
                : this.listQueryParams.filters,
            prefilters: this.listQueryParams.prefilters,
            order_obj: this.listQueryParams.order_obj,
            workflowType: this.listQueryParams.workflowType,
            tabName: this.listQueryParams.tabName,
            tagFilters: this.listQueryParams.tagFilters,
            pages,
            page_name,
            template_name,
            export_template,
        }

        return this.api.postParam4Excel(parametersObj)
    }

    /**
     * 得到某个标签页的模版
     * @param tabName
     * @returns Promise<url>
     */
    public getDefaultTemplateUrl(tabName = "") {
        return this.api.getDefaultTemplateUrl(this.props.list_name, tabName)
    }
    /**
     * 得到所有模版
     * @returns Promise<url>
     */
    public getAlltemplateUrl() {
        return this.api.getAllDefaultTemplateUrl(this.props.list_name)
    }

    /**
     * 传入简单的键值对数组，放回sdk所需的filters参数
     * @param keyValueFilter
     */
    protected handleFilters(keyValueFilter: dto.ListTypes.KeyValueFilters) {
        if (keyValueFilter.length === 0) return []
        if (this._listQuery == null) {
            throw createInitErrorMsg()
        }
        return createFiltersHandler(
            this._listQuery.rawFilters,
            this.leftTree
        )(keyValueFilter)
    }

    public removeFilters(items: dto.filter[]) {
        const keys = items.map((i) => i.property)
        if (this.listQueryParams) {
            this.listQueryParams.filters = this.listQueryParams.filters.filter(
                (i) => !keys.includes(i.property)
            )
        }
    }

    public updateListName(name: string) {
        this.props.list_name = name
        this._listQuery && this._listQuery.updateListName(name)
    }
}
