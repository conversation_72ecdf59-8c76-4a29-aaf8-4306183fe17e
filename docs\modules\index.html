<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Index | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="index.html">Index</a>
				</li>
			</ul>
			<h1>Namespace Index</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Type aliases</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#appadminconfig" class="tsd-kind-icon">App<wbr>Admin<wbr>Config</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#appinstanceconfig" class="tsd-kind-icon">App<wbr>Instance<wbr>Config</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#applicationconfig" class="tsd-kind-icon">Application<wbr>Config</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#baseapplicationconfig" class="tsd-kind-icon">Base<wbr>Application<wbr>Config</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#bindpassportparams" class="tsd-kind-icon">Bind<wbr>Passport<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#bindpassportrequestresult" class="tsd-kind-icon">Bind<wbr>Passport<wbr>Request<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#entrance" class="tsd-kind-icon">Entrance</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#entrancetoolbaritem" class="tsd-kind-icon">Entrance<wbr>Toolbar<wbr>Item</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#entrancetoolbaritemsubitem" class="tsd-kind-icon">Entrance<wbr>Toolbar<wbr>Item<wbr>Sub<wbr>Item</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#initconfig" class="tsd-kind-icon">Init<wbr>Config</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#initdata" class="tsd-kind-icon">Init<wbr>Data</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#initialdata" class="tsd-kind-icon">Initial<wbr>Data</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#menuitem" class="tsd-kind-icon">Menu<wbr>Item</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#scene" class="tsd-kind-icon">Scene</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#sceneconfig" class="tsd-kind-icon">Scene<wbr>Config</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#authlogin" class="tsd-kind-icon">auth<wbr>Login</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#authloginbind" class="tsd-kind-icon">auth<wbr>Login<wbr>Bind</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#authloginbindparams" class="tsd-kind-icon">auth<wbr>Login<wbr>Bind<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#bindstatusrequestresult" class="tsd-kind-icon">bind<wbr>Status<wbr>Request<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#connectparams" class="tsd-kind-icon">connect<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#getuserinfo" class="tsd-kind-icon">get<wbr>User<wbr>Info</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#injectdependencyparams" class="tsd-kind-icon">inject<wbr>Dependency<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#loginbypassportrequestresult" class="tsd-kind-icon">login<wbr>ByPassport<wbr>Request<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#loginbytokenparams" class="tsd-kind-icon">login<wbr>ByToken<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#loginparams" class="tsd-kind-icon">login<wbr>Params</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#loginrequestresult" class="tsd-kind-icon">login<wbr>Request<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#refreshtokenrequestresult" class="tsd-kind-icon">refresh<wbr>Token<wbr>Request<wbr>Result</a></li>
								<li class="tsd-kind-type-alias tsd-parent-kind-namespace"><a href="index.html#reloadapiresult" class="tsd-kind-icon">reload<wbr>Api<wbr>Result</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Type aliases</h2>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="appadminconfig" class="tsd-anchor"></a>
					<h3>App<wbr>Admin<wbr>Config</h3>
					<div class="tsd-signature tsd-kind-icon">App<wbr>Admin<wbr>Config<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>applicationTitles<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>entrances<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>icon<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1717</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>application<wbr>Titles<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>entrances<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>icon<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="appinstanceconfig" class="tsd-anchor"></a>
					<h3>App<wbr>Instance<wbr>Config</h3>
					<div class="tsd-signature tsd-kind-icon">App<wbr>Instance<wbr>Config<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>organizationTitles<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>applicationInstanceTitleMetas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>entrances<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>icon<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>oid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1698</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>organization<wbr>Titles<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>applicationInstanceTitleMetas<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>entrances<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>icon<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>oid<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="applicationconfig" class="tsd-anchor"></a>
					<h3>Application<wbr>Config</h3>
					<div class="tsd-signature tsd-kind-icon">Application<wbr>Config<span class="tsd-signature-symbol">:</span> <a href="index.html#baseapplicationconfig" class="tsd-signature-type">BaseApplicationConfig</a><span class="tsd-signature-symbol"> &amp; </span><a href="index.html#appadminconfig" class="tsd-signature-type">AppAdminConfig</a><span class="tsd-signature-symbol"> &amp; </span><a href="index.html#appinstanceconfig" class="tsd-signature-type">AppInstanceConfig</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1729</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="baseapplicationconfig" class="tsd-anchor"></a>
					<h3>Base<wbr>Application<wbr>Config</h3>
					<div class="tsd-signature tsd-kind-icon">Base<wbr>Application<wbr>Config<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>copyright<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>customHomeRedirect<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>customHomeRedirectPath<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>loginBg<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>loginEntranceTitle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>loginMethod<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>oidc<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>qqxbBackLogin<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>usernamePassword<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>loginSlogan<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>show<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>url<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>projectTitle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showLoginEntrance<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>slogan<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1665</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> copyright<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>copyright 可不配置，不配置的话会有默认配置</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>custom<wbr>Home<wbr>Redirect<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>custom<wbr>Home<wbr>Redirect<wbr>Path<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>login<wbr>Bg<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>login<wbr>Entrance<wbr>Title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> login<wbr>Method<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>oidc<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>qqxbBackLogin<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>usernamePassword<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> }</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>是否显示slogan图标，默认显示，若不想要，需要配置为 false</p>
									</div>
								</div>
								<ul class="tsd-parameters">
									<li class="tsd-parameter">
										<h5><span class="tsd-flag ts-flagOptional">Optional</span> oidc<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
										<div class="tsd-comment tsd-typography">
											<div class="lead">
												<p>是否启用老Passport登录，不配置默认true，若不想要，需要设置为 false</p>
											</div>
										</div>
									</li>
									<li class="tsd-parameter">
										<h5><span class="tsd-flag ts-flagOptional">Optional</span> qqxb<wbr>Back<wbr>Login<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
										<div class="tsd-comment tsd-typography">
											<div class="lead">
												<p>是否启用老小保后台登录，不配置默认true，若不想要，需要设置为 false</p>
											</div>
										</div>
									</li>
									<li class="tsd-parameter">
										<h5><span class="tsd-flag ts-flagOptional">Optional</span> username<wbr>Password<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
										<div class="tsd-comment tsd-typography">
											<div class="lead">
												<p>是否启用用户名密码登录，不配置默认true，若不想要，需要设置为 false</p>
											</div>
										</div>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> login<wbr>Slogan<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>show<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>url<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>左侧登录slogan相关配置。可不配置，不配置的话会有默认配置</p>
									</div>
								</div>
								<ul class="tsd-parameters">
									<li class="tsd-parameter">
										<h5><span class="tsd-flag ts-flagOptional">Optional</span> show<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
										<div class="tsd-comment tsd-typography">
											<div class="lead">
												<p>是否显示slogan图标，默认显示，若不想要，需要配置为 false</p>
											</div>
										</div>
									</li>
									<li class="tsd-parameter">
										<h5><span class="tsd-flag ts-flagOptional">Optional</span> url<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
										<div class="tsd-comment tsd-typography">
											<div class="lead">
												<p>slogan 图标地址，有默认值，可不配置</p>
											</div>
										</div>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>project<wbr>Title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>show<wbr>Login<wbr>Entrance<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>slogan<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="bindpassportparams" class="tsd-anchor"></a>
					<h3>Bind<wbr>Passport<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">Bind<wbr>Passport<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>password<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>token<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>username<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1623</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>password<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>token<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>username<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="bindpassportrequestresult" class="tsd-anchor"></a>
					<h3>Bind<wbr>Passport<wbr>Request<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">Bind<wbr>Passport<wbr>Request<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>isSuperUser<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>jwt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>username<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1618</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>is<wbr>Super<wbr>User<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>jwt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>username<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="entrance" class="tsd-anchor"></a>
					<h3>Entrance</h3>
					<div class="tsd-signature tsd-kind-icon">Entrance<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>application<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>canUseSystemWithoutScene<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>features<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>chat<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>todo<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>globalSearchTypes<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>key<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>homepage<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>homepageFullscreen<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>items<span class="tsd-signature-symbol">: </span><a href="index.html#menuitem" class="tsd-signature-type">MenuItem</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>notExistSceneItems<span class="tsd-signature-symbol">?: </span><a href="index.html#menuitem" class="tsd-signature-type">MenuItem</a><span class="tsd-signature-symbol">; </span>order<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>sectionRefs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>modelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>sectionName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>sub_project_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>toolbar<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>header<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>enhancer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>left<span class="tsd-signature-symbol">: </span><a href="index.html#entrancetoolbaritem" class="tsd-signature-type">EntranceToolbarItem</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>right<span class="tsd-signature-symbol">: </span><a href="index.html#entrancetoolbaritem" class="tsd-signature-type">EntranceToolbarItem</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>tooltip<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1823</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>application<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> can<wbr>Use<wbr>System<wbr>Without<wbr>Scene<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> features<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>chat<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>todo<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> }</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter">
										<h5>chat<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
										<div class="tsd-comment tsd-typography">
											<div class="lead">
												<p>是否启用【消息】和【会话】顶级侧边栏入口</p>
											</div>
										</div>
									</li>
									<li class="tsd-parameter">
										<h5>todo<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
										<div class="tsd-comment tsd-typography">
											<div class="lead">
												<p>是否启用【待办】顶级侧边栏入口</p>
											</div>
										</div>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> global<wbr>Search<wbr>Types<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">{ </span>key<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>homepage<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>homepage<wbr>Fullscreen<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>items<span class="tsd-signature-symbol">: </span><a href="index.html#menuitem" class="tsd-signature-type">MenuItem</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> not<wbr>Exist<wbr>Scene<wbr>Items<span class="tsd-signature-symbol">?: </span><a href="index.html#menuitem" class="tsd-signature-type">MenuItem</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5>order<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>section<wbr>Refs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>modelName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>sectionName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>sub_<wbr>project_<wbr>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>toolbar<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>header<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>enhancer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>left<span class="tsd-signature-symbol">: </span><a href="index.html#entrancetoolbaritem" class="tsd-signature-type">EntranceToolbarItem</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>right<span class="tsd-signature-symbol">: </span><a href="index.html#entrancetoolbaritem" class="tsd-signature-type">EntranceToolbarItem</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter">
										<h5>header<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>enhancer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>left<span class="tsd-signature-symbol">: </span><a href="index.html#entrancetoolbaritem" class="tsd-signature-type">EntranceToolbarItem</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>right<span class="tsd-signature-symbol">: </span><a href="index.html#entrancetoolbaritem" class="tsd-signature-type">EntranceToolbarItem</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></h5>
										<ul class="tsd-parameters">
											<li class="tsd-parameter">
												<h5>enhancer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>left<span class="tsd-signature-symbol">: </span><a href="index.html#entrancetoolbaritem" class="tsd-signature-type">EntranceToolbarItem</a><span class="tsd-signature-symbol">[]</span></h5>
											</li>
											<li class="tsd-parameter">
												<h5>right<span class="tsd-signature-symbol">: </span><a href="index.html#entrancetoolbaritem" class="tsd-signature-type">EntranceToolbarItem</a><span class="tsd-signature-symbol">[]</span></h5>
											</li>
										</ul>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> tooltip<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="entrancetoolbaritem" class="tsd-anchor"></a>
					<h3>Entrance<wbr>Toolbar<wbr>Item</h3>
					<div class="tsd-signature tsd-kind-icon">Entrance<wbr>Toolbar<wbr>Item<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>action<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>badge<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>behavior<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>icon<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>input<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>items<span class="tsd-signature-symbol">: </span><a href="index.html#entrancetoolbaritemsubitem" class="tsd-signature-type">EntranceToolbarItemSubItem</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1812</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>action<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>badge<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>behavior<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>icon<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>input<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>items<span class="tsd-signature-symbol">: </span><a href="index.html#entrancetoolbaritemsubitem" class="tsd-signature-type">EntranceToolbarItemSubItem</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="entrancetoolbaritemsubitem" class="tsd-anchor"></a>
					<h3>Entrance<wbr>Toolbar<wbr>Item<wbr>Sub<wbr>Item</h3>
					<div class="tsd-signature tsd-kind-icon">Entrance<wbr>Toolbar<wbr>Item<wbr>Sub<wbr>Item<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>action<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>action_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>authed<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>back_image<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>behavior<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>canBatch<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>col_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>container<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>css<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>enabled<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>forward<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label_position<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label_width<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>need_footer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>need_title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>on<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>open_in_new_tab<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>prompt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>size_percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>uniplat_version<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>badge<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>behavior<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1783</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>action<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>action_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>authed<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>back_image<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>behavior<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>canBatch<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>col_count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>container<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>css<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>enabled<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>forward<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label_position<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>label_width<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>need_footer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>need_title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>on<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>open_in_new_tab<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>prompt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>size_percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>uniplat_version<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></h5>
								<ul class="tsd-parameters">
									<li class="tsd-parameter">
										<h5>action_<wbr>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>authed<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>back_<wbr>image<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>behavior<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>can<wbr>Batch<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>col_<wbr>count<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>container<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>css<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>enabled<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>forward<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>label_<wbr>position<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>label_<wbr>width<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>need_<wbr>footer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>need_<wbr>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>on<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>open_<wbr>in_<wbr>new_<wbr>tab<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>prompt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>size_<wbr>percent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
									</li>
									<li class="tsd-parameter">
										<h5>uniplat_<wbr>version<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
									</li>
								</ul>
							</li>
							<li class="tsd-parameter">
								<h5>badge<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>behavior<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>label<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="initconfig" class="tsd-anchor"></a>
					<h3>Init<wbr>Config</h3>
					<div class="tsd-signature tsd-kind-icon">Init<wbr>Config<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>canUseSystemWithoutScene<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>needOrg<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>notExistSceneItems<span class="tsd-signature-symbol">: </span><a href="index.html#menuitem" class="tsd-signature-type">MenuItem</a><span class="tsd-signature-symbol">; </span>replaceHeaderTitleWithScene<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>scenes<span class="tsd-signature-symbol">: </span><a href="index.html#sceneconfig" class="tsd-signature-type">SceneConfig</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1755</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> can<wbr>Use<wbr>System<wbr>Without<wbr>Scene<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>为true时可以不选组织和场景进入系统</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>need<wbr>Org<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>not<wbr>Exist<wbr>Scene<wbr>Items<span class="tsd-signature-symbol">: </span><a href="index.html#menuitem" class="tsd-signature-type">MenuItem</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> replace<wbr>Header<wbr>Title<wbr>With<wbr>Scene<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>true 时，header 的title 用场景值替换</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>scenes<span class="tsd-signature-symbol">: </span><a href="index.html#sceneconfig" class="tsd-signature-type">SceneConfig</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="initdata" class="tsd-anchor"></a>
					<h3>Init<wbr>Data</h3>
					<div class="tsd-signature tsd-kind-icon">Init<wbr>Data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>orgId<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>scenes<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">readonly </span><a href="index.html#scene" class="tsd-signature-type">Scene</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>xid<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1773</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> <span class="tsd-flag ts-flagReadonly">Readonly</span> org<wbr>Id<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> <span class="tsd-flag ts-flagReadonly">Readonly</span> scenes<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">readonly </span><a href="index.html#scene" class="tsd-signature-type">Scene</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> <span class="tsd-flag ts-flagReadonly">Readonly</span> xid<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="initialdata" class="tsd-anchor"></a>
					<h3>Initial<wbr>Data</h3>
					<div class="tsd-signature tsd-kind-icon">Initial<wbr>Data<span class="tsd-signature-symbol">:</span> <a href="index.html#baseapplicationconfig" class="tsd-signature-type">BaseApplicationConfig</a><span class="tsd-signature-symbol"> &amp; </span><span class="tsd-signature-symbol">{ </span>rootEntrances<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>rootEntrancesWithTitle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>icon<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>title<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1693</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="menuitem" class="tsd-anchor"></a>
					<h3>Menu<wbr>Item</h3>
					<div class="tsd-signature tsd-kind-icon">Menu<wbr>Item<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>badge<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>container<span class="tsd-signature-symbol">: </span><a href="../enums/intentcontainer.html" class="tsd-signature-type">IntentContainer</a><span class="tsd-signature-symbol">; </span>fullscreen<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>href<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>icon<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>isUnion<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>items<span class="tsd-signature-symbol">: </span><a href="index.html#menuitem" class="tsd-signature-type">MenuItem</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>order<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>title<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>unionItems<span class="tsd-signature-symbol">?: </span><a href="index.html#menuitem" class="tsd-signature-type">MenuItem</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>unionTitle<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1855</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-index-signature">
								<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-type">any</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> badge<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">&gt;</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>container<span class="tsd-signature-symbol">: </span><a href="../enums/intentcontainer.html" class="tsd-signature-type">IntentContainer</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5>fullscreen<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> href<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> icon<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> is<wbr>Union<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>items<span class="tsd-signature-symbol">: </span><a href="index.html#menuitem" class="tsd-signature-type">MenuItem</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>order<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> title<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> union<wbr>Items<span class="tsd-signature-symbol">?: </span><a href="index.html#menuitem" class="tsd-signature-type">MenuItem</a><span class="tsd-signature-symbol">[]</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> union<wbr>Title<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="scene" class="tsd-anchor"></a>
					<h3>Scene</h3>
					<div class="tsd-signature tsd-kind-icon">Scene<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>key<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1778</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>key<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="sceneconfig" class="tsd-anchor"></a>
					<h3>Scene<wbr>Config</h3>
					<div class="tsd-signature tsd-kind-icon">Scene<wbr>Config<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>config<span class="tsd-signature-symbol">: </span><a href="scenetypes.html#sceneconfig" class="tsd-signature-type">SceneConfig</a><span class="tsd-signature-symbol">; </span>defaultValue<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>scene<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1768</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>config<span class="tsd-signature-symbol">: </span><a href="scenetypes.html#sceneconfig" class="tsd-signature-type">SceneConfig</a></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> default<wbr>Value<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>scene<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="authlogin" class="tsd-anchor"></a>
					<h3>auth<wbr>Login</h3>
					<div class="tsd-signature tsd-kind-icon">auth<wbr>Login<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>isSuperUser<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>jwt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>username<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1628</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>is<wbr>Super<wbr>User<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>jwt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>username<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="authloginbind" class="tsd-anchor"></a>
					<h3>auth<wbr>Login<wbr>Bind</h3>
					<div class="tsd-signature tsd-kind-icon">auth<wbr>Login<wbr>Bind<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>isSuperUser<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>jwt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>username<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1639</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>is<wbr>Super<wbr>User<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>jwt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>username<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="authloginbindparams" class="tsd-anchor"></a>
					<h3>auth<wbr>Login<wbr>Bind<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">auth<wbr>Login<wbr>Bind<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>authcode<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>password<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>username<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1633</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>authcode<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>password<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>username<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="bindstatusrequestresult" class="tsd-anchor"></a>
					<h3>bind<wbr>Status<wbr>Request<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">bind<wbr>Status<wbr>Request<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>create_time<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>scene<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>scene_user_id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>user_id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1645</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="connectparams" class="tsd-anchor"></a>
					<h3>connect<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">connect<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>axiosAdapter<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">AxiosAdapter</span><span class="tsd-signature-symbol">; </span>axiosTimeout<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>baseUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>sseUrl<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1733</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> axios<wbr>Adapter<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">AxiosAdapter</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> axios<wbr>Timeout<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span></h5>
								<div class="tsd-comment tsd-typography">
									<div class="lead">
										<p>可配置请求超时时间，默认 60s</p>
									</div>
								</div>
							</li>
							<li class="tsd-parameter">
								<h5>base<wbr>Url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> sse<wbr>Url<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="getuserinfo" class="tsd-anchor"></a>
					<h3>get<wbr>User<wbr>Info</h3>
					<div class="tsd-signature tsd-kind-icon">get<wbr>User<wbr>Info<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>firstName<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>isSuperUser<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>username<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1656</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> first<wbr>Name<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>is<wbr>Super<wbr>User<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>username<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="injectdependencyparams" class="tsd-anchor"></a>
					<h3>inject<wbr>Dependency<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">inject<wbr>Dependency<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>broadcastChannel<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1602</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> broadcast<wbr>Channel<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">unknown</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="loginbypassportrequestresult" class="tsd-anchor"></a>
					<h3>login<wbr>ByPassport<wbr>Request<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">login<wbr>ByPassport<wbr>Request<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>firstName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>isSuperUser<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>jwt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>username<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1611</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>first<wbr>Name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>is<wbr>Super<wbr>User<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>jwt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>username<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="loginbytokenparams" class="tsd-anchor"></a>
					<h3>login<wbr>ByToken<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">login<wbr>ByToken<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>isSuperUser<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>token<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>username<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1749</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> is<wbr>Super<wbr>User<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>token<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> username<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="loginparams" class="tsd-anchor"></a>
					<h3>login<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">login<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>codeToVerify<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>password<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>rootEntrance<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>username<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1743</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>code<wbr>ToVerify<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>password<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>root<wbr>Entrance<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>username<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="loginrequestresult" class="tsd-anchor"></a>
					<h3>login<wbr>Request<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">login<wbr>Request<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>isSuperUser<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>jwt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>passwordChanged<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1606</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>is<wbr>Super<wbr>User<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>jwt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>password<wbr>Changed<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="refreshtokenrequestresult" class="tsd-anchor"></a>
					<h3>refresh<wbr>Token<wbr>Request<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">refresh<wbr>Token<wbr>Request<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>jwt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1653</li>
						</ul>
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>jwt<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-type-alias tsd-parent-kind-namespace">
					<a name="reloadapiresult" class="tsd-anchor"></a>
					<h3>reload<wbr>Api<wbr>Result</h3>
					<div class="tsd-signature tsd-kind-icon">reload<wbr>Api<wbr>Result<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">void</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/def/index.ts:1663</li>
						</ul>
					</aside>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
					<li class="current tsd-kind-namespace">
						<a href="index.html">Index</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#appadminconfig" class="tsd-kind-icon">App<wbr>Admin<wbr>Config</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#appinstanceconfig" class="tsd-kind-icon">App<wbr>Instance<wbr>Config</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#applicationconfig" class="tsd-kind-icon">Application<wbr>Config</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#baseapplicationconfig" class="tsd-kind-icon">Base<wbr>Application<wbr>Config</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#bindpassportparams" class="tsd-kind-icon">Bind<wbr>Passport<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#bindpassportrequestresult" class="tsd-kind-icon">Bind<wbr>Passport<wbr>Request<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#entrance" class="tsd-kind-icon">Entrance</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#entrancetoolbaritem" class="tsd-kind-icon">Entrance<wbr>Toolbar<wbr>Item</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#entrancetoolbaritemsubitem" class="tsd-kind-icon">Entrance<wbr>Toolbar<wbr>Item<wbr>Sub<wbr>Item</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#initconfig" class="tsd-kind-icon">Init<wbr>Config</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#initdata" class="tsd-kind-icon">Init<wbr>Data</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#initialdata" class="tsd-kind-icon">Initial<wbr>Data</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#menuitem" class="tsd-kind-icon">Menu<wbr>Item</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#scene" class="tsd-kind-icon">Scene</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#sceneconfig" class="tsd-kind-icon">Scene<wbr>Config</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#authlogin" class="tsd-kind-icon">auth<wbr>Login</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#authloginbind" class="tsd-kind-icon">auth<wbr>Login<wbr>Bind</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#authloginbindparams" class="tsd-kind-icon">auth<wbr>Login<wbr>Bind<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#bindstatusrequestresult" class="tsd-kind-icon">bind<wbr>Status<wbr>Request<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#connectparams" class="tsd-kind-icon">connect<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#getuserinfo" class="tsd-kind-icon">get<wbr>User<wbr>Info</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#injectdependencyparams" class="tsd-kind-icon">inject<wbr>Dependency<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#loginbypassportrequestresult" class="tsd-kind-icon">login<wbr>ByPassport<wbr>Request<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#loginbytokenparams" class="tsd-kind-icon">login<wbr>ByToken<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#loginparams" class="tsd-kind-icon">login<wbr>Params</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#loginrequestresult" class="tsd-kind-icon">login<wbr>Request<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#refreshtokenrequestresult" class="tsd-kind-icon">refresh<wbr>Token<wbr>Request<wbr>Result</a>
					</li>
					<li class=" tsd-kind-type-alias tsd-parent-kind-namespace">
						<a href="index.html#reloadapiresult" class="tsd-kind-icon">reload<wbr>Api<wbr>Result</a>
					</li>
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>