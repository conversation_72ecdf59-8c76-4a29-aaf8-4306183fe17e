import axios from "axios"
import type { EventSourcePolyfill } from "event-source-polyfill"

import type * as dto from "../../def/index"
import { channel } from "../broadcast-channel"
import { events } from "../events"
import { global } from "../global"
import { TimersManager } from "../timers-manager"

import { createConnection } from "./api"

type BroadcastLister = (e: dto.SSE.msg) => void

const online = "online"
const offline = "offline"
const ONLINE = true
const OFFLINE = false
const createBroadCastChannel = () => {
    const broadcastChannel = channel.getClass()
    if (broadcastChannel == null) {
        return {
            postMessage: () => null,
            onmessage: () => null,
        }
    }
    return new broadcastChannel("SSE.MESSAGE_DATA_SDK")
}

class Broadcast {
    private publisher = createBroadCastChannel()
    private listener?: BroadcastLister
    public postMessage(msg: dto.SSE.msg) {
        this.publisher.postMessage(msg)
        this.listener && this.listener(msg)
    }
    public addListener(listener: BroadcastLister) {
        this.listener = listener
        this.publisher.onmessage = listener
    }
}

function tryCatchWrapper<T extends (...args: unknown[]) => unknown>(
    callback: T
) {
    return (e: Parameters<T>[0]) => {
        try {
            callback(e)
        } catch (error) {
            console.error(error)
        }
    }
}

const reconnectTimers = TimersManager("ReconnectTimers")
export class Sse {
    private tokenInvalid = false
    private eventSource?: EventSourcePolyfill
    private transportMessageListeners: dto.SSE.transportMessageListener[] = []
    private bigActionMessageListeners: dto.SSE.bigActionUpdateListener[] = []
    private notifyMessageListener: dto.SSE.notifyListener[] = []
    private menuDataChangedMessageListener: dto.SSE.MenuDataChangedListener[] =
        []
    private connectivityObserver?: dto.SSE.ConnectivityObserver
    public connectivity = false

    private broadcast?: Broadcast

    private usedUid = ""
    private usedSid = ""
    private lastCheck = 0

    private randomId() {
        return String(Math.floor(Math.random() * 65535 * 65535))
    }

    private broadcastConnectivity(status: boolean) {
        if (this.broadcast == null) {
            throw new Error("请先初始化sse")
        }
        this.broadcast.postMessage({
            type: status ? online : offline,
            createByMyself: false, //占位：只是为了类型不报错
            self: false,
            timestamp: 7, //占位：只是为了类型不报错
            dataUpdates: [], //占位：只是为了类型不报错
        })
    }

    public getUUID() {
        if (this.usedUid) {
            return this.usedUid
        }
        return (this.usedUid = this.randomId())
    }

    public getSessionId() {
        if (this.usedSid) {
            return this.usedSid
        }
        return (this.usedSid = this.randomId())
    }

    private checkConnectivity() {
        const ts = this.lastCheck
        const now = new Date().getTime()
        if (now - ts > 10000) {
            this.initEventSource()
        } else {
            reconnectTimers.push(
                setTimeout(() => this.checkConnectivity(), 10e3)
            )
        }
    }

    public initEventSource(open = true) {
        if (this.tokenInvalid) return
        this.initBroadcastChannel()
        const jwtTOKEN = global.getCurrentToken()
        if (jwtTOKEN == null) {
            console.error("还没登录，不能初始化sse")
            return
        }
        if (!open) {
            return this.broadcastConnectivity(ONLINE)
        }
        const sseInstance = (this.eventSource = createConnection({
            sseUuid: this.getUUID(),
            sessionId: this.getSessionId(),
            jwtTOKEN,
        }))

        this.eventSource.addEventListener("error", () => {
            console.error("sse 连接失败", sseInstance)
            this.lastCheck = new Date().valueOf()
            this.broadcastConnectivity(OFFLINE)
            this.usedSid = ""
            this.usedUid = ""
            sseInstance.close()
            reconnectTimers.push(
                setTimeout(() => this.checkConnectivity(), 10000)
            )
        })

        this.eventSource.addEventListener("open", (e) => {
            this.lastCheck = new Date().valueOf()
            this.broadcastConnectivity(ONLINE)
            console.log("sse 连接成功", e)
        })
        this.eventSource.addEventListener("message", (e: { data: string }) => {
            this.lastCheck = new Date().valueOf()
            const msgdata = JSON.parse(e.data)
            if (msgdata.type === "init") return
            if (msgdata.type === "auth_failed") {
                // token过期，不再重连
                this.broadcastConnectivity(OFFLINE)
                events.callTokenExpiring(msgdata.type)
                this.tokenInvalid = true
                sseInstance.close()
                return
            }
            if (msgdata.type === "heartBeat") {
                this.broadcastConnectivity(ONLINE)
                console.log("heart beat")
                return
            }
            console.log("sse 原始消息:", JSON.parse(e.data))
            msgdata.createByMyself = !!msgdata.createByMyself
            msgdata.dataUpdates?.forEach(
                (k: { selectedList: string | string[] }) => {
                    if (typeof k.selectedList === "string") {
                        k.selectedList = k?.selectedList?.split(",") ?? []
                    }
                }
            )
            if (this.broadcast != null) {
                this.broadcast.postMessage(msgdata)
            } else {
                console.error("broadcast未初始化")
            }
        })
    }

    private initBroadcastChannel() {
        this.broadcast = new Broadcast()
        this.broadcast.addListener((event) => {
            !event.type && (event.type = "transportMessage")
            event.createByMyself = event.self
            if (event.type === "transportMessage") {
                console.log("sse transportMessage", event)
                this.transportMessageListeners.forEach((cb) => cb(event))
            } else if (event.type === "bigAction") {
                console.log("sse bigActionMessage", event)
                this.bigActionMessageListeners.forEach((cb) => cb(event))
            } else if ([online, offline].includes(event.type)) {
                const isOnline = online === event.type
                this.connectivity = isOnline
                this.connectivityObserver && this.connectivityObserver(isOnline)
            } else if (event.type === "menuData") {
                console.log("sse menu data event", event)
                this.menuDataChangedMessageListener.forEach((cb) =>
                    cb(event.value as Record<string, number>)
                )
            } else {
                console.log("sse notify message", event)
                this.notifyMessageListener.forEach((cb) => cb(event))
            }
        })
    }

    public async close() {
        this.eventSource?.close()
        this.transportMessageListeners = []
        this.notifyMessageListener = []
        this.menuDataChangedMessageListener = []
        this.eventSource = undefined
    }

    public addBigActionMessageListener(
        callback: dto.SSE.bigActionUpdateListener
    ): dto.SSE.removeSSEListener {
        const warpedCallback = tryCatchWrapper(callback)
        this.bigActionMessageListeners.push(warpedCallback)
        const removeEventListener = () => {
            this.bigActionMessageListeners =
                this.bigActionMessageListeners.filter(
                    (cb) => cb !== warpedCallback
                )
        }
        return removeEventListener
    }

    public addTransportMessageListener(
        callback: dto.SSE.transportMessageListener
    ): dto.SSE.removeSSEListener {
        const warpedCallback = tryCatchWrapper(callback)
        this.transportMessageListeners.push(warpedCallback)
        const removeEventListener = () => {
            this.transportMessageListeners =
                this.transportMessageListeners.filter(
                    (cb) => cb !== warpedCallback
                )
        }
        return removeEventListener
    }

    public addSseNotifyMessageListener(
        callback: dto.SSE.notifyListener
    ): dto.SSE.removeSSEListener {
        const warpedCallback = tryCatchWrapper(callback)
        this.notifyMessageListener.push(warpedCallback)
        const removeEventListener = () => {
            this.notifyMessageListener = this.notifyMessageListener.filter(
                (cb) => cb !== warpedCallback
            )
        }
        return removeEventListener
    }

    public addSseMenuDataChangedMessageListener(
        callback: dto.SSE.MenuDataChangedListener
    ): dto.SSE.removeSSEListener {
        const warpedCallback = tryCatchWrapper(callback)
        this.menuDataChangedMessageListener.push(warpedCallback)
        const removeEventListener = () => {
            this.menuDataChangedMessageListener =
                this.menuDataChangedMessageListener.filter(
                    (cb) => cb !== warpedCallback
                )
        }
        return removeEventListener
    }

    public addConnectivityObserver(callback: dto.SSE.ConnectivityObserver) {
        this.connectivityObserver = callback
        return () => (this.connectivityObserver = undefined)
    }

    public registerModels<T>(models: string[]) {
        const set = new Set(models)
        const items = []
        set.forEach((v) => items.push(v))
        return axios.get<unknown, T>(
            `${
                global.SSEURL
            }general/sse/token/${global.getCurrentToken()}/uuid/${this.getUUID()}/subscribe/${items.join(
                ","
            )}`
        )
    }

    public registerMenuOnBadgeChanged<T>(
        models: string[],
        project: "entrance" | "communication" | "todo"
    ) {
        const set = new Set(models)
        const items = []
        set.forEach((v) => items.push(v))
        const tail = items.length ? `?dataNames=${items.join(",")}` : ""
        return axios.get<unknown, T>(
            `${
                global.SSEURL
            }general/sse/token/${global.getCurrentToken()}/uuid/${this.getUUID()}/subscribeMenuData/${project}${tail}`
        )
    }
}

export default new Sse()
