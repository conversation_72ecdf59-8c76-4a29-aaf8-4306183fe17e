<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>PivotTable | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="pivottable.html">PivotTable</a>
				</li>
			</ul>
			<h1>Class PivotTable</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">PivotTable</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="pivottable.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section tsd-is-private tsd-is-private-protected">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="pivottable.html#api" class="tsd-kind-icon">api</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="pivottable.html#columns" class="tsd-kind-icon">columns</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="pivottable.html#getdetailparams" class="tsd-kind-icon">get<wbr>Detail<wbr>Params</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="pivottable.html#groupfields" class="tsd-kind-icon">group<wbr>Fields</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="pivottable.html#keyvaluefilters" class="tsd-kind-icon">key<wbr>Valuefilters</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="pivottable.html#meta" class="tsd-kind-icon">meta</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="pivottable.html#notqueried" class="tsd-kind-icon">not<wbr>Queried</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="pivottable.html#prefilters" class="tsd-kind-icon">prefilters</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="pivottable.html#refreshdata" class="tsd-kind-icon">refresh<wbr>Data</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="pivottable.html#refreshparams" class="tsd-kind-icon">refresh<wbr>Params</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="pivottable.html#rows" class="tsd-kind-icon">rows</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="pivottable.html#summaryfields" class="tsd-kind-icon">summary<wbr>Fields</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#addcolumns" class="tsd-kind-icon">add<wbr>Columns</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#addfilter" class="tsd-kind-icon">add<wbr>Filter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#addgroupfields" class="tsd-kind-icon">add<wbr>Group<wbr>Fields</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#addprefilter" class="tsd-kind-icon">add<wbr>Prefilter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#addrawprefilters" class="tsd-kind-icon">add<wbr>Raw<wbr>Prefilters</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#addrows" class="tsd-kind-icon">add<wbr>Rows</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#addsummaryfields" class="tsd-kind-icon">add<wbr>Summary<wbr>Fields</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#clearcolumns" class="tsd-kind-icon">clear<wbr>Columns</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#clearfilter" class="tsd-kind-icon">clear<wbr>Filter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#cleargroupfields" class="tsd-kind-icon">clear<wbr>Group<wbr>Fields</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#clearprefilter" class="tsd-kind-icon">clear<wbr>Prefilter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#clearrows" class="tsd-kind-icon">clear<wbr>Rows</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#clearsummaryfields" class="tsd-kind-icon">clear<wbr>Summary<wbr>Fields</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#exportdetailasexcel" class="tsd-kind-icon">export<wbr>Detail<wbr>AsExcel</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#exporttoexcel" class="tsd-kind-icon">export<wbr>ToExcel</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#getdetail" class="tsd-kind-icon">get<wbr>Detail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#gettabledata" class="tsd-kind-icon">get<wbr>Table<wbr>Data</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><a href="pivottable.html#handlefilters" class="tsd-kind-icon">handle<wbr>Filters</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#query" class="tsd-kind-icon">query</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="pivottable.html#queryforyou" class="tsd-kind-icon">query<wbr>For<wbr>You</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="pivottable.html#refresh" class="tsd-kind-icon">refresh</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new <wbr>Pivot<wbr>Table<span class="tsd-signature-symbol">(</span>model_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, pivottableName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="pivottable.html" class="tsd-signature-type">PivotTable</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:23</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>model_name: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>pivottableName: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="pivottable.html" class="tsd-signature-type">PivotTable</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="api" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> api</h3>
					<div class="tsd-signature tsd-kind-icon">api<span class="tsd-signature-symbol">:</span> <a href="pivottableapi.html" class="tsd-signature-type">PivotTableApi</a></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/pivot-table.ts:10</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="columns" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> columns</h3>
					<div class="tsd-signature tsd-kind-icon">columns<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/pivot-table.ts:18</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="getdetailparams" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> get<wbr>Detail<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">get<wbr>Detail<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.PivotTableTypes.getDetailAPIParams</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/pivot-table.ts:14</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="groupfields" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> group<wbr>Fields</h3>
					<div class="tsd-signature tsd-kind-icon">group<wbr>Fields<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/pivot-table.ts:19</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="keyvaluefilters" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> key<wbr>Valuefilters</h3>
					<div class="tsd-signature tsd-kind-icon">key<wbr>Valuefilters<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.ListTypes.KeyValueFilters</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/pivot-table.ts:16</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="meta" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> meta</h3>
					<div class="tsd-signature tsd-kind-icon">meta<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.PivotTableTypes.queryResult</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/pivot-table.ts:22</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="notqueried" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> not<wbr>Queried</h3>
					<div class="tsd-signature tsd-kind-icon">not<wbr>Queried<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/pivot-table.ts:12</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="prefilters" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> prefilters</h3>
					<div class="tsd-signature tsd-kind-icon">prefilters<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/pivot-table.ts:15</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="refreshdata" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> refresh<wbr>Data</h3>
					<div class="tsd-signature tsd-kind-icon">refresh<wbr>Data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.PivotTableTypes.refreshResult</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/pivot-table.ts:23</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="refreshparams" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> refresh<wbr>Params</h3>
					<div class="tsd-signature tsd-kind-icon">refresh<wbr>Params<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">dto.PivotTableTypes.refreshAPIParams</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/pivot-table.ts:13</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="rows" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> rows</h3>
					<div class="tsd-signature tsd-kind-icon">rows<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/pivot-table.ts:17</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="summaryfields" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> summary<wbr>Fields</h3>
					<div class="tsd-signature tsd-kind-icon">summary<wbr>Fields<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/pivot-table/pivot-table.ts:20</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addcolumns" class="tsd-anchor"></a>
					<h3>add<wbr>Columns</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Columns<span class="tsd-signature-symbol">(</span>rows<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:52</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>rows: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addfilter" class="tsd-anchor"></a>
					<h3>add<wbr>Filter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Filter<span class="tsd-signature-symbol">(</span>filter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.KeyValueFilters</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:69</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>filter: <span class="tsd-signature-type">dto.ListTypes.KeyValueFilters</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addgroupfields" class="tsd-anchor"></a>
					<h3>add<wbr>Group<wbr>Fields</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Group<wbr>Fields<span class="tsd-signature-symbol">(</span>data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:28</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>data: <span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addprefilter" class="tsd-anchor"></a>
					<h3>add<wbr>Prefilter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Prefilter<span class="tsd-signature-symbol">(</span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.PrefiltersObject</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:87</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>prefilters: <span class="tsd-signature-type">dto.ListTypes.PrefiltersObject</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addrawprefilters" class="tsd-anchor"></a>
					<h3>add<wbr>Raw<wbr>Prefilters</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Raw<wbr>Prefilters<span class="tsd-signature-symbol">(</span>prefilters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.prefilters</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:64</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>prefilters: <span class="tsd-signature-type">dto.prefilters</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addrows" class="tsd-anchor"></a>
					<h3>add<wbr>Rows</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Rows<span class="tsd-signature-symbol">(</span>rows<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:48</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>rows: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addsummaryfields" class="tsd-anchor"></a>
					<h3>add<wbr>Summary<wbr>Fields</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Summary<wbr>Fields<span class="tsd-signature-symbol">(</span>data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:33</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>data: <span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearcolumns" class="tsd-anchor"></a>
					<h3>clear<wbr>Columns</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Columns<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:60</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearfilter" class="tsd-anchor"></a>
					<h3>clear<wbr>Filter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Filter<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:82</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="cleargroupfields" class="tsd-anchor"></a>
					<h3>clear<wbr>Group<wbr>Fields</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Group<wbr>Fields<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:38</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearprefilter" class="tsd-anchor"></a>
					<h3>clear<wbr>Prefilter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Prefilter<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:95</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearrows" class="tsd-anchor"></a>
					<h3>clear<wbr>Rows</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Rows<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:56</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="clearsummaryfields" class="tsd-anchor"></a>
					<h3>clear<wbr>Summary<wbr>Fields</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">clear<wbr>Summary<wbr>Fields<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">this</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:43</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">this</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="exportdetailasexcel" class="tsd-anchor"></a>
					<h3>export<wbr>Detail<wbr>AsExcel</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">export<wbr>Detail<wbr>AsExcel<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:148</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="exporttoexcel" class="tsd-anchor"></a>
					<h3>export<wbr>ToExcel</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">export<wbr>ToExcel<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:134</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getdetail" class="tsd-anchor"></a>
					<h3>get<wbr>Detail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Detail<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.PivotTableTypes.getDetaiParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/pivottabletypes.html#getdetailrequestresult" class="tsd-signature-type">getDetailRequestResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:155</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.PivotTableTypes.getDetaiParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/pivottabletypes.html#getdetailrequestresult" class="tsd-signature-type">getDetailRequestResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="gettabledata" class="tsd-anchor"></a>
					<h3>get<wbr>Table<wbr>Data</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Table<wbr>Data<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>tableData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tableHeaders<span class="tsd-signature-symbol">: </span><a href="../globals.html#headeritem" class="tsd-signature-type">HeaderItem</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:170</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{ </span>tableData<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tableHeaders<span class="tsd-signature-symbol">: </span><a href="../globals.html#headeritem" class="tsd-signature-type">HeaderItem</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></h4>
							<ul class="tsd-parameters">
								<li class="tsd-parameter">
									<h5>table<wbr>Data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
								<li class="tsd-parameter">
									<h5>table<wbr>Headers<span class="tsd-signature-symbol">: </span><a href="../globals.html#headeritem" class="tsd-signature-type">HeaderItem</a><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-protected">
					<a name="handlefilters" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagProtected">Protected</span> handle<wbr>Filters</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-protected">
						<li class="tsd-signature tsd-kind-icon">handle<wbr>Filters<span class="tsd-signature-symbol">(</span>keyValueFilter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.ListTypes.KeyValueFilters</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../globals.html#filter" class="tsd-signature-type">filter</a><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:197</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>keyValueFilter: <span class="tsd-signature-type">dto.ListTypes.KeyValueFilters</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="../globals.html#filter" class="tsd-signature-type">filter</a><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="query" class="tsd-anchor"></a>
					<h3>query</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">query<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/swaggertypes.html#queryresult" class="tsd-signature-type">queryResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:106</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/swaggertypes.html#queryresult" class="tsd-signature-type">queryResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="queryforyou" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> query<wbr>For<wbr>You</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">query<wbr>For<wbr>You<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:100</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="refresh" class="tsd-anchor"></a>
					<h3>refresh</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">refresh<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">dto.PivotTableTypes.refreshParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/pivottabletypes.html#refreshresult" class="tsd-signature-type">refreshResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/pivot-table/pivot-table.ts:113</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>params: <span class="tsd-signature-type">dto.PivotTableTypes.refreshParams</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../modules/pivottabletypes.html#refreshresult" class="tsd-signature-type">refreshResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="pivottable.html" class="tsd-kind-icon">Pivot<wbr>Table</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class">
								<a href="pivottable.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="pivottable.html#api" class="tsd-kind-icon">api</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="pivottable.html#columns" class="tsd-kind-icon">columns</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="pivottable.html#getdetailparams" class="tsd-kind-icon">get<wbr>Detail<wbr>Params</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="pivottable.html#groupfields" class="tsd-kind-icon">group<wbr>Fields</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="pivottable.html#keyvaluefilters" class="tsd-kind-icon">key<wbr>Valuefilters</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="pivottable.html#meta" class="tsd-kind-icon">meta</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="pivottable.html#notqueried" class="tsd-kind-icon">not<wbr>Queried</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="pivottable.html#prefilters" class="tsd-kind-icon">prefilters</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="pivottable.html#refreshdata" class="tsd-kind-icon">refresh<wbr>Data</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="pivottable.html#refreshparams" class="tsd-kind-icon">refresh<wbr>Params</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="pivottable.html#rows" class="tsd-kind-icon">rows</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="pivottable.html#summaryfields" class="tsd-kind-icon">summary<wbr>Fields</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#addcolumns" class="tsd-kind-icon">add<wbr>Columns</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#addfilter" class="tsd-kind-icon">add<wbr>Filter</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#addgroupfields" class="tsd-kind-icon">add<wbr>Group<wbr>Fields</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#addprefilter" class="tsd-kind-icon">add<wbr>Prefilter</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#addrawprefilters" class="tsd-kind-icon">add<wbr>Raw<wbr>Prefilters</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#addrows" class="tsd-kind-icon">add<wbr>Rows</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#addsummaryfields" class="tsd-kind-icon">add<wbr>Summary<wbr>Fields</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#clearcolumns" class="tsd-kind-icon">clear<wbr>Columns</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#clearfilter" class="tsd-kind-icon">clear<wbr>Filter</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#cleargroupfields" class="tsd-kind-icon">clear<wbr>Group<wbr>Fields</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#clearprefilter" class="tsd-kind-icon">clear<wbr>Prefilter</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#clearrows" class="tsd-kind-icon">clear<wbr>Rows</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#clearsummaryfields" class="tsd-kind-icon">clear<wbr>Summary<wbr>Fields</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#exportdetailasexcel" class="tsd-kind-icon">export<wbr>Detail<wbr>AsExcel</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#exporttoexcel" class="tsd-kind-icon">export<wbr>ToExcel</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#getdetail" class="tsd-kind-icon">get<wbr>Detail</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#gettabledata" class="tsd-kind-icon">get<wbr>Table<wbr>Data</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-protected">
								<a href="pivottable.html#handlefilters" class="tsd-kind-icon">handle<wbr>Filters</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#query" class="tsd-kind-icon">query</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="pivottable.html#queryforyou" class="tsd-kind-icon">query<wbr>For<wbr>You</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="pivottable.html#refresh" class="tsd-kind-icon">refresh</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>