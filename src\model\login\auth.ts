import { global } from "../../core/global"
import type * as dto from "../../def/index"

import * as api from "./api"
export class AuthLogin {
    constructor(private done: () => void, private failed: () => void) {}

    public async login(authcode: string) {
        try {
            const data = await api.authLogin(authcode)
            global.username = data.username
            global.isSuperAdmin = data.isSuperUser
            global.jwtToken = data.jwt
            this.done()
            return data
        } catch (error) {
            this.failed()
            const unbind = "账号未绑定"
            if (error.toString() === unbind) {
                return unbind
            }
            throw error
        }
    }

    public async binding(params: dto.Index.authLoginBindParams) {
        const data = await api.authLoginBind(params)
        global.jwtToken = data.jwt
        global.username = data.username
        global.isSuperAdmin = data.isSuperUser
        this.done()
        return data
    }
}
