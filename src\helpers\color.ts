export function color2Rgb(sColor: string) {
    const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
    sColor = sColor.toLowerCase()
    if (sColor && reg.test(sColor)) {
        if (sColor.length === 4) {
            let sColorNew = "#"
            for (let i = 1; i < 4; i += 1) {
                sColorNew += sColor
                    .slice(i, i + 1)
                    .concat(sColor.slice(i, i + 1))
            }
            sColor = sColorNew
        }
        const sColorChange = []
        for (let i = 1; i < 7; i += 2) {
            sColorChange.push(parseInt("0x" + sColor.slice(i, i + 2)))
        }
        return {
            r: sColorChange[0] as number,
            g: sColorChange[1] as number,
            b: sColorChange[2] as number,
        }
    }
    return { r: 0, g: 0, b: 0 }
}

export function colorToRgb(sColor: string, a = 1) {
    const v = color2Rgb(sColor)
    return `rgba(${v.r}, ${v.g}, ${v.b}, ${a})`
}

export function colorToHex(rgb: string) {
    const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
    if (/^(rgb|RGB)/.test(rgb)) {
        const aColor = rgb.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",")
        let strHex = "#"
        for (let i = 0; i < aColor.length; i++) {
            let hex: number | string = +Number(aColor[i]).toString(16)
            hex = hex < 10 ? 0 + "" + hex : hex
            if (hex === "0") {
                hex += hex
            }
            strHex += hex
        }
        if (strHex.length !== 7) {
            strHex = rgb
        }

        return strHex
    }
    if (reg.test(rgb)) {
        const aNum = rgb.replace(/#/, "").split("")
        if (aNum.length === 6) {
            return rgb
        } else if (aNum.length === 3) {
            let numHex = "#"
            for (let i = 0; i < aNum.length; i += 1) {
                numHex += aNum[i] + aNum[i]
            }
            return numHex
        }
    }
    return rgb
}
