export class ImageCompresser {
    public static dataURL2File(data: string, name: string) {
        const arr = data.split(",")
        if (arr) {
            if (arr[0]) {
                const match = arr[0].match(/:(.*?);/)
                if (match) {
                    const mime = match[1]
                    const bstr = atob(arr[1])
                    let n = bstr.length
                    const u8arr = new Uint8Array(n)
                    while (n--) {
                        u8arr[n] = bstr.charCodeAt(n)
                    }
                    return new File([u8arr], name, { type: mime })
                }
            }
        }
        return null
    }

    public static readFile2Image(
        file: File,
        maxWidth: number,
        maxHeight: number
    ): Promise<File | null> {
        return new Promise<File>((resolve, reject) => {
            const img = new Image()
            const reader = new FileReader()
            reader.onload = (e) => {
                if (e && e.target && e.target.result) {
                    img.src = e.target.result as string
                } else {
                    reject(new Error("file load failed"))
                }
            }
            reader.readAsDataURL(file)
            img.onload = () => {
                const originWidth = img.naturalWidth
                const originHeight = img.naturalHeight
                if (originWidth > maxWidth || originHeight > maxHeight) {
                    ImageCompresser.compressImg(img, maxWidth, maxHeight)
                        .then((b) => {
                            if (b) {
                                const newFile = new File([b], file.name)
                                // 如果压缩完还不如原始图片size小，直接返回原始图片
                                resolve(
                                    newFile.size <= file.size ? newFile : file
                                )
                            } else {
                                reject(new Error("file load failed"))
                            }
                        })
                        .catch(reject)
                } else {
                    resolve(file)
                }
            }
        })
    }

    /**
     * 压缩图片
     * @param img 被压缩的img对象
     * @param type 压缩后转换的文件类型
     * @param mx 触发压缩的图片最大宽度限制
     * @param mh 触发压缩的图片最大高度限制
     */
    public static compressImg(
        img: HTMLImageElement,
        mx: number,
        mh: number,
        type?: string
    ): Promise<Blob | null> {
        return new Promise((resolve, reject) => {
            const canvas = document.createElement("canvas")
            const context = canvas.getContext("2d")
            if (!context) {
                reject(new Error(" compress image failed"))
                return
            }
            const { width: originWidth, height: originHeight } = img // 最大尺寸限制
            const maxWidth = mx
            const maxHeight = mh // 目标尺寸
            let targetWidth = originWidth
            let targetHeight = originHeight
            if (originWidth > maxWidth || originHeight > maxHeight) {
                if (originWidth / originHeight > 1) {
                    targetWidth = maxWidth
                    targetHeight = Math.round(
                        maxWidth * (originHeight / originWidth)
                    )
                } else {
                    targetHeight = maxHeight
                    targetWidth = Math.round(
                        maxHeight * (originWidth / originHeight)
                    )
                }
            }
            canvas.width = targetWidth
            canvas.height = targetHeight
            context.clearRect(0, 0, targetWidth, targetHeight)
            context.drawImage(img, 0, 0, targetWidth, targetHeight)
            canvas.toBlob((blob) => {
                resolve(blob)
                canvas.remove()
            }, type || "image/png")
        })
    }
}
