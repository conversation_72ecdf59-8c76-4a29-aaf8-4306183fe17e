import axios from "../../core/axios"
import { ListSection as ListSectionTypes } from "../../def"


export class ListSectionBuilder {
    constructor(private model: string, private name: string) {}

    /**
     * 获取section详情
     * @param query 这里的query，完全与 list 查询 meta 时一致
     */
    public meta(query: unknown): Promise<ListSectionTypes.Meta> {
        return axios.post<ListSectionTypes.Meta>(
            `general/model/${this.model}/section_of_list2/${this.name}/meta`,
            query
        )
    }
}
