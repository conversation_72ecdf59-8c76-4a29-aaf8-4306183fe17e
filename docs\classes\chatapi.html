<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>ChatApi | uniplat-sdk</title>
	<meta name="description" content="Documentation for uniplat-sdk">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">uniplat-sdk</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<ul class="tsd-breadcrumb">
				<li>
					<a href="../globals.html">Globals</a>
				</li>
				<li>
					<a href="chatapi.html">ChatApi</a>
				</li>
			</ul>
			<h1>Class ChatApi</h1>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">ChatApi</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="chatapi.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section tsd-is-private tsd-is-private-protected">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="chatapi.html#id" class="tsd-kind-icon">id</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="chatapi.html#model_name" class="tsd-kind-icon">model_<wbr>name</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="chatapi.html#orgid" class="tsd-kind-icon">orgID</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="chatapi.html#addcs" class="tsd-kind-icon">add<wbr>Cs</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="chatapi.html#addmember" class="tsd-kind-icon">add<wbr>Member</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="chatapi.html#buildheader" class="tsd-kind-icon">build<wbr>Header</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="chatapi.html#createchat" class="tsd-kind-icon">create<wbr>Chat</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="chatapi.html#csexitchat" class="tsd-kind-icon">cs<wbr>Exit<wbr>Chat</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="chatapi.html#finishchat" class="tsd-kind-icon">finish<wbr>Chat</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="chatapi.html#removecs" class="tsd-kind-icon">remove<wbr>Cs</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="chatapi.html#removemember" class="tsd-kind-icon">remove<wbr>Member</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="chatapi.html#sendmsg" class="tsd-kind-icon">send<wbr>Msg</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="chatapi.html#startchat" class="tsd-kind-icon">start<wbr>Chat</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter"><a href="chatapi.html#userexitchat" class="tsd-kind-icon">user<wbr>Exit<wbr>Chat</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new <wbr>Chat<wbr>Api<span class="tsd-signature-symbol">(</span>model_name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span>, orgID<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="chatapi.html" class="tsd-signature-type">ChatApi</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/chat/api.ts:4</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>model_name: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>id: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>orgID: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="chatapi.html" class="tsd-signature-type">ChatApi</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="id" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> id</h3>
					<div class="tsd-signature tsd-kind-icon">id<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/chat/api.ts:7</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="model_name" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> model_<wbr>name</h3>
					<div class="tsd-signature tsd-kind-icon">model_<wbr>name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/chat/api.ts:6</li>
						</ul>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private">
					<a name="orgid" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> orgID</h3>
					<div class="tsd-signature tsd-kind-icon">orgID<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<ul>
							<li>Defined in src/model/chat/api.ts:8</li>
						</ul>
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="addcs" class="tsd-anchor"></a>
					<h3>add<wbr>Cs</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Cs&lt;T&gt;<span class="tsd-signature-symbol">(</span>uids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/chat/api.ts:63</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>uids: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="addmember" class="tsd-anchor"></a>
					<h3>add<wbr>Member</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Member&lt;T&gt;<span class="tsd-signature-symbol">(</span>uids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span>, msgId<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/chat/api.ts:25</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>uids: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagDefault value">Default value</span> msgId: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = 0</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private">
					<a name="buildheader" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagPrivate">Private</span> build<wbr>Header</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private">
						<li class="tsd-signature tsd-kind-icon">build<wbr>Header<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>headers<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">object</span><span class="tsd-signature-symbol"> }</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/chat/api.ts:11</li>
								</ul>
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{ </span>headers<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">object</span><span class="tsd-signature-symbol"> }</span></h4>
							<ul class="tsd-parameters">
								<li class="tsd-parameter">
									<h5>headers<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">object</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter">
											<h5>Current<wbr>Org<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
										</li>
									</ul>
								</li>
							</ul>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="createchat" class="tsd-anchor"></a>
					<h3>create<wbr>Chat</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">create<wbr>Chat<span class="tsd-signature-symbol">(</span>autoJoin<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span>, title<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#createchatresult" class="tsd-signature-type">CreateChatResult</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/chat/api.ts:15</li>
								</ul>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> autoJoin: <span class="tsd-signature-type">boolean</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> title: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../globals.html#createchatresult" class="tsd-signature-type">CreateChatResult</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="csexitchat" class="tsd-anchor"></a>
					<h3>cs<wbr>Exit<wbr>Chat</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">cs<wbr>Exit<wbr>Chat&lt;T&gt;<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/chat/api.ts:85</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="finishchat" class="tsd-anchor"></a>
					<h3>finish<wbr>Chat</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">finish<wbr>Chat&lt;T&gt;<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/chat/api.ts:55</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="removecs" class="tsd-anchor"></a>
					<h3>remove<wbr>Cs</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">remove<wbr>Cs&lt;T&gt;<span class="tsd-signature-symbol">(</span>uids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/chat/api.ts:70</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>uids: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="removemember" class="tsd-anchor"></a>
					<h3>remove<wbr>Member</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">remove<wbr>Member&lt;T&gt;<span class="tsd-signature-symbol">(</span>uids<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/chat/api.ts:32</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>uids: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="sendmsg" class="tsd-anchor"></a>
					<h3>send<wbr>Msg</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">send<wbr>Msg&lt;T&gt;<span class="tsd-signature-symbol">(</span>type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, msg<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/chat/api.ts:39</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>type: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5>msg: <span class="tsd-signature-type">T</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="startchat" class="tsd-anchor"></a>
					<h3>start<wbr>Chat</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">start<wbr>Chat&lt;T&gt;<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/chat/api.ts:47</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
					<a name="userexitchat" class="tsd-anchor"></a>
					<h3>user<wbr>Exit<wbr>Chat</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
						<li class="tsd-signature tsd-kind-icon">user<wbr>Exit<wbr>Chat&lt;T&gt;<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<ul>
									<li>Defined in src/model/chat/api.ts:77</li>
								</ul>
							</aside>
							<h4 class="tsd-type-parameters-title">Type parameters</h4>
							<ul class="tsd-type-parameters">
								<li>
									<h4>T</h4>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">T</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="globals  ">
						<a href="../globals.html"><em>Globals</em></a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-class">
						<a href="chatapi.html" class="tsd-kind-icon">Chat<wbr>Api</a>
						<ul>
							<li class=" tsd-kind-constructor tsd-parent-kind-class">
								<a href="chatapi.html#constructor" class="tsd-kind-icon">constructor</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="chatapi.html#id" class="tsd-kind-icon">id</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="chatapi.html#model_name" class="tsd-kind-icon">model_<wbr>name</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-class tsd-is-private">
								<a href="chatapi.html#orgid" class="tsd-kind-icon">orgID</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="chatapi.html#addcs" class="tsd-kind-icon">add<wbr>Cs</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="chatapi.html#addmember" class="tsd-kind-icon">add<wbr>Member</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-is-private">
								<a href="chatapi.html#buildheader" class="tsd-kind-icon">build<wbr>Header</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class">
								<a href="chatapi.html#createchat" class="tsd-kind-icon">create<wbr>Chat</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="chatapi.html#csexitchat" class="tsd-kind-icon">cs<wbr>Exit<wbr>Chat</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="chatapi.html#finishchat" class="tsd-kind-icon">finish<wbr>Chat</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="chatapi.html#removecs" class="tsd-kind-icon">remove<wbr>Cs</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="chatapi.html#removemember" class="tsd-kind-icon">remove<wbr>Member</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="chatapi.html#sendmsg" class="tsd-kind-icon">send<wbr>Msg</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="chatapi.html#startchat" class="tsd-kind-icon">start<wbr>Chat</a>
							</li>
							<li class=" tsd-kind-method tsd-parent-kind-class tsd-has-type-parameter">
								<a href="chatapi.html#userexitchat" class="tsd-kind-icon">user<wbr>Exit<wbr>Chat</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer class="with-border-bottom">
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li>
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="container tsd-generator">
	<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p>
</div>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
</body>
</html>