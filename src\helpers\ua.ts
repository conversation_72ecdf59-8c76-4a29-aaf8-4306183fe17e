import includes from "lodash/includes"

export enum UserAgentType {
    None = 0,
    /**
     * 安卓平板设备
     */
    Tablet = 1 << 1,

    DesktopOthers = 1 << 2,
    DesktopSafari = 1 << 3,
    DesktopChrome = 1 << 4,
    DesktopFirefox = 1 << 5,
    DesktopIE = 1 << 6,
    DesktopEdge = 1 << 7,

    Desktop = DesktopOthers |
        DesktopSafari |
        DesktopChrome |
        DesktopFirefox |
        DesktopIE |
        DesktopEdge,

    IPhone = 1 << 8,
    IPad = 1 << 9,
    /**
     * 安卓手机设备
     */
    Andriod = 1 << 10,

    /**
     * 移动设备（不含包平板设备）
     */
    MobilePhone = IPhone | Andriod,

    /**
     * 所有移动设备，包含手机+平板
     */
    Mobile = MobilePhone | Tablet | IPhone,
}

export class UserAgentHelper {
    private static contains(source: string, match: string) {
        return source.indexOf(match) > -1
    }

    private static containsAny(source: string, matches: string[]) {
        for (const item of matches) {
            if (UserAgentHelper.contains(source, item)) {
                return true
            }
        }
        return false
    }

    public static getType(ua: string) {
        const lower = ua.toLowerCase()

        if (UserAgentHelper.contains(lower, "ipad")) {
            return UserAgentType.IPad
        }

        if (UserAgentHelper.contains(lower, "android_tablet")) {
            return UserAgentType.Tablet
        }

        if (UserAgentHelper.contains(lower, "iphone")) {
            return UserAgentType.IPhone
        }

        if (UserAgentHelper.contains(lower, "ipod")) {
            return UserAgentType.IPhone
        }

        if (UserAgentHelper.containsAny(lower, ["android", "mobile"])) {
            return UserAgentType.Andriod
        }

        if (UserAgentHelper.contains(lower, "safari")) {
            return UserAgentType.DesktopSafari
        }

        if (UserAgentHelper.containsAny(lower, ["edge", "edg"])) {
            return UserAgentType.DesktopEdge
        }

        if (UserAgentHelper.contains(lower, "trident")) {
            return UserAgentType.DesktopIE
        }

        if (UserAgentHelper.contains(lower, "firefox")) {
            return UserAgentType.DesktopFirefox
        }

        if (UserAgentHelper.contains(lower, "chrome")) {
            return UserAgentType.DesktopChrome
        }

        if (UserAgentHelper.containsAny(lower, ["360", "baidu", "qq"])) {
            return UserAgentType.DesktopOthers
        }

        return UserAgentType.None
    }

    public static isMobile(ua: string) {
        const type = UserAgentHelper.getType(ua)
        return type === UserAgentType.Andriod || type === UserAgentType.IPhone
    }

    public static isIe() {
        const ua = window.navigator.userAgent.toLowerCase()
        return includes(ua, "trident")
    }
}
